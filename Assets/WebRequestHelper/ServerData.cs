using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace Amanotes.TripleSDK.Core
{
    [Serializable]
    public class ErrorResponseData
    {
        public string error;
        public string message;
        public string statusCode;
    }

    [Serializable]
    public class ResponseData
    {
        public object data;
        public ErrorResponseData error;
    }

    [Serializable]
    public class LocalData
    {
        public string fbName;        
    }

    public static class ObjectExtensions
    {
        public static T ToObject<T>(this Dictionary<string, object> source) where T : class, new()
        {
            var someObject = new T();
            var someObjectType = someObject.GetType();

            foreach (var item in source)
            {
                someObjectType
                         .GetField(item.Key)
                         .SetValue(someObject, item.Value);
            }

            return someObject;
        }

        public static Dictionary<string, object> AsDictionary(this object source, BindingFlags bindingAttr = BindingFlags.DeclaredOnly | BindingFlags.Public | BindingFlags.Instance)
        {
            return source.GetType().GetFields(bindingAttr).ToDictionary
            (
                propInfo => propInfo.Name,
                propInfo => propInfo.GetValue(source)
            );

        }
    }
}