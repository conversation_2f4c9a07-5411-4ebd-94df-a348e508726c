using System;
using System.Collections.Generic;
using Music.ACM;
using Music.ACM.Interfaces;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

public class ApprovalProcessContentDisplayRadio : ApprovalProcessContentDisplay
{
    [Header("UI")]
    [SerializeField] private ToggleGroup _toggleGroupAudio = null;
    [SerializeField] private ToggleGroup _toggleGroupLevelDesign = null;
    [SerializeField] private ApprovalProcessToggleContent _toggleContent = null;
    [SerializeField] private Button[] _btnContentTypes = null;
    [SerializeField] private GameObject[] _objectContentTypes = null;

    private void Awake()
    {
        for (int i = 0; i < _btnContentTypes.Length; i++)
        {
            var index = i;
            _btnContentTypes[i].onClick.AddListener(() => OnContentTypeButtonClicked(index));
        }
        OnContentTypeButtonClicked(0);
    }

    public override void Init(ApprovalProcessSongState songState, List<Content> audioContents, List<Content> levelDesignContents, bool isNewSong = false)
    {
        base.Init(songState, audioContents, levelDesignContents, isNewSong);
        DestroyChildren(_toggleGroupAudio.gameObject);
        DestroyChildren(_toggleGroupLevelDesign.gameObject);

        int currentAudioSlotOrder = songState.audioSlotOrder;
        for(int i = 0 ; i < AudioContents.Count; i++)
        {
            Content content = (Content)AudioContents[i];
            ApprovalProcessToggleContent toggleContent = Instantiate(_toggleContent, _toggleGroupAudio.transform);
            toggleContent.Init(_toggleGroupAudio, i, GetContentName(content));
            toggleContent.OnToggleValueChanged += OnAudioToggleSelected;
            if (IsNewSong)
            {
                currentSongState.audioSlotOrder = i;
                toggleContent.Active(IsActiveContent(content));
            }
            else
            {
                toggleContent.Active(i == currentAudioSlotOrder);
            }
        }

        int currentLevelDesignSlotOrder = songState.levelDesignSlotOrder;
        for(int i = 0 ; i < LevelDesignContents.Count; i++)
        {
            Content content = (Content)LevelDesignContents[i];
            ApprovalProcessToggleContent toggleContent = Instantiate(_toggleContent, _toggleGroupLevelDesign.transform);
            toggleContent.Init(_toggleGroupLevelDesign, i, GetContentName(content));
            toggleContent.OnToggleValueChanged += OnLevelDesignToggleSelected;
            if (IsNewSong)
            {
                currentSongState.levelDesignSlotOrder = i;
                toggleContent.Active(IsActiveContent(content));
            }
            else
            {
                toggleContent.Active(i == currentLevelDesignSlotOrder);
            }
        }

        OnContentTypeButtonClicked(0);
    }

    private void OnAudioToggleSelected(int order)
    {
        currentSongState.audioSlotOrder = order;
    }

    private void OnLevelDesignToggleSelected(int order)
    {
        currentSongState.levelDesignSlotOrder = order;
    }

    private void OnContentTypeButtonClicked(int id)
    {
        for (int i = 0; i < _objectContentTypes.Length; i++)
        {
            if (i != id)
            {
                _objectContentTypes[i].gameObject.SetActive(false);
                _btnContentTypes[i].image.color = new Color32(255, 255, 255, 25);
            }
        }
        _btnContentTypes[id].image.color = new Color32(255, 255, 255, 50);
        _objectContentTypes[id].gameObject.SetActive(true);
    }

    private void DestroyChildren(GameObject go)
    {
        foreach (Transform trans in go.transform)
        {
            Destroy(trans.gameObject);
        }
    }
}