using System;

namespace TileHop.MysteryBox {
    [Serializable]
    public class MysteryV2Config {
        public int song_start; // thời điểm xuất hiện
        public int reward_ads_required; // Số lượng RW phải xem để nhận quà
        public int reward_ads_capacity; //max reward bằng xem ad trong 1 lần countdown (sau khi nhận được free)
        public int interval_hour; //Config thời gian countdown (tính từ thời điểm nhận lần free cuối) tính theo giờ

        public MysteryV2Config() {
            song_start = 3;
            reward_ads_required = 2;
            reward_ads_capacity = 2;
            interval_hour = 5;
        }
    }
}