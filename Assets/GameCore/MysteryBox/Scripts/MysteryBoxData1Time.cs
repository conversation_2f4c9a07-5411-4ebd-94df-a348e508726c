using System;
using UnityEngine;

namespace TileHop.MysteryBox {
    [Serializable]
    public class MysteryBoxData1Time : MysteryBoxData {
        public override string KEY_SAVE_DATA => MysteryBoxKey.KEY_DATA_1TIME;

        public MysteryBoxData1Time() {
            receivedFirstGift = false;
            enableOfferAds = true;
            countRefuse = 0;
            countGift = 0;
        }

        public override MysteryBoxData LoadData() {
            if (PlayerPrefs.HasKey(KEY_SAVE_DATA)) {
                string data = PlayerPrefs.GetString(KEY_SAVE_DATA);
                try {
                    var mysteryBoxData = JsonUtility.FromJson<MysteryBoxData1Time>(data);
                    return mysteryBoxData;
                }
                catch (Exception e) {
                    return new MysteryBoxData1Time();
                }
            } else {
                return new MysteryBoxData1Time();
            }
        }

        public override MysteryBoxType GetCurrentState() {
            if (receivedFirstGift) {
                return MysteryBoxType.OneTimeFreeGift_Ads;
            } else {
                return MysteryBoxType.OneTimeFreeGift_Free;
            }
        }

        public override MysteryBoxReward GetCurrentReward() {
            if (!receivedFirstGift) {
                return GetRewardSong();
            } else {
                switch (countGift % 3) {
                    case 1: return GetRewardSong();
                    case 2: return GetRewardBall();
                    case 0: return GetRewardGem();
                    default:
                        return GetRewardGem();
                }
            }
        }

        public override TimeSpan GetRemainTime() {
            return TimeSpan.MaxValue;
        }

        public override DateTime GetNextFreeGiftTime() {
            return DateTime.MaxValue;
        }

        public override bool OnWatchVideoComplete() {
            return true;
        }

        public override string GetAdsProgress() {
            return string.Empty;
        }
    }
}