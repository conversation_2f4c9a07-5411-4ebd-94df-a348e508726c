using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering;

public class AnzuLayerSetter : MonoBehaviour
{
   private void Start() {
      if (ThemeManager.instance.CurrentThemeId == ThemeManager.ThemeBeliever || ThemeManager.instance.CurrentThemeId == ThemeManager.ThemeRadioStroke) {
         var sortingGroup = gameObject.AddComponent<SortingGroup>();
         sortingGroup.sortingLayerName = "Front";
         sortingGroup.sortingOrder = 20;
      }
   }
}
