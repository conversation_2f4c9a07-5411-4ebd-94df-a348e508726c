using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class RateUs : PopupUI {
    private const string FeedbackGoogleDocs =
        "https://docs.google.com/forms/d/e/1FAIpQLSc-vVVbaZRN90Gx5LcjNtL-noVLs-6HNa9X_q6nBU11u7Xolg/formResponse";

    public static   bool IsShowing   = false;
    public static readonly byte  OFF_FOREVER = 99;

    public static GameObject instancedPopup;
    
    private void Awake() {
        IsShowing = true;
    }

    protected override void OnEnable() {
        AnalyticHelper.instance.SetCurrentPopup(LOCATION_NAME.RATE_US);
        base.OnEnable();
    }
    private void Start() {
        TrackingPopup();
    }

    private void TrackingPopup() {
        int countEventMeStart = AnalyticHelper.CountEvent(SONG_STATUS.me_start.ToString());
        int countEventSongEnd = AnalyticHelper.CountEvent(SONG_STATUS.song_end.ToString());
        var param = new Dictionary<string, object> {
            { "screen_name", "RATE_US" },
            { "screen_class", "home" },
            { "me_current", countEventMeStart },
            { "song_end_current", countEventSongEnd }
        };
        AnalyticHelper.LogEvent(TRACK_NAME.popup_rating_impression, param);
    }

    public void DontAsk() {
        AnalyticHelper.Button_Click(BUTTON_NAME.Rate_DontAsk);
        AnalyticHelper.LogEvent(TRACK_NAME.rating_click_rate_dontask);
        PlayerPrefs.SetInt(CONFIG_STRING.RateLater, OFF_FOREVER);
        Close();
    }

    public void Rate() {
        AnalyticHelper.Button_Click(BUTTON_NAME.Rate);
        AnalyticHelper.LogEvent(TRACK_NAME.rating_click_rate);
        Util.RateUs();
        Close();
    }

    public override void Close() {
        if (AnalyticHelper.instance) {
            AnalyticHelper.instance.SetPopupClose();
        }

        base.Close();

        IsShowing = false;
    }

    public void RateLater() {
        PlayerPrefs.SetInt(CONFIG_STRING.RateLater, PlayerPrefs.GetInt(CONFIG_STRING.RateLater, 0) + 1);
        AnalyticHelper.Button_Click(BUTTON_NAME.Rate_Later); // đời cũ
        AnalyticHelper.LogEvent(TRACK_NAME.rating_click_rate_later);
        Close();
    }

    public static void CheckAndOpenRate() {
        int rateData = PlayerPrefs.GetInt(CONFIG_STRING.RateLater, 0);
        bool firstShow =  rateData== 0;
        bool nextShow = rateData == 1;

        RatePopupConfig rateConfig = Configuration.GetRateConfig();

        bool isShowRate = false;
        if (rateConfig == null) {
            //old config
            bool conditionPlayCount = PlayerPrefs.GetInt(CONFIG_STRING.PlayCount, 0) >=
                                      (RemoteConfig.instance.RateAtPlayCount + (nextShow ? 5 : 0));
            isShowRate = (firstShow || nextShow) && conditionPlayCount && IsValidScoreToRate(firstShow);
        } else {
            if (firstShow) {
                //check first show
                bool conditionPlayCount = PlayerPrefs.GetInt(CONFIG_STRING.PlayCount, 0) >=
                                          RemoteConfig.instance.RateAtPlayCount;
                int countEventMeStart = AnalyticHelper.CountEvent(SONG_STATUS.me_start.ToString());
                int countEventSongEnd = AnalyticHelper.CountEvent(SONG_STATUS.song_end.ToString());
                isShowRate = rateConfig.CheckFirstShow(countEventMeStart, countEventSongEnd) && conditionPlayCount;
            } else if (nextShow) {
                //check remind show
                int countEventSongEnd = AnalyticHelper.CountEvent(SONG_STATUS.song_end.ToString());
                isShowRate = countEventSongEnd >= rateConfig.remind.songend && IsValidScoreToRate(false);
            }
        }

        if (isShowRate) {
            ShowReviewCustom(); //TH-2564 priority custom review
        }
    }

    private static bool IsValidScoreToRate(bool firstShow) {
        int requiredScore = firstShow ? RemoteConfig.instance.RateAtScore : RemoteConfig.instance.RateAtScore + 100; 
        foreach (Song song in SongManager.instance.songs.Values) {
            if (song.savedType == SONGTYPE.OPEN) {
                int best = Configuration.GetBestScore(song.path);
                if (best> requiredScore) {
                    return true;
                }
            }
        }

        return false;
    }

    public static void ShowReviewCustom() {
        string path = RemoteConfig.instance.RatePopup_StyleIndex switch {
            0 => PopupName.RateUs,
            1 => PopupName.RateUs2,
            _ => PopupName.RateUs
        };

        instancedPopup = Util.ShowPopUp(path);
    }

    public static IEnumerator IEObtainSheetData(string star, string feedback) {
        WWWForm form = new WWWForm();
        form.AddField("entry.1397495168", Application.version);
        form.AddField("entry.1398335131", Configuration.instance.CountryName);
        form.AddField("entry.125906237", star);
        form.AddField("entry.1860761668", feedback);
        byte[] rawData = form.data;
        WWW www = new WWW(FeedbackGoogleDocs, rawData);
        yield return www;
        Logger.EditorLog("RatePopup", "Rateus send google Done " + www.error);
        www.Dispose();
    }
}