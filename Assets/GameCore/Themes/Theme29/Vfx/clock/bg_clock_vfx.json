{"skeleton": {"hash": "F+yakvHVexQ", "spine": "4.0.64", "x": -305.53, "y": 442.84, "width": 848.62, "height": 656.51, "images": "", "audio": ""}, "bones": [{"name": "root"}, {"name": "all", "parent": "root", "y": 799.5}, {"name": "bone", "parent": "all"}, {"name": "bone2", "parent": "all"}, {"name": "bone3", "parent": "all", "rotation": 28.77}, {"name": "bone4", "parent": "all", "y": -538.59}, {"name": "vfx", "parent": "all", "y": -415.71, "color": "ff0000ff"}, {"name": "line_all", "parent": "vfx", "x": 598.85, "y": 202.46, "color": "0dff00ff"}, {"name": "bone6", "parent": "line_all", "rotation": -16.69, "x": 201.14, "y": -88.98}, {"name": "bone5", "parent": "bone6", "scaleX": 2.3765, "scaleY": 1.3836}, {"name": "Mask", "parent": "all", "y": -612.84}, {"name": "bone8", "parent": "line_all", "rotation": -16.69, "x": 101.49, "y": -134.88}, {"name": "bone9", "parent": "bone8", "scaleX": 2.3765, "scaleY": 1.3836}, {"name": "bone10", "parent": "line_all", "rotation": -16.69, "x": 128.36, "y": 67.78}, {"name": "bone11", "parent": "bone10", "scaleX": 2.3765, "scaleY": 1.3836}, {"name": "bone12", "parent": "line_all", "rotation": -16.69, "x": 85.81, "y": 215.58}, {"name": "bone13", "parent": "bone12", "scaleX": 3.912, "scaleY": 1.2146}, {"name": "bone14", "parent": "line_all", "rotation": -16.69, "x": 207.04, "y": -44.42}, {"name": "bone15", "parent": "bone14", "scaleX": 3.2219, "scaleY": 1.297}, {"name": "bone16", "parent": "line_all", "rotation": -16.69, "x": 85.96, "y": -183.2}, {"name": "bone17", "parent": "bone16", "scaleX": 2.6923, "scaleY": 0.5786}, {"name": "bone18", "parent": "line_all", "rotation": -16.69, "x": 211.14, "y": 282.8}, {"name": "bone19", "parent": "bone18", "scaleX": 2.3765, "scaleY": 0.8063}, {"name": "bone20", "parent": "line_all", "rotation": -16.69, "x": 307.34, "y": 225.75, "scaleY": 0.5082}, {"name": "bone21", "parent": "bone20", "scaleX": 2.3765, "scaleY": 0.8063}], "slots": [{"name": "BG", "bone": "root"}, {"name": "Mask", "bone": "Mask", "attachment": "Mask"}, {"name": "line3", "bone": "bone9", "attachment": "line", "blend": "additive"}, {"name": "line4", "bone": "bone11", "attachment": "line", "blend": "additive"}, {"name": "line5", "bone": "bone13", "attachment": "line", "blend": "additive"}, {"name": "line8", "bone": "bone19", "attachment": "line", "blend": "additive"}, {"name": "line9", "bone": "bone21", "attachment": "line", "blend": "additive"}, {"name": "line6", "bone": "bone15", "attachment": "line", "blend": "additive"}, {"name": "line7", "bone": "bone17", "attachment": "line", "blend": "additive"}, {"name": "line2", "bone": "bone5", "attachment": "line", "blend": "additive"}, {"name": "Asset 1", "bone": "all", "attachment": "Asset 1"}, {"name": "Asset 27", "bone": "bone", "attachment": "Asset 27"}, {"name": "Asset 28", "bone": "bone2", "attachment": "Asset 28"}, {"name": "Asset 29", "bone": "bone3", "attachment": "Asset 29"}], "skins": [{"name": "default", "attachments": {"Asset 1": {"Asset 1": {"x": -1.53, "y": -3.66, "width": 608, "height": 607}}, "Asset 27": {"Asset 27": {"x": 39.79, "y": 43.63, "width": 65, "height": 72}}, "Asset 28": {"Asset 28": {"x": -71.67, "y": -50.01, "width": 124, "height": 86}}, "Asset 29": {"Asset 29": {"x": 26.32, "y": 41.94, "width": 43, "height": 69}}, "line2": {"line": {"width": 197, "height": 9}}, "line3": {"line": {"width": 197, "height": 9}}, "line4": {"line": {"width": 197, "height": 9}}, "line5": {"line": {"width": 197, "height": 9}}, "line6": {"line": {"width": 197, "height": 9}}, "line7": {"line": {"width": 197, "height": 9}}, "line8": {"line": {"width": 197, "height": 9}}, "line9": {"line": {"width": 197, "height": 9}}, "Mask": {"Mask": {"type": "clipping", "end": "line2", "vertexCount": 4, "vertices": [-536.96, 140.21, 543.1, 149.48, 543.01, 879.72, -537.03, 879.29], "color": "ce3a3aff"}}}}], "animations": {"clock_idle": {"slots": {"line2": {"rgba": [{"color": "ffffff00"}, {"time": 0.1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.8333, "color": "ffffffff"}, {"time": 1.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00"}, {"time": 2.1, "color": "ffffffff", "curve": "stepped"}, {"time": 3.8333, "color": "ffffffff"}, {"time": 3.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 4, "color": "ffffff00"}, {"time": 4.1, "color": "ffffffff", "curve": "stepped"}, {"time": 5.8333, "color": "ffffffff"}, {"time": 5.9333, "color": "ffffff00"}]}, "line3": {"rgba": [{"time": 0.4333, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5667, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4333, "color": "ffffffff"}, {"time": 2.5, "color": "ffffff00", "curve": "stepped"}, {"time": 2.5667, "color": "ffffff00"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 4.4333, "color": "ffffffff"}, {"time": 4.5, "color": "ffffff00", "curve": "stepped"}, {"time": 4.5667, "color": "ffffff00"}, {"time": 4.6667, "color": "ffffffff"}]}, "line4": {"rgba": [{"time": 1.3333, "color": "ffffffff"}, {"time": 1.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00"}, {"time": 1.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}, {"time": 3.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 3.5, "color": "ffffff00"}, {"time": 3.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 5.3333, "color": "ffffffff"}, {"time": 5.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 5.5, "color": "ffffff00"}, {"time": 5.5667, "color": "ffffffff"}]}, "line5": {"rgba": [{"time": 1.0333, "color": "ffffffff"}, {"time": 1.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2, "color": "ffffff00"}, {"time": 1.3, "color": "ffffffff", "curve": "stepped"}, {"time": 3.0333, "color": "ffffffff"}, {"time": 3.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 3.2, "color": "ffffff00"}, {"time": 3.3, "color": "ffffffff", "curve": "stepped"}, {"time": 5.0333, "color": "ffffffff"}, {"time": 5.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 5.2, "color": "ffffff00"}, {"time": 5.3, "color": "ffffffff"}]}, "line6": {"rgba": [{"time": 0.4667, "color": "ffffffff"}, {"time": 0.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6, "color": "ffffff00"}, {"time": 0.7, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.6, "color": "ffffff00"}, {"time": 2.7, "color": "ffffffff", "curve": "stepped"}, {"time": 4.4667, "color": "ffffffff"}, {"time": 4.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 4.6, "color": "ffffff00"}, {"time": 4.7, "color": "ffffffff"}]}, "line7": {"rgba": [{"time": 1, "color": "ffffffff"}, {"time": 1.1, "color": "ffffff00", "curve": "stepped"}, {"time": 1.1667, "color": "ffffff00"}, {"time": 1.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 3, "color": "ffffffff"}, {"time": 3.1, "color": "ffffff00", "curve": "stepped"}, {"time": 3.1667, "color": "ffffff00"}, {"time": 3.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 5, "color": "ffffffff"}, {"time": 5.1, "color": "ffffff00", "curve": "stepped"}, {"time": 5.1667, "color": "ffffff00"}, {"time": 5.2667, "color": "ffffffff"}]}, "line8": {"rgba": [{"time": 0.5, "color": "ffffffff"}, {"time": 0.6, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.5, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00", "curve": "stepped"}, {"time": 2.6667, "color": "ffffff00"}, {"time": 2.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 4.5, "color": "ffffffff"}, {"time": 4.6, "color": "ffffff00", "curve": "stepped"}, {"time": 4.6667, "color": "ffffff00"}, {"time": 4.7667, "color": "ffffffff"}]}, "line9": {"rgba": [{"color": "ffffff00"}, {"time": 0.1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.8333, "color": "ffffffff"}, {"time": 1.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00"}, {"time": 2.1, "color": "ffffffff", "curve": "stepped"}, {"time": 3.8333, "color": "ffffffff"}, {"time": 3.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 4, "color": "ffffff00"}, {"time": 4.1, "color": "ffffffff", "curve": "stepped"}, {"time": 5.8333, "color": "ffffffff"}, {"time": 5.9333, "color": "ffffff00"}]}}, "bones": {"bone": {"rotate": [{}, {"time": 6, "value": -360}]}, "bone2": {"rotate": [{}, {"time": 6, "value": -720}]}, "bone3": {"rotate": [{"value": -28.77}, {"time": 6, "value": -1108.77}]}, "bone5": {"translate": [{}, {"time": 1.9333, "x": -1564.76}, {"time": 2}, {"time": 3.9333, "x": -1564.76}, {"time": 4}, {"time": 5.9333, "x": -1564.76}, {"time": 6}]}, "bone13": {"translate": [{"x": -651.98}, {"time": 1.1333, "x": -1564.76}, {"time": 1.2}, {"time": 2, "x": -651.98}, {"time": 3.1333, "x": -1564.76}, {"time": 3.2}, {"time": 4, "x": -651.98}, {"time": 5.1333, "x": -1564.76}, {"time": 5.2}, {"time": 6, "x": -651.98}]}, "bone15": {"translate": [{"x": -1121.41}, {"time": 0.5333, "x": -1564.76}, {"time": 0.6}, {"time": 2, "x": -1121.41}, {"time": 2.5333, "x": -1564.76}, {"time": 2.6}, {"time": 4, "x": -1121.41}, {"time": 4.5333, "x": -1564.76}, {"time": 4.6}, {"time": 6, "x": -1121.41}]}, "bone17": {"translate": [{"x": -678.06}, {"time": 1.1, "x": -1564.76}, {"time": 1.1667}, {"time": 2, "x": -678.06}, {"time": 3.1, "x": -1564.76}, {"time": 3.1667}, {"time": 4, "x": -678.06}, {"time": 5.1, "x": -1564.76}, {"time": 5.1667}, {"time": 6, "x": -678.06}]}, "bone19": {"translate": [{"x": -1069.25}, {"time": 0.6, "x": -1564.76}, {"time": 0.6667}, {"time": 2, "x": -1069.25}, {"time": 2.6, "x": -1564.76}, {"time": 2.6667}, {"time": 4, "x": -1069.25}, {"time": 4.6, "x": -1564.76}, {"time": 4.6667}, {"time": 6, "x": -1069.25}]}, "bone9": {"translate": [{"x": -1147.49}, {"time": 0.5, "x": -1564.76}, {"time": 0.5667}, {"time": 2, "x": -1147.49}, {"time": 2.5, "x": -1564.76}, {"time": 2.5667}, {"time": 4, "x": -1147.49}, {"time": 4.5, "x": -1564.76}, {"time": 4.5667}, {"time": 6, "x": -1147.49}]}, "bone11": {"translate": [{"x": -417.27}, {"time": 1.4333, "x": -1564.76}, {"time": 1.5}, {"time": 2, "x": -417.27}, {"time": 3.4333, "x": -1564.76}, {"time": 3.5}, {"time": 4, "x": -417.27}, {"time": 5.4333, "x": -1564.76}, {"time": 5.5}, {"time": 6, "x": -417.27}]}, "bone21": {"translate": [{}, {"time": 1.9333, "x": -1564.76}, {"time": 2}, {"time": 3.9333, "x": -1564.76}, {"time": 4}, {"time": 5.9333, "x": -1564.76}, {"time": 6}]}}}}}