using System.Collections;
using UnityEngine;
using UnityEngine.UI;

namespace TileHop.LiveEvent.GalaxyQuest {
	public class GalaxyQuestEntryPoint : MonoBehaviour {
		[SerializeField] private GameObject root;
		[SerializeField] private Button btnEntryPoint;
		[SerializeField] private Text textCountDown;
		[SerializeField] private GameObject objStart;
		[SerializeField] private GameObject objJoining;
		[SerializeField] private GameObject objCountdown;

		private IEnumerator Start() {
			GalaxyQuest.GetEventData();
			GalaxyQuest.GetStateData();

			while (!GalaxyQuestTimeHelper.instanceSafe.IsRequestDone) {
				yield return null;
			}

			bool canActive = GalaxyQuestTimeHelper.instanceSafe.RealTime >= GalaxyQuest.config.time_start;

			if (!canActive) {
				gameObject.SetActive(false);
				yield break;
			}

			bool hasActived = GalaxyQuest.isActive;
			if (!hasActived && !CheckUnlockConditions()) {
				gameObject.SetActive(false);
				yield break;
			}

			if (!hasActived) {
				GalaxyQuest.ActiveEvent();
				GalaxyQuest.SetAutoShowPopup(PopupType.Start);
				GalaxyQuest.instanceSafe.AutoShowPopup();
			}

			UpdateUI();

			btnEntryPoint.onClick.RemoveAllListeners();
			btnEntryPoint.onClick.AddListener(OnClickButton);
		}

		private void OnEnable() {
			StartCoroutine(Start());
		}

		private bool CheckUnlockConditions() {
			GalaxyQuestConfig config = GalaxyQuest.config;
			bool isSongStartEnough = GalaxyQuest.GetSongStartQuantity() >= config.unlock_conditions.song_start;
			bool isHasStarEnough = GalaxyQuest.GetTotalStar() >= config.unlock_conditions.stars_earn;
			bool isPlayedTimeEnough = Time.realtimeSinceStartup >= config.unlock_conditions.time_session;

			return isSongStartEnough && isHasStarEnough && isPlayedTimeEnough;
		}

		private void UpdateUI() {
			StopAllCoroutines();

			root.gameObject.SetActive(true);

			StartCoroutine(IECountDownEvent());
		}

		private IEnumerator IECountDownEvent() {
			objCountdown.SetActive(false);

			var delay = GalaxyQuestHelper.GetWaitForSeconds(1f);
			int timeLeft = GalaxyQuest.GetEndTime() - GalaxyQuestTimeHelper.instanceSafe.RealTime;
			
			while (timeLeft > 0) {
				bool isJoining = GalaxyQuest.isJoiningEvent;
				objStart.SetActive(!isJoining);
				objJoining.SetActive(isJoining);
				
				timeLeft = GalaxyQuest.GetEndTime() - GalaxyQuestTimeHelper.instanceSafe.RealTime;
				GalaxyQuestTimeHelper.FormatTimeHMSToDisplay(timeLeft, textCountDown, timeLeft);
				yield return delay;
			}
			
			if (GalaxyQuest.isOpening) {
				//event tracking
				var stateData = GalaxyQuest.GetStateData();
				if (stateData.triggerTime < 0) {
					// user chưa click Start
					GalaxyQuestAnalysis.EmitQuestExpired();
				} else {
					GalaxyQuestAnalysis.EmitQuestEnd(GalaxyQuest.timeElapsedSinceOfferEvent, QuestEndReason.TIME_OUT);
				}
				
				GalaxyQuest.EndEvent(true);
				GalaxyQuest.SetAutoShowPopup(PopupType.CountDown);
				GalaxyQuest.instanceSafe.AutoShowPopup();
			}
			
			StartCoroutine(IECountDownEnd());
		}

		private IEnumerator IECountDownEnd() {
			objStart.SetActive(false);
			objJoining.SetActive(false);
			objCountdown.SetActive(true);
			var delay = GalaxyQuestHelper.GetWaitForSeconds(1f);
			int timeLeft = GalaxyQuest.reopenTime - GalaxyQuestTimeHelper.instanceSafe.RealTime;
			while (timeLeft > 0) {
				{
					timeLeft = GalaxyQuest.reopenTime - GalaxyQuestTimeHelper.instanceSafe.RealTime;
					GalaxyQuestTimeHelper.FormatTimeHMSToDisplay(timeLeft, textCountDown, timeLeft);
					yield return delay;
				}
			}

			GalaxyQuest.ActiveEvent();
			GalaxyQuest.SetAutoShowPopup(PopupType.Start);
			GalaxyQuest.instanceSafe.AutoShowPopup();
			UpdateUI();
		}

		private void OnClickButton() {
			if (!GalaxyQuest.isInstanced) {
				return;
			}
			
			if(GalaxyQuest.reopenTime > GalaxyQuestTimeHelper.instanceSafe.RealTime) {
				GalaxyQuest.instanceSafe.ShowCountDownPopup();
				return;
			}

			if (GalaxyQuest.isJoiningEvent) {
				GalaxyQuest.instanceSafe.ShowMainPopupWithoutJump();
			} else {
				GalaxyQuest.instanceSafe.ShowStartPopup();
			}
		}
	}
}
