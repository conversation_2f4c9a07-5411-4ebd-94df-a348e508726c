using System;
using UnityEngine;
using UnityEngine.UI;

namespace TileHop.LiveEvent.GalaxyQuest {
	public class GalaxyQuestStartPopup : PopupUI {
		[SerializeField] private Button _btnClose;
		[SerializeField] private Button _btnStart;
		[SerializeField] private Text textStart;
		[SerializeField] private Text txtGemReward;

		private void Start() {
			_btnClose.onClick.AddListener(OnClickClose);
			_btnStart.onClick.AddListener(OnClickStart);

			textStart.text = string.Format(GalaxyQuest.GetLocalizationValue("QG_START"), GalaxyQuest.GetStateData().isLongQuest ? 7 : 5);
			txtGemReward.text = GalaxyQuest.GetFinalRewardGemAmount().ToString();
		}

		protected override void OnEnable() {
			base.OnEnable();

			_btnClose.gameObject.SetActive(GalaxyQuest.eventOrderNumber > 1);
		}

		private void OnClickClose() {
			Close();
		}

		private void OnClickStart() {
			GalaxyQuest.instanceSafe.Join();
			GalaxyQuest.InitStateData();
			
			if(GalaxyQuest.eventOrderNumber > 1) {
				GalaxyQuestMatching.ShowPopup();
			} else {
				var onboarding = GalaxyQuestHelper.ShowPopup("GalaxyQuestOnboarding");
				if (onboarding != null && onboarding.TryGetComponent(out UIOnBoardingUnlockElement onboardingPopup)) {
					onboardingPopup.Show(GalaxyQuestMatching.ShowPopup);
				} else {
					GalaxyQuestMatching.ShowPopup();
				}
			}
			
			GalaxyQuestAnalysis.EmitQuestStarted(GalaxyQuest.timeElapsedSinceOfferEvent);
			gameObject.SetActive(false);
		}

		public override void Show(Action callback = null) {
			gameObject.SetActive(true);
			GalaxyQuestAnalysis.EmitQuestOffered();
		}

		public override bool HandleEventBack() {
			return _btnClose.gameObject.activeSelf && base.HandleEventBack();
		}
	}
}