using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace TileHop.LiveEvent {
    public class LiveEventManager : Singleton<LiveEventManager> {
        private bool _isEnableLiveEvent;
        private bool _isOnlyShowIntroduction; // với các user day 1, sẽ chưa được join event
        private bool _isPassSongCount; // kiểm tra đã pass songcount k

        public        bool IsGoEventTab;
        public static int  IdCurrentEvent = int.MinValue;

        private int      _minSongStart;
        private DateTime _openLiveEventDate;
        private bool     _isInit = false;

        private Dictionary<int, LiveEventItem> _dictEventItems;

        private const string KEY_ID_LIVEEVENT           = "LiveEvent_IdActiveEvent";
        public const  string KEY_PROGRESS_DATA          = "LiveEvent_ProgressData_";
        public const  string KEY_FIRST_ACTIVE_LIVEEVENT = "LiveEvent_First_Active_";
        public const  string KEY_REPLACE_DATA           = "LiveEvent_ReplaceData_";

        public bool IsInit => _isInit;
        public bool IsActiveEvent => _isEnableLiveEvent && _isPassSongCount;
        public bool IsShowIntro => IsActiveEvent && _isOnlyShowIntroduction;

        public bool CanEarnToken {
            get {
                if (!IsActiveEvent) {
                    return false; // đang k bật liveEvent
                }

                if (_isOnlyShowIntroduction) {
                    return false; // đang show intro
                }

                if (IdCurrentEvent == int.MinValue) {
                    return false; // đang không có event nào hoạt động
                }

                var eventItem = GetLiveEvent(IdCurrentEvent);
                if (eventItem != null) {
                    if (!eventItem.CanEarnToken())
                        return false;
                }

                return true;
            }
        }

        public bool CanShowTokenAtGameComplete {
            get {
                if (!IsActiveEvent) {
                    return false; // đang k bật liveEvent
                }

                if (_isOnlyShowIntroduction) {
                    return false; // đang show intro
                }

                if (IdCurrentEvent == int.MinValue) {
                    return false; // đang không có event nào hoạt động
                }

                var eventItem = GetLiveEvent(IdCurrentEvent);
                if (eventItem != null) {
                    if (!eventItem.CanShowTokenAtGameComplete())
                        return false;
                }

                return true;
            }
        }

        public static event Action<int> OnUnlockSong;
        public static event Action OnChangeNoticeState;
        public static event Action<int, int> OnChangeToken;
        public static event Action OnInitCompleted;

        public TimeSpan RetroRemainTime => _openLiveEventDate - DateTime.Now;

        public bool HaveNotice {
            get {
                if (IsShowIntro) {
                    return false; // không notice khi chỉ có intro
                }

                // if (!CanEarnToken) {
                //     return false; //khi chưa đc earn token -> k show notice
                // }

                foreach (var eventItem in _dictEventItems) {
                    var progress = eventItem.Value.progress;
                    if (progress == null) continue;
                    if (progress.CompleteEvent) {
                        continue; // hoàn thành event rồi, k thêm nữa
                    }

                    if (progress.HasNotice(eventItem.Value.detailData.GetSongPrice())) {
                        return true; // can unlock song
                    }
                }

                return false;
            }
        }

        #region Unity Methods

        private IEnumerator Start() {
            while (Configuration.instance.gameloaded == false) {
                yield return null;
            }

            Util.WaitRemoteConfigDone(Init, true);
            InvokeRepeating(nameof(TickTime), 1f, 1f);
        }

        private void OnEnable() {
            SongList.OnUnlockSong += SongListOnOnUnlockSong;
        }

        private void OnDisable() {
            SongList.OnUnlockSong -= SongListOnOnUnlockSong;
            if (_dictEventItems != null) {
                foreach (var item in _dictEventItems) {
                    item.Value.SaveProgressData();
                }
            }
        }

        #endregion

        private List<Song>      _hideEventSongs;
        private LiveEventConfig _rootData;

        private void Init() {
            if (!RemoteConfigBase.instance.LiveEvent_Enable) {
                // đang không active event -> off hết các tính năng
                _isEnableLiveEvent = false;
                _isInit = true;
                OnInitCompleted?.Invoke();
                return;
            } 
            
            _dictEventItems = new Dictionary<int, LiveEventItem>();
            _rootData = LiveEventConfig.ParseFromString(RemoteConfigBase.instance.LiveEvent_Data);
            if (_rootData != null) {
                InitDataEventList(_rootData);
                List<string> allSongsOfInactiveEvent = GetAllSongsOfInactiveEvent();
                if (allSongsOfInactiveEvent != null && allSongsOfInactiveEvent.Count > 0) {
                    Util.WaitSongListDone(() => {
                        _hideEventSongs = SongManager.instance.HideSongsInList(allSongsOfInactiveEvent);
                    });
                }
            }
            if (_rootData == null || _rootData.EventList == null || _rootData.EventList.Length == 0) {
                Logger.LogError($"[LiveEvent] Không parse được data");
                _isEnableLiveEvent = false;
            } else {
                _isEnableLiveEvent = true;
                _minSongStart = _rootData.MinSongStart;

                int countSongStart = UserProperties.GetPropertyInt(UserProperties.song_start);
                _isPassSongCount = countSongStart >= _minSongStart;

                int userCurrentDay = GetUserLoginDay();
                if (userCurrentDay < _rootData.ShowFromUserDay) { // user chưa tới ngày show -> off event
                    _isEnableLiveEvent = false;
                } else if (userCurrentDay < _rootData.ActiveFromUserDay) { // user tới ngày show upcoming
                    _isOnlyShowIntroduction = true;
                    _openLiveEventDate = DateTime.Today.AddDays(_rootData.ActiveFromUserDay - userCurrentDay);
                } else { // thỏa mãn điều kiện
                    _isOnlyShowIntroduction = false;
                }
                Util.WaitSongListDone(() => {
                    InitGroup();
                });
            }

            _isInit = true;
            OnInitCompleted?.Invoke();
        }

        public bool IsComingEvent() {
            if (_rootData == null) {
                return false;
            }

            int userCurrentDay = GetUserLoginDay();
            int countSongStart = UserProperties.GetPropertyInt(UserProperties.song_start);
            bool isComing = userCurrentDay < _rootData.ShowFromUserDay ||
                            userCurrentDay < _rootData.ActiveFromUserDay || countSongStart < _rootData.MinSongStart;
            return isComing;
        }

        // Dùng để kích hoạt lại live event
        public void ReInit() {
            if (!_isInit) {
                return; // chỉ call khi đã init rồi
            }

            bool prevState = IsActiveEvent;
            if (!_isPassSongCount) {
                int countSongStart = UserProperties.GetPropertyInt(UserProperties.song_start);
                _isPassSongCount = countSongStart >= _minSongStart;
                bool newState = IsActiveEvent;

                if (newState != prevState) {
                    // có active event
                    if (!IsComingEvent()) {
                        SongList.instance.AddHideSongs();
                    }

                    InitGroup(true); // cần init group lại
                    OnInitCompleted?.Invoke();
                }
            }
        }

        private void InitDataEventList(LiveEventConfig rootData) {
            LiveEventData[] arrEventDatas = rootData.EventList;
            if (arrEventDatas == null || arrEventDatas.Length == 0) {
                Logger.LogError($"[LiveEvent] datas null");
                _isEnableLiveEvent = false;
                return;
            }

            DateTime now = DateTime.Now;
            _isEnableLiveEvent = true;
            foreach (LiveEventData liveEventData in arrEventDatas) {
                int idEvent = liveEventData.Id;
                if (now < liveEventData.TimeStart) {
                    AddFutureEvent(idEvent, liveEventData);
                } else if (now < liveEventData.TimeEnd) {
                    AddActiveEvent(idEvent, liveEventData, null);
                } else { //past event
                    // B1: kiểm tra user đang còn ở trong progress không -> nếu còn và đủ điều kiện tham gia -> cho tiếp tục
                    if (PlayerPrefs.HasKey(KEY_PROGRESS_DATA + idEvent)) {
                        // có join event
                        var progress = LoadProgressData(idEvent, liveEventData);
                        if ((now - progress.TimeStart).TotalDays > liveEventData.MinTotalDay) {
                            // vượt quá min total day rồi -> tính là past event
                            AddPastEvent(idEvent, liveEventData);
                        } else {
                            // nằm trong event extend
                            liveEventData.TimeEnd = progress.TimeStart.AddDays(liveEventData.MinTotalDay);
                            AddActiveEvent(idEvent, liveEventData, progress);
                        }
                    } else {
                        // B2: Nếu k đủ điều kiện tham gia nữa -> đưa vào list past event
                        AddPastEvent(idEvent, liveEventData);
                    }
                }
            }
        }

        private void AddActiveEvent(int idEvent, LiveEventData eventData, LiveEventProgress progress) {
            if (_dictEventItems.ContainsKey(idEvent)) {
                Logger.LogError($"Duplicated IdEvent {idEvent.ToString()} int dict active event");
                return;
            }

            eventData.status = LiveEventStatus.ACTIVE;
            eventData.RemainTime = eventData.TimeEnd - DateTime.Now;
            eventData.IconToken = Resources.Load<Sprite>($"icons/{eventData.Id}");


            var eventItem = LiveEventItem.CreateInstance(eventData, null);
            if (eventItem.IsValidData()) {
                if (progress == null) {
                    progress = LoadProgressData(idEvent, eventData);
                }

                //Logger.Log($"[LiveEvent] enable live event {eventData.Title}");
                IdCurrentEvent = idEvent;
                // Check thêm điều kiện đủ tối thiểu số ngày tham gia
                if ((eventData.TimeEnd - progress.TimeStart).TotalDays < eventData.MinTotalDay) {
                    // tham gia muộn
                    eventData.RemainTime = progress.TimeStart.AddDays(eventData.MinTotalDay) - DateTime.Now;
                }

                eventItem = LiveEventItem.CreateInstance(eventData, progress);
                _dictEventItems.Add(idEvent, eventItem);
            } else {
                Logger.Log($"[LiveEvent] invalid live event {eventData.Title}");
            }
        }

        private void AddFutureEvent(int idEvent, LiveEventData eventData) {
            // coming soon event
            if (_dictEventItems.ContainsKey(idEvent)) {
                Logger.LogError($"Duplicated IdEvent {idEvent} int dict coming soon events ");
                return;
            }

            eventData.status = LiveEventStatus.FUTURE;
            eventData.RemainTime = eventData.TimeStart - DateTime.Now;

            _dictEventItems.Add(idEvent, LiveEventItem.CreateInstance(eventData, null));
            Logger.Log($"[LiveEvent] coming soon event {eventData.Title}");
        }

        private void AddPastEvent(int idEvent, LiveEventData eventData) {
            // coming soon event
            if (_dictEventItems.ContainsKey(idEvent)) {
                Logger.LogError($"Duplicated IdEvent {idEvent} int dict past events ");
                return;
            }

            eventData.status = LiveEventStatus.PAST;
            eventData.RemainTime = TimeSpan.Zero;

            _dictEventItems.Add(idEvent, LiveEventItem.CreateInstance(eventData, null));
            Logger.Log($"[LiveEvent] past event {eventData.Title}");
        }

        /// <summary>
        /// Thực hiện giảm toàn bộ các event đi 1s.
        /// Cái này nên độc lập thời gian với Time.deltaTime vì thời gian event là thời gian thực
        /// </summary>
        private void TickTime() {
            if (_dictEventItems == null) {
                return;
            }

            foreach (var eventData in _dictEventItems) {
                eventData.Value.eventConfig.Tick();
            }
        }

        /// <summary>
        /// Lấy thời gian remain hiện tại -> hiện thông tin popup
        /// </summary>
        /// <param name="idEvent"></param>
        /// <returns></returns>
        public TimeSpan GetRemainTime(int idEvent) {
            var item = GetLiveEvent(idEvent);
            if (item == null) {
                Logger.LogError($"Don't exist event with ID {idEvent}");
                return TimeSpan.Zero;
            }

            return item.eventConfig.RemainTime;
        }

        private LiveEventProgress CreateNewProgressData(int idEvent, LiveEventData eventData) {
            //Debug.LogError("Create new");
            LiveEventProgress progressData;
            switch (eventData.liveEventType) {
                case LiveEventType.SkinQuest:
                    progressData = new SkinQuestProgress();
                    break;
                case LiveEventType.FeedMission:
                    progressData = new FeedMissionProgress();
                    break;
                default:
                    Logger.EditorLogError("Live Event", " Chưa làm type mới");
                    progressData = new SkinQuestProgress();
                    break;
            }

            PlayerPrefs.SetInt(KEY_ID_LIVEEVENT, idEvent); // set current progress Id
            progressData.Init(eventData);
            return progressData;
        }

        private LiveEventProgress LoadProgressData(int idEvent, LiveEventData eventData) {
            if (PlayerPrefs.HasKey(KEY_ID_LIVEEVENT)) { // đã từng tồn tại live event
                //int prevEvent = PlayerPrefs.GetInt(KEY_ID_LIVEEVENT);
                if ( /*prevEvent == idEvent &&*/ PlayerPrefs.HasKey(KEY_PROGRESS_DATA + idEvent)) {
                    // là 1 event ->>> load progress hiện tại
                    //Logger.Log(PlayerPrefs.GetString(KEY_PROGRESS_DATA + idEvent));
                    LiveEventProgress progressData = null;
                    switch (eventData.liveEventType) {
                        case LiveEventType.SkinQuest:
                            progressData =
                                JsonUtility.FromJson<SkinQuestProgress>(
                                    PlayerPrefs.GetString(KEY_PROGRESS_DATA + idEvent));
                            break;
                        case LiveEventType.FeedMission:
                            progressData =
                                JsonUtility.FromJson<FeedMissionProgress>(
                                    PlayerPrefs.GetString(KEY_PROGRESS_DATA + idEvent));
                            break;
                    }

                    if (progressData == null) {
                        return CreateNewProgressData(idEvent, eventData);
                    } else {
                        return progressData;
                    }
                } else {
                    // event mới -> clear data cũ
                    //Logger.EditorLog("Live Event",$"Clear old cached data {prevEvent} != {idEvent}");
                    //PlayerPrefs.DeleteKey(KEY_PROGRESS_DATA + prevEvent); // xóa toàn bộ progress event cũ
                    return CreateNewProgressData(idEvent, eventData);
                }
            } else {
                // chưa có live event bao giờ
                return CreateNewProgressData(idEvent, eventData);
            }
        }

        /// <summary>
        /// Cần phải init cái này càng sớm càng tốt, nhưng phải sau khi song list load xong
        /// </summary>
        private void InitGroup(bool isReinit = false) {
            if (SongManager.instance.songs.Count == 0) {
                Debug.LogError("Chưa init song count");
            }

            if (!IsActiveEvent) {
                return; // không tác động list song khi chưa active event
            }

            if (_isOnlyShowIntroduction) {
                return; // Nếu chỉ show intro -> chưa tác động vào list song
            }

            foreach (var data in _dictEventItems) {
                int idEvent = data.Value.eventConfig.Id;
                if (_dictEventItems[idEvent].status != LiveEventStatus.ACTIVE)
                    continue; // chỉ khởi tạo các active event
                _dictEventItems[idEvent].InitGroup(isReinit);
            }
        }


        private void SongListOnOnUnlockSong(string acm_id_v3) {
            if (!IsActiveEvent) return;
            foreach (var liveEvent in _dictEventItems.Values) {
                if (liveEvent.status == LiveEventStatus.ACTIVE) {
                    liveEvent.UpdateProgressAfterUnlockSong(acm_id_v3);
                }
            }
            CompleteUnlockSong();
        }

        public bool UnlockSong(int idEvent, string acm_id_v3) {
            var eventItem = GetLiveEvent(idEvent);
            if (eventItem == null) {
                Logger.LogError("IdEvent không hợp lệ");
                return false;
            }

            return eventItem.UnlockSong(acm_id_v3);
        }

        public void AddToken(int idEvent, int amount, string location) {
            var eventItem = GetLiveEvent(idEvent);
            if (eventItem == null) {
                return;
            }

            eventItem.AddToken(amount, location);
            OnChangeToken?.Invoke(idEvent, eventItem.progress.TokenAmount);
        }

        public void SpendToken(int idEvent, int amount) {
            var eventItem = GetLiveEvent(idEvent);
            if (eventItem == null) {
                return;
            }

            eventItem.SpendToken(amount);
            OnChangeToken?.Invoke(idEvent, eventItem.progress.TokenAmount);
        }

        public Dictionary<int, int> GetCurrencySongEarn() {
            if (!CanEarnToken) {
                return null;
            }

            var eventItem = GetLiveEvent(IdCurrentEvent);
            if (eventItem == null) {
                return null;
            }

            return eventItem.GetCurrencySongEarn();
        }

        public bool IsEarnTokenInEndless() {
            if (!CanEarnToken) {
                return false;
            }

            var eventItem = GetLiveEvent(IdCurrentEvent);
            if (eventItem == null) {
                return false;
            }

            return eventItem.CanEarnTokenInEndless();
        }

        public LiveEventItem GetCurrentLiveEvent() {
            if (IdCurrentEvent == int.MinValue) {
                return null;
            }
            return GetLiveEvent(IdCurrentEvent);
        }

        public LiveEventItem GetLiveEvent(int idEvent) {
            if (!_dictEventItems.ContainsKey(idEvent)) {
                Logger.EditorLogError($"IdEvent không hợp lệ {idEvent}");
                return null;
            }

            return _dictEventItems[idEvent];
        }

        public (bool isHaveReward, int idEvent) HaveRewardAvailable {
            get {
                foreach (var item in _dictEventItems) {
                    if (item.Value.HaveReward()) {
                        return (true, item.Key);
                    }
                }

                return (false, 0);
            }
        }
        public IEnumerable<LiveEventData> GetAllEvent() {
            List<LiveEventData> listConfigs = new List<LiveEventData>();
            foreach (var item in _dictEventItems) {
                listConfigs.Add(item.Value.eventConfig);
            }

            return listConfigs;
        }

        /// <summary>
        /// Đếm số lượng event đang active . Thông thường là 1 event thôi
        /// </summary>
        /// <returns></returns>
        public int CountActiveEvent() {
            if (!_isEnableLiveEvent) {
                return 0;
            }
            int count = 0;
            foreach (var pair in _dictEventItems) {
                if (pair.Value.status == LiveEventStatus.ACTIVE) {
                    count++;
                }
            }

            return count;
        }

        public int GetFirstActiveEvent() {
            foreach (var pair in _dictEventItems) {
                if (pair.Value.status == LiveEventStatus.ACTIVE) {
                    return pair.Key;
                }
            }

            return -1;
        }

        public bool ContainSong(Song currentSong) {
            var eventItem = GetLiveEvent(IdCurrentEvent);
            return eventItem != null && eventItem.ContainSong(currentSong);
        }

        public void CompleteUnlockSong() {
            OnUnlockSong?.Invoke(IdCurrentEvent);
        }

        public void CheckShowInfor(int idEvent) {
            if (IsShowIntro)
                return; // không auto bật popup infor khi đang ở intro event

            var eventItem = GetLiveEvent(idEvent);
            if (eventItem != null && !eventItem.progress.PassShowInfor) {
                ShowEventInfor(eventItem.eventConfig.Id); // show popup infor in the first time go to event
            }
        }

        public void ShowEventInfor(int idEvent) {
            var popup = Util.ShowPopUp(PopupName.LiveEventInfor + idEvent);
            if (popup != null) {
                if (popup.TryGetComponent(out UILiveEventInfor liveEventInfor)) {
                    liveEventInfor.Show(TrackingLocation.event_screen.ToString());

                    //pass data
                    var eventItem = GetLiveEvent(idEvent);
                    eventItem?.CompleteShowInfor();
                } else {
                    Logger.EditorLogError("Not found component!");
                }
            } else {
                Logger.EditorLogError("Not found popup!");
            }
        }

        public bool IsNeedUpdateSongList() {
            if (_hideEventSongs != null && _hideEventSongs.Count != 0) {
                foreach (var liveEvent in _dictEventItems.Values) {
                    if (liveEvent.status == LiveEventStatus.ACTIVE)
                        return true;
                }
            }

            return false;
        }
        private List<string> GetAllSongsOfInactiveEvent() {
            if (!RemoteConfig.instance.LiveEvent_Enable) {
                return null;
            }

            if (!RemoteConfig.instance.LiveEvent_OnlyShowSongWhenActiveEvent) {
                return null;
            }

            bool isComing = IsComingEvent();
            List<string> listSong = new List<string>();
            if (!isComing) {
                foreach (var item in _dictEventItems) {
                    if (item.Value.status == LiveEventStatus.ACTIVE) {
                        continue;
                    }

                    listSong.AddRange(item.Value.GetAllSongsNeedToHideAtMainHome());
                }
            }

            Logger.EditorLog("Live Event", "[GetAllSongsOfInactiveEvent] listSong.Count => " + listSong.Count);
            return listSong;
        }

        public List<Song> GetHideEventSongs() {
            return _hideEventSongs;
        }

        public void TurnOffNotice() {
            OnChangeNoticeState?.Invoke();
        }

        public void SetCurrentLiveEvent(int eventItemId) {
            IdCurrentEvent = eventItemId;
            Logger.EditorLog("LiveEvent", $"Set current live event: {eventItemId}");
        }

        private int GetUserLoginDay() {
            if (RemoteConfig.instance.LiveEvent_UserDayByDayDiff)
            {
                return UserProperties.GetDayDiff();
            }
            else
            {
                return UserProperties.GetLoginDay();
            }
        }

        public LiveEventItem GetEventNeedShowToolTip() {
            foreach (var liveEvent in _dictEventItems.Values) {
                if (liveEvent.status == LiveEventStatus.ACTIVE) {
                    var progress = liveEvent.progress;
                    if (progress == null) continue;
                    if (!progress.PassTutorial) {
                        return liveEvent;
                    }
                }
            }
            return null;
        }
    }
}