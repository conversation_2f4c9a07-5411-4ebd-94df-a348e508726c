using System;
using System.IO;
using UnityEditor;
using UnityEngine;

public class CropSongsEditor : Editor {
    private const string menuLogs               = "InWave/Modules/Enable CropSongs";
    private const string folderCropSongsEnable  = @"GameCore/DevInfo/CropSongs";
    private const string folderCropSongsDisable = @"GameCore/DevInfo/.CropSongs";

    [MenuItem(menuLogs, true)]
    public static bool DoEnableCropSongsValidate() {
        string path = Path.Combine(Application.dataPath, folderCropSongsEnable);
        bool isDefine = Directory.Exists(path);

        UnityEditor.Menu.SetChecked(menuLogs, isDefine);
        return true;
    }

    [MenuItem(menuLogs)]
    static void DoEnableCropSongs() {
        try {
            if (UnityEditor.Menu.GetChecked(menuLogs)) { //Uncheck => disable logs

                Debug.Log("Disable folder: " + folderCropSongsEnable);
                EnableFolder(folderCropSongsEnable, false);

            } else { //Check => enable logs
                Debug.Log("Enable folder: " + folderCropSongsEnable);
                EnableFolder(folderCropSongsEnable, true);
            }

        } catch (Exception e) {
            Debug.LogException(e);
        }
    }

    private static void EnableFolder(string folderName, bool isEnable) {
        string OnLinarConsole = Path.Combine(Application.dataPath, folderCropSongsEnable);
        string OffLinarConsole = Path.Combine(Application.dataPath, folderCropSongsDisable);

        if (isEnable) {
            if (Directory.Exists(OffLinarConsole)) {
                Directory.Move(OffLinarConsole, OnLinarConsole);
            }
        } else {
            if (Directory.Exists(OnLinarConsole)) {
                Directory.Move(OnLinarConsole, OffLinarConsole);
            }
        }

        AssetDatabase.Refresh();
    }
}