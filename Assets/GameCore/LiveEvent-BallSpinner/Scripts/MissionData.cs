using System;
using UnityEngine;

namespace TileHop.LiveEvent.BallSpinner {
    [SerializeField]
    public class MissionData : IComparable {
        [Header("Data")]
        public int id;

        public MissionType type;
        public int         count;

        public RewardType rewardType;
        public int        reward;

        [Header("Progress")]
        public int progress;

        public bool isComplete;
        public bool isRewarded;

        public bool DoMission(int step = 1) {
            progress += step;
            if (progress >= count) {
                progress = count;
                isComplete = true;
            }

            return isComplete;
        }

        public void DoMissionPoints(int score) {
            //if (score >= mission.points) {
            DoMission();
            // }
        }

        public void DoMissionGenre(string genre) {
            //if (!string.IsNullOrEmpty(genre) && genre.ToUpper().Contains(this.genre.ToUpper())) {
            DoMission();
            //}
        }

        public void ResetData() {
            progress = 0;
            isComplete = false;
            isRewarded = false;
        }

        public void GetReward(string source) {
            isRewarded = true;
            switch (rewardType) {
                case RewardType.Coin:
                    BallSpinnerManager.instanceSafe.ChangeCoins(reward, source);
                    break;
                case RewardType.Ticket:
                case RewardType.TicketDaily:
                    BallSpinnerManager.instanceSafe.ChangeTickets(reward, source);
                    break;
                default:
                    Logger.EditorLogError($"Not handle this reward type: {rewardType}!!!");
                    break; 
            }
        }

        public int CompareTo(object obj) {
            MissionData another = (MissionData) obj;
            if (this.isRewarded && another.isRewarded) {
                return this.id.CompareTo(another.id);
            }

            if (another.isRewarded)
                return -1;
            if (this.isRewarded)
                return 1;

            if (this.isComplete && another.isComplete) {
                return this.id.CompareTo(another.id);
            }

            if (another.isComplete)
                return 1;
            if (this.isComplete)
                return -1;

            return this.id.CompareTo(another.id);
        }
    }
}