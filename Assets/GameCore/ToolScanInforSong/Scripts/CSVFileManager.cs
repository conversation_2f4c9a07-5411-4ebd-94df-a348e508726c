using System;
using System.IO;
using UnityEngine;

namespace TileHop.ToolScanSongInformation {
    public class CSVWriter {
        public static void WriteCSV(string path, string data) {
            FileStream file = new FileStream(path, FileMode.Create, FileAccess.Write);
            StreamWriter sw = new StreamWriter(file);
            sw.Write(data);
            sw.Close();
            file.Close();
        }

        public static string GetDownloadFolder() {
            string[] temp =
                (Application.persistentDataPath.Replace("Android", "")).Split(new string[] { "//" },
                    System.StringSplitOptions.None);

            string folderPath = (temp[0] + "/Download");
            CreateDirectory(folderPath);
            return folderPath;
        }

        private static void CreateDirectory(string dir) {
            if (!Directory.Exists(dir)) {
                var directory = Directory.CreateDirectory(dir);
            }
        }

        public static string GetTime() {
            var time = DateTime.Now;
            return
                $"{(time.Year % 100).ToString("00")}{time.Month.ToString("00")}{time.Day.ToString("00")}{time.Hour.ToString("00")}{time.Minute.ToString("00")}";
        }
    }
}