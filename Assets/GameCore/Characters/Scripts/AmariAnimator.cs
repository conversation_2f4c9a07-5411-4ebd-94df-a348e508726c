using UnityEngine;

public class AmariAnimator : SimpleAnimator {
    private static readonly int _shortJumpLr  = Animator.StringToHash("ShortJumpLR");
    private static readonly int _shortJumpRl  = Animator.StringToHash("ShortJumpRL");
    private static readonly int _normalJumpLr = Animator.StringToHash("NormalJumpLR");
    private static readonly int _normalJumpRl = Animator.StringToHash("NormalJumpRL");

    public override void SetJump(float jumpTime, bool isJumpRight, int random) {
        if (Ball.b.isPausePlayer) {
            return;
        }

        if (enableChangeToVictoryInAdvance && currentNoteId >= NotesManager.instance.noteCount - 2) {
            return;
        }

        JumpType jumpType = GetJumpType(jumpTime);
        switch (jumpType) {
            case JumpType.ShortJump:
                animator.SetTrigger(isJumpRight ? _shortJumpLr : _shortJumpRl);
                animator.speed = timeShortJump / jumpTime;
                break;
            
            case JumpType.NormalJump:
                animator.SetTrigger(isJumpRight ? _normalJumpLr : _normalJumpRl);
                animator.speed = timeNormalJump / jumpTime;
                break;

            case JumpType.LongJump:
                int range = random == 0 ? Random.Range(1, 3) : random; // 1 2
                switch (range) {
                    case 1:
                        animator.SetTrigger(LongJumpLR);
                        animator.speed = timeLongJumpLr / jumpTime;
                        break;

                    case 2:
                        animator.SetTrigger(LongJumpRL);
                        animator.speed = timeLongJumpRl / jumpTime;
                        break;
                }

                break;
        }
        
        UpdateParticleTime(animator.speed);
    }

    public override void SetStateSlide() {
        animator.SetTrigger(_slide);
        animator.speed = 1;
        UpdateParticleTime(1f);
    }
}
