using UnityEngine;

public class PumpkinAnimation : CharacterAnimator {
    private static readonly int startInGame = Animator.StringToHash("StartInGame");

    public override void SetJump(float jumpTime, bool isJumpRight, int random) { }

    public override void SetLastJump(float jumpTime) { }

    public override void SetStateStart() {
        animator.SetTrigger(startInGame);
        animator.speed = 1;
    }

    public override float SetStateVictory() {
        return 0;
    }

    public override void RunAnimation(int hash) {
        animator.SetTrigger(hash);
        animator.speed = 1;
    }

    public override void SetStateFly() { }

    public override void SetBallID(int ballId) { }

    public override void SetActiveTrail(bool isActive) {
        //no thing
    }

    public override void PlayRandomAnimFromShop(bool ingame) {
        animator.SetTrigger(ingame ? startInGame : Pop);
    }
}