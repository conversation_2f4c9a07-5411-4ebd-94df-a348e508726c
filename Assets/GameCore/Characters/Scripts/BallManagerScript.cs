using System;
using UnityEngine;

public class BallManagerScript : Mono<PERSON><PERSON><PERSON><PERSON>, ICharacter {
    private                Transform _ball;
    [NonSerialized] public Transform transformCached;

    private void Awake() {
        transformCached = this.transform;
    }

    public void Init(int ballId, int layer = -1, Action<bool> onInitDone = null, int timeout = 0) {
        if (_ball != null) {
            Destroy(_ball.gameObject);
        }

        Transform loadPrefab = Resources.Load<Transform>(ResourcesPath.Ball + ballId);
        if (loadPrefab != null) {
            _ball = Instantiate(loadPrefab, this.transform, false);
            if (layer >= 0) {
                _ball.SetLayerRecursively(layer);
            }

            onInitDone?.Invoke(true);
        } else {
            Logger.LogWarning($"Can't load prefab Ball {ballId} in resources path: {ResourcesPath.Ball + ballId}");
            onInitDone?.Invoke(false);
        }
    }

    public void ShowVfx() {
        if (_ball == null)
            return;

        if (_ball.childCount > 0) {
            _ball.GetChild(0).gameObject.SetActive(true);
        }
    }

    public void SetActive(bool isShow) {
        gameObject.SetActive(isShow);
    }

    public void SetActiveTrail(bool isActive) { }

    public Renderer GetRenderer() {
        return _ball.GetComponent<Renderer>();
    }

    public float PrepareStart() {
        return 0;
    }

    public float PrepareFinished() {
        return 0;
    }

    public void Rotate(float angle) {
        Vector3 newRotate = new Vector3(0, angle, 0);
        transformCached.Rotate(newRotate);
    }

    public void SetRotation(Vector3 direction) {
        transformCached.localRotation = Quaternion.Euler(direction);
    }

    public void SetChildRotation(Vector3 direction) {
        if (transformCached.childCount > 0) {
            transformCached.GetChild(0).localRotation = Quaternion.Euler(direction);
        }
    }

    /// <summary>
    /// animation clip event
    /// </summary>
    public void PlayPreviewBouncing() {
        if (RemoteConfigBase.instance.BouncingBall_UseBouncingBall && !Ball.b.isCharacter) {
            Ball.b.StretchController.PlayBouncePreview();
        }
    }
}