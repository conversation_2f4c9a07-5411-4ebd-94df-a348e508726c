using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace TileHop.Cores.Boosters {
    public static class BoosterTracking {
        public enum OnboardState {
            unlock,
            selection,
            setup,
            ready
        }

        public static void BoosterOnboarding(BoosterType boosterType, OnboardState state) {
            string eventKey = string.Empty;
            switch (state) {
                case OnboardState.unlock:
                    eventKey = "booster_onboarding_unlock";
                    break;

                case OnboardState.selection:
                    eventKey = "booster_onboarding_selection";
                    break;

                case OnboardState.setup:
                    eventKey = "booster_onboarding_setup";
                    break;

                case OnboardState.ready:
                    eventKey = "booster_onboarding_ready";
                    break;
            }

            Dictionary<string, object> param = new Dictionary<string, object> {
                {TRACK_PARAM.booster_name, ConvertToName(boosterType)},
            };
            AnalyticHelper.LogEvent(eventKey, param);
        }

        public static void BoosterSelection(BoosterType boosterType, string song_name, string song_acm_id) {
            string eventKey = "booster_selection";

            Dictionary<string, object> param = new Dictionary<string, object> {
                {TRACK_PARAM.booster_name, ConvertToName(boosterType)},
                {TRACK_NAME.song_name, song_name},
                {TRACK_NAME.song_acm_id, song_acm_id},
                {TRACK_PARAM.gameplay_count, GameController.gameplayCount}
            };
            AnalyticHelper.LogEvent(eventKey, param);
        }

        public static void BoosterImpression(BoosterType boosterType, string song_name, string song_acm_id) {
            string eventKey = "booster_impression";

            Dictionary<string, object> param = new Dictionary<string, object> {
                {TRACK_PARAM.booster_name, ConvertToName(boosterType)},
                {TRACK_NAME.song_name, song_name},
                {TRACK_NAME.song_acm_id, song_acm_id},
                {TRACK_PARAM.gameplay_count, GameController.gameplayCount}
            };
            AnalyticHelper.LogEvent(eventKey, param);
        }

        public static void BoosterUse(BoosterType boosterType, string song_name, string song_acm_id) {
            string eventKey = "booster_use";

            Dictionary<string, object> param = new Dictionary<string, object> {
                {TRACK_PARAM.booster_name, ConvertToName(boosterType)},
                {TRACK_NAME.song_name, song_name},
                {TRACK_NAME.song_acm_id, song_acm_id},
                {TRACK_PARAM.gameplay_count, GameController.gameplayCount}
            };
            // AnalyticHelper.UpdateParamsAccumulatedCount(param, eventKey);
            AnalyticHelper.LogEvent(eventKey, param);
        }

        public static void BoosterActive(BoosterType boosterType, string song_name, string song_acm_id) {
            string eventKey = "booster_active";

            Dictionary<string, object> param = new Dictionary<string, object> {
                {TRACK_PARAM.booster_name, ConvertToName(boosterType)},
                {TRACK_NAME.song_name, song_name},
                {TRACK_NAME.song_acm_id, song_acm_id},
                {TRACK_PARAM.gameplay_count, GameController.gameplayCount}
            };
            // AnalyticHelper.UpdateParamsAccumulatedCount(param, eventKey);
            AnalyticHelper.LogEvent(eventKey, param);
        }

        public static void BoosterReceive(BoosterType boosterType, string source) {
            string eventKey = "booster_receive";

            Dictionary<string, object> param = new Dictionary<string, object> {
                {TRACK_PARAM.source, source},
                {TRACK_PARAM.booster_name, ConvertToName(boosterType)},
                {TRACK_PARAM.gameplay_count, GameController.gameplayCount}
            };
            if (Util.IsGameScene()) {
                if (GameController.isInstanced) {
                    var song = GameController.instance._song;
                    param.TryAdd(TRACK_NAME.song_name, song.name);
                    param.TryAdd(TRACK_NAME.song_acm_id, song?.acm_id_v3);
                }
            }

            // AnalyticHelper.UpdateParamsAccumulatedCount(param, eventKey);
            AnalyticHelper.LogEvent(eventKey, param);
        }

        public static void UpdateParamBooster(Dictionary<string, object> param) {
            param.TryAdd(TRACK_PARAM.booster_use, BoosterManager.GetListUsedItem());
        }

        public static string ConvertToName(BoosterType boosterType) {
            switch (boosterType) {
                case BoosterType.Shield:
                    return "life_saver";

                case BoosterType.GemRain:
                    return "gem_rain";

                case BoosterType.TileTidy:
                    return "tile_tidy";

                case BoosterType.Magnet:
                    return "magnet";

                case BoosterType.HyperStar:
                    return "hyper_star";

                default:
                    Logger.EditorLogError("BoosterType: " + boosterType);
                    return "unnamed";
            }
        }
    }
}