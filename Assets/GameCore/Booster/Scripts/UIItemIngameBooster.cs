using System;
using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using Sirenix.OdinInspector;
using TileHop.Cores.Pooling;
using UnityEngine;
using UnityEngine.UI;

namespace TileHop.Cores.Boosters {
    public class UIItemIngameBooster : MonoBehaviour {
        [SerializeField]            private RectTransform rootTransform;
        [SerializeField]            private Image         iconBooster;
        [SerializeField]            private Image         imgUsing;
        [SerializeField]            private Text          txtAmount;
        [ShowInInspector, ReadOnly] private BoosterType   _type;
        [ShowInInspector, ReadOnly] private int           _amount;
        [ShowInInspector, ReadOnly] private bool          _using;
        public BoosterType type => _type;

        private float _timeActive;
        private float _countTime;
        private event Action _callback;

        private void LateUpdate() {
            if (_using) {
                if (GameController.instance.game == GameStatus.LIVE) {
                    _countTime -= Time.deltaTime;
                    if (_countTime > 0) {
                        imgUsing.fillAmount = _countTime / _timeActive;
                    } else {
                        _using = false;
                        imgUsing.gameObject.SetActive(false);
                        _callback?.Invoke();
                    }
                }

            }
        }

        public void Init(int index,BoosterType boosterType, int amount) {
            _using = false;
            _type = boosterType;
            _amount = amount;
            txtAmount.text = _amount > 1 ? _amount.ToString() : string.Empty;

            imgUsing.gameObject.SetActive(false);
            var boosterConfig = BoosterManager.GetBoosterConfig(boosterType);
            if (boosterConfig == null) {
                iconBooster.gameObject.SetActive(false);
            } else {
                iconBooster.sprite = boosterConfig.icon;
                iconBooster.gameObject.SetActive(true);
            }

            this.gameObject.SetActive(true);
            StartCoroutine(IEShow(index * 0.2f));
        }

        private IEnumerator IEShow(float delayTime) {
            rootTransform.DOLocalMoveX(0, 0f);
            transform.localScale = new Vector3(1, 0, 1);
            yield return YieldPool.GetWaitForSeconds(delayTime);

            rootTransform.DOLocalMoveX(0, 0.3f);
            transform.DOScaleY(1, 0.3f);
        }

        public void Active(float time, Action callback) {
            if (time <= 0) {
                callback?.Invoke();
                return;
            }

            _using = true;
            _timeActive = time;
            _countTime = time;
            _callback = callback;
            imgUsing.fillAmount = _countTime / _timeActive;
            imgUsing.gameObject.SetActive(true);
        }

        private void Remove() {
            rootTransform.DOLocalMoveX(rootTransform.rect.width, 0.3f);
            transform.DOScaleY(0, 0.3f).OnComplete(Destroy);
        }

        public void Destroy() {
            this.gameObject.SetActive(false);
        }

        public void Reduce() {
            _amount -= 1;
            if (_amount <= 0) {
                Remove();
            } else {
                txtAmount.text = _amount > 1 ? _amount.ToString() : string.Empty;
            }
        }
    }
}