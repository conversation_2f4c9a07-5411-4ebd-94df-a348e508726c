using DG.Tweening;
using Sirenix.OdinInspector;
using UnityEngine;
using Random = UnityEngine.Random;

namespace TileHop.Cores.Boosters {
    public class BoosterGemRain : MonoBehaviour {
        [SerializeField] private Transform      transCache;
        [SerializeField] private ParticleSystem vfxCloud;
        [SerializeField] private ParticleSystem vfxGemRain;

        [ShowInInspector] private float _duration;
        [ShowInInspector] private int   _maxTimeAppear = 1;
        [ShowInInspector] private int   _value         = 1;
        [ShowInInspector] private int   _maxAmount     = 1;

        [ShowInInspector] private float _dropInterval;
        [ShowInInspector] private int   _appearMoodChange;
        [ShowInInspector] private STATE _state     = STATE.DeActive;
        [ShowInInspector] private float _countTime = 0f;
        [ShowInInspector] private float _targetTime;

        private static readonly Vector3   _offsetDefault = new Vector3(0, 10, 30);
        private                 Transform _targetFollow;
        private                 Vector3   _currentPos = _offsetDefault;
        private                 float     _spawnTime;
        private                 float     _rangeX;

        private                             int _curAmount = 0;
        private                             int _moodCount;
        [ShowInInspector, ReadOnly] private int _dropAmount;

        private enum STATE {
            FadeIn,
            Active,
            FadeOut,
            DeActive,
            Disable
        }

        private void OnEnable() {
            Spawner.OnChangeMood += OnMoodChange;
        }

        private void OnDisable() {
            Spawner.OnChangeMood -= OnMoodChange;
        }

        private void Update() {
            if (GameController.instance.game == GameStatus.LIVE) {
                if (_state != STATE.DeActive && _state != STATE.Disable) {
                    _currentPos.z = _targetFollow.position.z + _offsetDefault.z;
                    transCache.position = _currentPos;
                }

                if (_state == STATE.Active) {
                    CheckSpawnDiamond();
                    if (_countTime >= _targetTime) {
                        ChangeState();
                    }

                    _countTime += Time.deltaTime;
                }
            }
        }

#if UNITY_EDITOR
        private void LateUpdate() {
            if (Input.GetKeyDown(KeyCode.G)) {
                Logger.EditorLogError(BoosterManager.KEY_FEATURE, "Test feature GemRain");
                if (_state == STATE.DeActive || _state == STATE.Disable) {
                    _state = STATE.DeActive;
                    ChangeState();
                }
            }
        }
#endif

        private void OnMoodChange(int arg1, SkinSet arg2) {
            _moodCount++;
            if (_moodCount >= _appearMoodChange) {
                if (_state == STATE.DeActive && _curAmount < _maxTimeAppear) {
                    ChangeState();
                }
            }
        }

        public void Init(Transform ballCache) {
            this._targetFollow = ballCache;
            _duration = RemoteConfigBase.instance.Booster_GemRain_Duration;
            _appearMoodChange = RemoteConfigBase.instance.Booster_GemRain_MoodChange;
            _value = RemoteConfigBase.instance.Booster_GemRain_ValueGem;
            _maxAmount = RemoteConfigBase.instance.Booster_GemRain_AmountGem;
            if (_maxAmount <= 0) {
                _maxAmount = 20;
            }

            if (_value <= 0) {
                _value = 1;
            }

            _dropInterval = _duration / _maxAmount;

            _rangeX = RemoteConfigBase.instance.GetTileMaxPositionX();
        }

        public void ActiveFeature() {
            _moodCount = 1;
            _curAmount = 0;
            _currentPos.z = _targetFollow.position.z + _offsetDefault.z;
            transCache.position = _currentPos;
            vfxCloud.gameObject.SetActive(false);
            vfxGemRain.Stop();
            if (_curAmount >= _maxTimeAppear) {
                _state = STATE.Disable;
            } else {
                _state = STATE.DeActive;
                _countTime = 0f;
                _targetTime = 20;
            }
        }

        private void CheckSpawnDiamond() {
            if (_spawnTime <= 0f) {
                if (SpawnDiamond()) {
                    _spawnTime += _dropInterval;
                } else {
                    _spawnTime += _dropInterval / 2f;
                }
            } else {
                _spawnTime -= Time.deltaTime;
            }
        }

        private bool SpawnDiamond() {
            if (_dropAmount >= _maxAmount) {
                return false;
            }

            float zPos = transCache.position.z;
            Platform tileTarget = Spawner.s.CheckSpawnInTile(zPos);
            Diamond diamond;
            if (tileTarget) {
                diamond = Spawner.s.CreateDiamond(_value, CurrencyEarnSource.booster, tileTarget, zPos);
            } else {
                //spawn random
                Vector3 initPos = Spawner.s.CalculateDiamondOnRoad(zPos);

                if (initPos.Equals(Vector3.zero)) {
                    return false;
                }

                //randomX
                float randomValue =
                    initPos.y * (Random.value > 0.5f
                        ? 0.5f
                        : -0.5f); //nhân với initPos.y vì độ càng cao thì biên độ càng lớn
                initPos.x = Mathf.Clamp(initPos.x + randomValue, -_rangeX, _rangeX);

                diamond = Spawner.s.CreateDiamond(_value, CurrencyEarnSource.booster, initPos);
            }

            float timeMove = Ball.b.timeScale > 1 ? 0.2f / Ball.b.trustTime : 0.2f;
            diamond.DropFromCloud(_currentPos.y / 2, timeMove);
            _dropAmount += 1;
#if UNITY_EDITOR
            diamond.gameObject.name = $"GemRain_{_dropAmount}";
#endif
            return true;
        }

        private void ChangeState() {
            _countTime = 0f;
            switch (_state) {
                case STATE.Active:
                    _state = STATE.FadeOut;
                    _targetTime = 0.3f;
                    vfxGemRain.Stop();
                    DOVirtual.DelayedCall(_targetTime, ChangeState);
                    break;

                case STATE.FadeOut:
                    // vfxCloud.Stop();
                    vfxCloud.gameObject.SetActive(false);
                    if (_curAmount >= _maxTimeAppear) {
                        _state = STATE.Disable;
                    } else {
                        _state = STATE.DeActive;
                        _targetTime = 20f;
                    }

                    break;

                case STATE.DeActive:
                    _state = STATE.FadeIn;
                    _targetTime = 0.3f;
                    vfxCloud.gameObject.SetActive(true);
                    vfxCloud.Play();
                    DOVirtual.DelayedCall(_targetTime, ChangeState);
                    break;

                case STATE.FadeIn:
                    vfxGemRain.Play();
                    _state = STATE.Active;
                    _targetTime = _duration;
                    _spawnTime = 0;
                    _curAmount++;
                    _dropAmount = 0;
                    Use();
                    break;
            }
        }

        public void Use() {
            BoosterManager.UseItem(BoosterType.GemRain, _duration);
        }

        public void GameStop() {
            if (vfxCloud) {
                vfxCloud.Stop();
            }
            if (vfxGemRain) {
                vfxGemRain.Stop();
            }
            if (_curAmount >= _maxTimeAppear) {
                _state = STATE.Disable;
            } else {
                _state = STATE.DeActive;
                _countTime = 0f;
                _targetTime = 20f;
            }
        }
    }
}