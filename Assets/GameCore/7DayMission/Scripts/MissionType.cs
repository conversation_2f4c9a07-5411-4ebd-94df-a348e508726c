// ReS<PERSON>per disable InconsistentNaming
public enum MissionType {
    start_x_songs,
    play_x_songs_and_get_at_least_1_star,
    play_x_songs_and_get_at_least_2_stars,
    finish_x_songs,
    play_x_songs_in_endless_mode,
    play_x_songs_in_genre_y,
    replay_x_times,
    continue_x_times,
    unlock_x_skins,
    get_at_least_x_point,
    get_x_stars,
    unlock_x_songs,
    get_free_gem_in_shop_x_times, 
    unlock_x_songs_with_ads,
    reach_1_stars_x_times,
    reach_2_stars_x_times,
    reach_3_stars_x_times,
    unlock_x_songs_with_diamond,
    equip_other_ball_and_play_x_songs,
    finish_x_rounds_in_endless_mode,
    get_x_perfect,
    equip_other_ball_x_times,
    login_x_days,
    get_x_tile,
    adjust_sound,
    view_profile,
    customize_x_song,
    claim_x_star_reward,
    view_mission,
    view_achievement
}