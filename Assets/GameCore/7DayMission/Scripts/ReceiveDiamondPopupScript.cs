using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// trungvt
/// </summary>
public class ReceiveDiamondPopupScript : TabScript {
    #region Fields

    [SerializeField] private Text txtTitle;

    #endregion

    #region Unity Methods

    #endregion

    #region Methods

    public void Show(int count) {
        txtTitle.text = LocalizationManager.instance.GetLocalizedValue("RECEIVED") + " X " + count;
        LocalizationManager.instance.UpdateFont(txtTitle);
        Show(Direction.Top);
        DOVirtual.DelayedCall(3, () => {
            //
            Hide(Direction.Top, () => {
                Destroy(gameObject);
                //
            });
        });
    }

    #endregion
}