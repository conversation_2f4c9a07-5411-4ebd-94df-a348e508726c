using System;
using UnityEngine;
using Music.ACM;

namespace TileHop.PlaytimeReward {
    public class PlaytimeReward : Singleton<PlaytimeReward>, FeatureService {
        private PlaytimeRewarCondition _condition;
        private PlaytimeRewarData      _data;

        [SerializeField, ReadOnly] private bool isEnableFeature;
        [SerializeField, ReadOnly] private bool isActive;

        private bool  _isCounting;
        private float _currentSeconds;
        private int   _currentMinute;
        private int   _currentValue;
        private int   _maxMinute;

        public const int     SecondPerMinute = 60;
        public       bool    IsActive       => isActive;
        public       int     ValuePerMinute => _data.gem_per_minute;
        public       int     MaxReward      => _data.gem_max;
        public       bool    EnableMultiply => _data.multiply_display;
        public       float[] MultiplyValues => _data.GetMultiplyValues();
        public       float[] MultiplySpeeds => _data.GetMultiplySpeeds();

        private const string KEY_ONBOARDING_BUTTON = "PlaytimeReward_Onboarding_Button";
        private const string KEY_ONBOARDING_POPUP  = "PlaytimeReward_Onboarding_Popup";

        private const string KEY_PLAYTIME_REWARD_SECOND = "PlaytimeReward_Seconds";
        private const string KEY_PLAYTIME_REWARD_MINUTE = "PlaytimeReward_Minutes";

        public bool IsShownTooltipButton {
            get => PlayerPrefs.GetInt(KEY_ONBOARDING_BUTTON, 0) == 1;
            set => PlayerPrefs.SetInt(KEY_ONBOARDING_BUTTON, value == true ? 1 : 0);
        }

        public bool IsShownTooltipPopup {
            get => PlayerPrefs.GetInt(KEY_ONBOARDING_POPUP, 0) == 1;
            set => PlayerPrefs.SetInt(KEY_ONBOARDING_POPUP, value == true ? 1 : 0);
        }

        public bool NeedShownTooltipPopup => !IsShownTooltipPopup && _data.enable_tooltip;

        public static event Action<RewardData> OnChangeValueReward;

        #region Unity Medthods


        private void OnDisable() {
            SaveProgress();
        }

        private void Update() {
            if (_isCounting) {
                _currentSeconds += Time.deltaTime;
                if (_currentSeconds >= SecondPerMinute) {
                    // next minute
                    _currentSeconds -= SecondPerMinute;
                    AddMinute();
                }
            }
        }

        private void OnApplicationPause(bool pauseStatus) {
            if (pauseStatus) {
                _isCounting = false;
                SaveProgress();
            } else {
                _isCounting = isEnableFeature;
            }
        }

        #endregion
        
        public void Init() {
            try {
                _condition =
                    JsonUtility.FromJson<PlaytimeRewarCondition>(RemoteConfigBase.instance.knob_enable_condition);
                // Logger.EditorLog("PlaytimeReward", JsonUtility.ToJson(_condition));
                _data = JsonUtility.FromJson<PlaytimeRewarData>(RemoteConfigBase.instance.knob_data);
                // Logger.EditorLog("PlaytimeReward", JsonUtility.ToJson(_data));

                isEnableFeature = _condition != null;
                isActive = _condition != null && _condition.Validate();

                _isCounting = isEnableFeature;
                _currentMinute = PlayerPrefs.GetInt(KEY_PLAYTIME_REWARD_MINUTE, 0);
                _currentValue = _currentMinute * ValuePerMinute;
                _currentSeconds = PlayerPrefs.GetFloat(KEY_PLAYTIME_REWARD_SECOND, 0);
                _maxMinute = _data.GetMaxMinute();
            }
            catch (Exception e) {
                Logger.LogError(e);
            }
        }

        public void ReInit() {
            if (isActive) {
                return;
            }
            isActive = isEnableFeature && _condition.Validate();
        }
        public RewardData GetCurrentReward() {
            if (_data.gem_max == 0) {
                return new RewardData(0, 0, 0);
            }
            float ratio = _currentValue / (float)MaxReward;
            return new RewardData(_currentValue, ratio, MaxReward);
        }

        private void AddMinute() {
            if (_currentMinute >= _maxMinute) return; // no add diamond
            _currentMinute += 1;
            _currentValue = _currentMinute * ValuePerMinute;
            float ratio = _currentValue / (float)MaxReward;

            OnChangeValueReward?.Invoke(new RewardData(_currentValue, ratio, MaxReward));
        }

        public void CollectDiamond(int amount) {
            _currentMinute = 0;
            _currentValue = 0;
            _currentSeconds = 0f;
            SaveProgress();
        }

        private void SaveProgress() {
            if (!isEnableFeature) {
                //Do not save if not necessary
                return;
            }
            PlayerPrefs.SetInt(KEY_PLAYTIME_REWARD_MINUTE, _currentMinute);
            PlayerPrefs.SetFloat(KEY_PLAYTIME_REWARD_SECOND, _currentSeconds);
            Logger.EditorLog("Playtime Reward", $"save progress {_currentMinute:00} : {_currentSeconds:00}");
        }

        public void SetCountingTime(bool isCounting) {
            _isCounting = isCounting;
            Logger.EditorLog("Playtime Reward",
                $"{(isCounting ? "Resume" : "Pause")} at {_currentMinute:00}:{_currentSeconds:00}");
        }
    }

    public struct RewardData {
        public int   amount;
        public float ratio;
        public int   max;
        public bool  ReachMax => amount >= max;

        public RewardData(int amount, float ratio, int max) {
            this.amount = amount;
            this.ratio = ratio;
            this.max = max;
        }
    }
}