using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using TileHop.Cores.Pooling;
using UnityEngine;
using UnityEngine.Pool;

public class ThemeListScroll : StandardScrollerAdapter {
    public static float multiplier = 1.2f;

    [SerializeField] private bool    isSlidingUI;
    [SerializeField] private Vector2 cellSizeDefault    = new Vector2(110f, 140f);
    [SerializeField] private int     numberCellInColumn = 2;

    [SerializeField] private StandardScrollerAdapter scrollerData;
    [SerializeField] private BallCategory        category;

    [SerializeField] private ColumnThemeItem prefColumnThemeItem;
    [SerializeField] private TabScript       tabScript;

    private List<ThemeData> _listVisibleTheme; // list theme đã loại trừ các theme event
    private SelectThemes    _selectThemes;
    private ColumnThemeItem _currentColumn;

    protected override void Awake() {
        base.Awake();
        if (isSlidingUI) {
            var padding = (int)(GetComponent<RectTransform>().rect.width / 2 - cellSizeDefault.x / 2);
            scrollerData.Parameters.ContentPadding.left = padding;
            scrollerData.Parameters.ContentPadding.right = padding;
        }

        scrollerData.Init(prefColumnThemeItem.transform as RectTransform);
    }

    protected override void OnEnable() {
        scrollerData.OnItemVisible += OnScroll_OnItemVisible;
        base.OnEnable();
    }

    protected override void OnDisable() {
        scrollerData.OnItemVisible -= OnScroll_OnItemVisible;
        base.OnDisable();
    }

    private void OnScroll_OnItemVisible(StandardItemViewsHolder item) {
        if (_selectThemes == null)
            return;

        //if (data.Count < item.ItemIndex) return;
        ColumnThemeItem view = item.cellView as ColumnThemeItem;
        if (view != null) {
            List<ThemeData> datas = new ();
            for (int index = 0; index < numberCellInColumn; index++) {
                int indexCell = numberCellInColumn * item.ItemIndex + index;
                datas.Add(indexCell <= _listVisibleTheme.Count - 1 ? _listVisibleTheme[indexCell] : null);
            }

            view.Init(datas, _selectThemes.selectedThemeID, _selectThemes.clickThemeID, SelectThemes.clickViewID,
                isSlidingUI ? _selectThemes.OnSelectThemeItem : _selectThemes.OnSelectThemeItemPreview);
        }
    }

    private void InitListTheme() {
        if (_listVisibleTheme == null || _listVisibleTheme.Count == 0) {
            // list theme chưa loại trừ các theme event
            List<ThemeData> listTheme = ThemeManager.instance.GetAllThemesData();
            listTheme = UpdateThemeByConfig(listTheme);
            ProcessThemeDatas(listTheme);
        }
    }

    public void ReloadPopup(bool isAutoScroll = false) {
        InitListTheme();
        Configuration.instance.StartCoroutine(SetData(isAutoScroll));
    }

    private IEnumerator SetData(bool isAutoScroll) {
        while (!scrollerData.IsInitialized) {
            yield return YieldPool.GetWaitForEndOfFrame();
        }

        int total = _listVisibleTheme.Count / numberCellInColumn;
        if (_listVisibleTheme.Count % numberCellInColumn != 0) {
            total += 1;
        }

        List<IData> temp = new List<IData>();
        for (int i = 0; i < total; i++) {
            temp.Add(new IData());
        }

        scrollerData.SetItems(temp);

        int indexView = GetIndexOfView(SelectThemes.clickViewID);
        if (indexView < 0) {
            indexView = 0;
        }
        
        int indexData = indexView / numberCellInColumn;
        indexData = Math.Clamp(indexData, 0, temp.Count - 1);
        if (isSlidingUI) {
            RequestChangeItemSizeAndUpdateLayout(indexData, cellSizeDefault.x * multiplier);
        }

        if (isAutoScroll) {
            scrollerData.ScrollTo(indexData, 0.5f, 0.5f);
        }
    }

    #region Process Theme Data

    private void IgnoreEventTheme(List<ThemeData> listTheme) {
        for (int index = 0; index < listTheme.Count; index++) {
            ThemeData themeData = listTheme[index];
            if (themeData.GetUnlockType() == UnlockType.Event && !themeData.IsOpenTheme()) {
                listTheme.Remove(themeData);
                index--;
            }
        }
    }

    private void IgnoreStarterPackTheme(List<ThemeData> listCharacter) {
        for (int index = 0; index < listCharacter.Count; index++) {
            ThemeData themeData = listCharacter[index];
            if (themeData.GetUnlockType() == UnlockType.StarterPack && !themeData.IsOpenTheme()) {
                listCharacter.Remove(themeData);
                index--;
            }
        }
    }
    
    private void ProcessThemeDatas(List<ThemeData> data) {
        _listVisibleTheme = new List<ThemeData>();
        
        var defaultThemeId = ThemeManager.instance.DefaultThemeId;
        if (!ThemeManager.instance.IsOpenTheme(defaultThemeId)) {
            foreach (ThemeData themeData in data) {
                if (themeData.id == defaultThemeId) {
                    var defaultThemeData = themeData.Clone();
                    defaultThemeData.viewId = -1;
                    _listVisibleTheme.Add(defaultThemeData);
                    break;
                }
            }
        }
        
        foreach (ThemeData themeData in data) {
            if (!ThemeManager.IsIgnoreTheme(themeData)) {
                themeData.viewId = themeData.id;
                _listVisibleTheme.Add(themeData);
            }
        }
    }
    
    #endregion
    

    private List<ThemeData> UpdateThemeByConfig(List<ThemeData> listTheme) {
        List<ThemeData> themeDatas = new ();
        List<int> themeIDs = ThemeManager.instance.GetThemeIDs();
        if (themeIDs != null && themeIDs.Count > 0) {
            var cachedIndexes = HashSetPool<int>.Get();
            foreach (int id in themeIDs) {
                ThemeData findTheme = listTheme.FirstOrDefault(themeData => themeData.id == id);
                if (findTheme == null) {
                    continue;
                }

                if (!cachedIndexes.Contains(id)) {
                    themeDatas.Add(findTheme);
                    cachedIndexes.Add(id);
                } else {
                    Logger.LogError($"UpdateThemeByConfig has the same Id Theme in the configs {id}");
                }
            }
            HashSetPool<int>.Release(cachedIndexes);
        }

        return themeDatas.Count > 0 ? themeDatas : listTheme;
    }

    public ThemeData GetThemeDataByID(int id) {
        if (_listVisibleTheme != null) {
            foreach (ThemeData themeData in _listVisibleTheme) {
                if (themeData.id == id) {
                    return themeData;
                }
            }
        }

        return null;
    }

    public void Show(Direction direction, SelectThemes shopScript) {
        _selectThemes = shopScript;
        tabScript.Show(direction);
        ReloadPopup(true);
    }

    public void Hide(Direction direction) {
        tabScript.Hide(direction);
    }

    public int GetIndexOf(int idTheme) {
        InitListTheme();
        for (int index = 0; index < _listVisibleTheme.Count; index++) {
            ThemeData data = _listVisibleTheme[index];
            if (data.id == idTheme) {
                return index;
            }
        }

        return -1;
    }
    
    public int GetIndexOfView(int idView) {
        InitListTheme();
        for (int index = 0; index < _listVisibleTheme.Count; index++) {
            ThemeData data = _listVisibleTheme[index];
            if (data.viewId == idView) {
                return index;
            }
        }

        return -1;
    }
}