using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public struct SelectThemeParam
{
    public const string see_all      = "see_all";
    public const string action_phase = "action_phase";
    public const string free         = "free";
    public const string diamond_Y    = "diamond_{0}";
    public const string play_song    = "play_song";
    public const string ads_Y        = "ads_{0}";
    public const string VIP          = "VIP";
    public const string unlocked     = "unlocked";
    public const string locked       = "lock";
    public const string ads_order_X_per_Y = "ads_order_{0}_per_{1}";
}

public class TrackingSelectTheme
{
    private enum Params
    {
        theme_id,
        theme_unlock_type,
        theme_unlock_status,
        see_all_time
    }
    
    public static void LogEventButtonClick()
    {
        AnalyticHelper.LogEvent(TRACK_NAME.themeui_ap_button_click);
    }

    public static void LogEventThumbnailClick(int themeId, string location, string unlockType, string unlockStatus)
    {
        Dictionary<string, object> param = new Dictionary<string, object>()
        {
            {Params.theme_id.ToString(), themeId},
            {TRACK_PARAM.location, location},
            {Params.theme_unlock_type.ToString(), unlockType},
            {Params.theme_unlock_status.ToString(), unlockStatus},
        };
        AnalyticHelper.LogEvent(TRACK_NAME.themeui_thumbnail_click, param);

    }

    public static void LogEventPreviewImpression(int themeId)
    {
        Dictionary<string, object> param = new Dictionary<string, object>()
        {
            {Params.theme_id.ToString(), themeId},
        };
        AnalyticHelper.LogEvent(TRACK_NAME.themeui_preview_impression, param);
    }

    public static void LogEventItemLoad()
    {
        int themeId = ThemeManager.GetPlayingThemeId();
        ThemeConfig themeConfig = ThemeManager.instance.GetThemeConfig(themeId);
        if (themeConfig != null) {
            Dictionary<string, object> param = new Dictionary<string, object>
            {
                {Params.theme_id.ToString(), themeId },
                {Params.theme_unlock_type.ToString(), GetThemeUnlockParam(themeConfig)},
                { "loading_duration", Time.realtimeSinceStartup - SelectThemes.time_themeui_item_click }
            };
            AnalyticHelper.LogEvent(TRACK_NAME.themeui_item_load, param);
        }
    }

    public static void LogEventFeatureTime(int time)
    {
        Dictionary<string, object> param = new Dictionary<string, object>()
        {
            {TRACK_NAME.time, time},
        };
        AnalyticHelper.LogEvent(TRACK_NAME.feature_session_time, param);
    }
    
    public static void LogEventSeeAllClick(string location)
    {
        Dictionary<string, object> param = new Dictionary<string, object>()
        {
            {TRACK_PARAM.location, location},
        };
        AnalyticHelper.LogEvent(TRACK_NAME.see_all_click, param);
    }
    
    public static void LogEventSeeAllImpression()
    {
        AnalyticHelper.LogEvent(TRACK_NAME.see_all_impression);
    }
    
    public static void LogEventSeeAllOut(int time)
    {
        Dictionary<string, object> param = new Dictionary<string, object>()
        {
            {Params.see_all_time.ToString(), time},
        };
        AnalyticHelper.LogEvent(TRACK_NAME.see_all_out, param);
    }
    
    public static void LogEventEquip(int id, string location, string unlockType) {
        Dictionary<string, object> param = new Dictionary<string, object> {
            {Params.theme_id.ToString(), id},
            {TRACK_PARAM.location, location},
            {Params.theme_unlock_type.ToString(), unlockType},
        };
        AnalyticHelper.LogEvent(TRACK_NAME.themeui_equipped, param);
    }

    public static string GetThemeUnlockParam(ThemeConfig themeConfig)
    {
        var unlockType = themeConfig.unlockType;
        switch (unlockType)
        {
            case UnlockType.Open:
                return SelectThemeParam.free;
            case UnlockType.Vip:
            case UnlockType.VipMission:
                return SelectThemeParam.VIP;
            case UnlockType.Video:
                return string.Format(SelectThemeParam.ads_Y, themeConfig.ads);
            case UnlockType.Diamond:
                return string.Format(SelectThemeParam.diamond_Y, themeConfig.diamond);
            default:
                return "";
        }
    }
}
