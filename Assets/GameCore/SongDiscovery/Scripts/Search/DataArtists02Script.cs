using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using TileHop.Cores.Pooling;
using UnityEngine;
using UnityEngine.UI;

public class DataArtists02Script : DataCatalogs {
    #region Fields
    //public
    [Header("Config")] [SerializeField] private StandardScrollerAdapter scrollerAdapter;
    [SerializeField]                    private OptimizedCellView   prefCellView;

    [SerializeField] private Button btnSeeAll;

    //private
    private List<string> _allArtist; //name of artist

    private List<IData> _datas;
    #endregion

    #region Unity Method

    protected override void Awake() {
        base.Awake();
        scrollerAdapter.Init(prefCellView.transform as RectTransform);
        //load srtists
        _allArtist = SongManager.instance.GetAllArtistFromRemote();

        if (_allArtist.Count == 0) {
            Destroy(gameObject);
            return;
        }
        btnSeeAll.onClick.AddListener(btnSeeAllOnClick);
    }
    private void Start() {
        _datas = new List<IData>();
        var data = _allArtist.Take(Mathf.Min(9, _allArtist.Count - 1));
        foreach (var item in data) {
            _datas.Add(new HeaderData(item));
        }
        Configuration.instance.StartCoroutine(SetData());
    }

    private void OnEnable() {
        scrollerAdapter.OnItemVisible += OnScroll_OnItemVisible;
    }

    private void OnDisable() {
        scrollerAdapter.OnItemVisible -= OnScroll_OnItemVisible;
    }
    #endregion

    #region OptimizedScroller Handlers
    private void OnScroll_OnItemVisible(StandardItemViewsHolder item) {
        if (_datas.Count < item.ItemIndex) return;
        if (item.cellView is ItemArtistScript view) {
            string artist = ((HeaderData) _datas[item.ItemIndex]).Title;
            view.ordering = item.ItemIndex +1;
            view.SetImage(this, artist);
            view.SetArtistName(artist);
            view.onButtonClick = () => {
                SoundManager.PlayGameButton();
                catalogs02SearchScript.OpenArtist(artist);
            };
        }
    }
    private IEnumerator SetData() {
        while (!scrollerAdapter.IsInitialized) {
            yield return YieldPool.GetWaitForEndOfFrame();
        }
        scrollerAdapter.SetItems(_datas);
    }
    #endregion
    private void btnSeeAllOnClick() {
        SoundManager.PlayGameButton();
        catalogs02SearchScript.OpenAllArtist();
        SearchScript.LogEventSeeAllClick(DiscoveryLocation.discover_artist.ToString());
    }
}