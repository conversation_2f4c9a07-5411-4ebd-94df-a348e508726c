using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Com.TheFallenGames.OSA.Core;
using Com.TheFallenGames.OSA.DataHelpers;
using UnityEngine;

public class DataSearchScrollAdapter : OSA<DataSearchParams, DataSearchViewsHolder> {
    // Helper that stores data and notifies the adapter when items count changes
    // Can be iterated and can also have its elements accessed by the [] operator
    public SimpleDataHelper<IData>             Data { get; private set; }
    public event Action<DataSearchViewsHolder> OnItemVisible;

    public void Init(RectTransform prefHeaderSong, RectTransform prefSongItem, RectTransform prefRowButtonScript,
        RectTransform prefRowScrollViewArtists) {
        Parameters.prefHeaderSong = prefHeaderSong;
        Parameters.prefSongItem = prefSongItem;
        Parameters.prefRowButtonScript = prefRowButtonScript;
        Parameters.prefRowScrollViewArtists = prefRowScrollViewArtists;
        // remote config cho sự giảm tốc của scroller
        Parameters.effects.InertiaDecelerationRate =
            Mathf.Clamp01(RemoteConfig.instance.Scroller_DeclearationRate);
        // remote config cho bật / tắt navigation
        if (Parameters.Scrollbar != null) {
            Parameters.Scrollbar.gameObject.SetActive(RemoteConfig.instance.ScrollerNavigation_IsEnable);
        }

        Parameters.isInited = true;
    }

    #region OSA implementation

    protected override void Start() {
        Data = new SimpleDataHelper<IData>(this);
        base.Start();
    }

    // This is called initially, as many times as needed to fill the viewport,
    // and anytime the viewport's size grows, thus allowing more items to be displayed
    // Here you create the "ViewsHolder" instance whose views will be re-used
    // *For the method's full description check the base implementation
    protected override DataSearchViewsHolder CreateViewsHolder(int itemIndex) {
        var modelType = Data[itemIndex].GetType(); // _ModelTypes[itemIndex];
        if (modelType == typeof(HeaderSong)) {
            var vh = new DataSearchHeaderViewsHolder();
            vh.Init(_Params.prefHeaderSong, _Params.Content, itemIndex);
            return vh;
        }

        if (modelType == typeof(Song)) {
            var vh = new DataSearchSongViewsHolder();
            vh.Init(_Params.prefSongItem, _Params.Content, itemIndex);
            return vh;
        }

        if (modelType == typeof(RowArtists)) {
            var vh = new DataSearchScrollViewArtistViewsHolder();
            vh.Init(_Params.prefRowScrollViewArtists, _Params.Content, itemIndex);
            return vh;
        }

        if (modelType == typeof(RowButton)) {
            var vh = new DataSearchRowButtonViewsHolder();
            vh.Init(_Params.prefRowButtonScript, _Params.Content, itemIndex);
            return vh;
        }

        //default value
        var vh2 = new DataSearchHeaderViewsHolder();
        vh2.Init(_Params.prefHeaderSong, _Params.Content, itemIndex);
        return vh2;
    }

    private Dictionary<int, bool> _resizeDicts = new Dictionary<int, bool>();

    protected override void Update() {
        base.Update();
        int count = VisibleItemsCount;
        for (int i = 0; i < count; i++) {
            var item = GetItemViewsHolder(i);
            if (item != null && !_resizeDicts.ContainsKey(item.ItemIndex)) {
                RequestChangeItemSizeAndUpdateLayout(item.ItemIndex, item.SizeDelta.y, false, true, false, true);
                _resizeDicts.Add(item.ItemIndex, true);
            }
        }
    }

    // This is called anytime a previously invisible item become visible, or after it's created,
    // or when anything that requires a refresh happens
    // Here you bind the data from the model to the item's views
    // *For the method's full description check the base implementation
    protected override void UpdateViewsHolder(DataSearchViewsHolder newOrRecycled) {
        // In this callback, "newOrRecycled.ItemIndex" is guaranteed to always reflect the
        // index of item that should be represented by this views holder. You'll use this index
        // to retrieve the model from your data set

        var modelData = Data[newOrRecycled.ItemIndex];
        newOrRecycled.cellView.SetData(modelData);
        OnItemVisible?.Invoke(newOrRecycled);
#if UNITY_EDITOR
        newOrRecycled.cellView.gameObject.name = $"Item {newOrRecycled.ItemIndex}";
#endif
    }

    protected override bool IsRecyclable(DataSearchViewsHolder potentiallyRecyclable,
        int indexOfItemThatWillBecomeVisible, double sizeOfItemThatWillBecomeVisible) {
        var model = Data[indexOfItemThatWillBecomeVisible];
        return potentiallyRecyclable.CanPresentModelType(model.GetType());
    }
    // This is the best place to clear an item's views in order to prepare it from being recycled, but this is not always needed,
    // especially if the views' values are being overwritten anyway. Instead, this can be used to, for example, cancel an image
    // download request, if it's still in progress when the item goes out of the viewport.
    // <newItemIndex> will be non-negative if this item will be recycled as opposed to just being disabled
    // *For the method's full description check the base implementation

    protected override void OnBeforeRecycleOrDisableViewsHolder(DataSearchViewsHolder inRecycleBinOrVisible,
        int newItemIndex) {
        if (_resizeDicts.ContainsKey(inRecycleBinOrVisible.ItemIndex)) {
            _resizeDicts.Remove(inRecycleBinOrVisible.ItemIndex);
        }

        base.OnBeforeRecycleOrDisableViewsHolder(inRecycleBinOrVisible, newItemIndex);
    }


    // You only need to care about this if changing the item count by other means than ResetItems,
    // case in which the existing items will not be re-created, but only their indices will change.
    // Even if you do this, you may still not need it if your item's views don't depend on the physical position
    // in the content, but they depend exclusively to the data inside the model (this is the most common scenario).
    // In this particular case, we want the item's index to be displayed and also to not be stored inside the model,
    // so we update its title when its index changes. At this point, the Data list is already updated and
    // shiftedViewsHolder.ItemIndex was correctly shifted so you can use it to retrieve the associated model
    // Also check the base implementation for complementary info
    /*
    protected override void OnItemIndexChangedDueInsertOrRemove(MyListItemViewsHolder shiftedViewsHolder, int oldIndex, bool wasInsert, int removeOrInsertIndex)
    {
        base.OnItemIndexChangedDueInsertOrRemove(shiftedViewsHolder, oldIndex, wasInsert, removeOrInsertIndex);

        shiftedViewsHolder.titleText.text = Data[shiftedViewsHolder.ItemIndex].title + " #" + shiftedViewsHolder.ItemIndex;
    }
    */

    #endregion

    // These are common data manipulation methods
    // The list containing the models is managed by you. The adapter only manages the items' sizes and the count
    // The adapter needs to be notified of any change that occurs in the data list. Methods for each
    // case are provided: Refresh, ResetItems, InsertItems, RemoveItems

    #region data manipulation

    public void AddItemsAt(int index, IList<IData> items) {
        // Commented: the below 2 lines exemplify how you can use a plain list to manage the data, instead of a DataHelper, in case you need full control
        //YourList.InsertRange(index, items);
        //InsertItems(index, items.Length);

        Data.InsertItems(index, items);
    }
    public void AddItemAtStart(IData item) {
        Data.InsertOneAtStart(item,true);
    }
    public void AddItemAtEnd(IData item) {
        Data.InsertOneAtEnd(item,true);
    }
    public void AddItemsAtEnd(IList<IData> items) {
        Data.InsertItems(Data.Count, items);
    }
    public void SetItem(int index, IData item) {
        Data.RemoveOne(index);
        Data.InsertOne(index,item);
    }

    public void RemoveItemsFrom(int index, int count) {
        // Commented: the below 2 lines exemplify how you can use a plain list to manage the data, instead of a DataHelper, in case you need full control
        //YourList.RemoveRange(index, count);
        //RemoveItems(index, count);

        Data.RemoveItems(index, count);
    }

    public void SetItems(IList<IData> items) {
        // Commented: the below 3 lines exemplify how you can use a plain list to manage the data, instead of a DataHelper, in case you need full control
        //YourList.Clear();
        //YourList.AddRange(items);
        //ResetItems(YourList.Count);
        Data.ResetItems(items);
    }

    #endregion
}

/// <summary>Contains the 2 prefabs associated with the 2 views holders</summary>
[Serializable] // serializable, so it can be shown in inspector
public class DataSearchParams : BaseParams {
    public bool          isInited;
    public RectTransform prefHeaderSong;
    public RectTransform prefSongItem;
    public RectTransform prefRowButtonScript;
    public RectTransform prefRowScrollViewArtists;

    public override void InitIfNeeded(IOSA iAdapter) {
        base.InitIfNeeded(iAdapter);
        if (!isInited) return;
        AssertValidWidthHeight(prefHeaderSong);
        AssertValidWidthHeight(prefSongItem);
        AssertValidWidthHeight(prefRowButtonScript);
        AssertValidWidthHeight(prefRowScrollViewArtists);
    }
}

public class DataSearchViewsHolder : BaseItemViewsHolder {
    public OptimizedCellView cellView;

    public virtual Vector2 SizeDelta => Vector2.zero;

    // Retrieving the views from the item's root GameObject
    public override void CollectViews() {
        root.sizeDelta = SizeDelta;
        base.CollectViews();
        cellView = root.GetComponent<OptimizedCellView>();
    }

    public virtual bool CanPresentModelType(Type modelType) {
        return true;
    }
}

public class DataSearchHeaderViewsHolder : DataSearchViewsHolder {
    public override Vector2 SizeDelta => new Vector2(480, 50);

    public override bool CanPresentModelType(Type modelType) {
        return modelType == typeof(HeaderSong);
    }
}

public class DataSearchSongViewsHolder : DataSearchViewsHolder {
    public override Vector2 SizeDelta => new Vector2(430, 90);

    public override bool CanPresentModelType(Type modelType) {
        return modelType == typeof(Song);
    }
}

public class DataSearchRowButtonViewsHolder : DataSearchViewsHolder {
    public override Vector2 SizeDelta => new Vector2(450, 75);

    public override bool CanPresentModelType(Type modelType) {
        return modelType == typeof(RowButton);
    }
}

public class DataSearchScrollViewArtistViewsHolder : DataSearchViewsHolder {
    public override Vector2 SizeDelta => new Vector2(480, 140);

    public override bool CanPresentModelType(Type modelType) {
        return modelType == typeof(RowArtists);
    }
}