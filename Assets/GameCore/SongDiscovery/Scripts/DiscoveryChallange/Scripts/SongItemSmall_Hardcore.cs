using UnityEngine;

public class SongItemSmall_Hardcore : SongItemSmall {
    [SerializeField] private DifficultyTagUI difficultyTag;
    [SerializeField] private GameObject      iconCrown;

    public override void SetSong(Song item, string locationSong) {
        base.SetSong(item, locationSong);
        difficultyTag.ShowTag(song);
    }

    protected override void UpdateStar(bool isOpened) {
        int stars = Configuration.GetBestStars(songID);
        groupStar.SetActive(true);
        imgStar01.sprite = stars >= 1 ? sprStarOn : sprStarOff;
        imgStar02.sprite = stars >= 2 ? sprStarOn : sprStarOff;
        imgStar03.sprite = stars >= 3 ? sprStarOn : sprStarOff;
        if (iconCrown) {
            iconCrown.SetActive(stars >= 3);
        }
    }
}