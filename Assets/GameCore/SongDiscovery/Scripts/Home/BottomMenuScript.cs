using System;
using System.Collections;
using TileHop.Cores.Pooling;
using TileHop.DiscoveryChallenge;
using TileHop.LiveEvent;
using UnityEngine;
using UnityEngine.UI;

public class BottomMenuScript : MonoBehaviour {
#if UNITY_ANDROID
    public static float heightBottomMenu = 75.1f;
#else
    public static float heightBottomMenu = 85f;
#endif
    public enum Type {
        Home,
        Search,
        Premium,
        LiveEvent,
        ShopIAP,
        ShopBall,
    }

    [SerializeField]
    protected MenuBottomButton menuHome;

    [SerializeField]
    protected MenuBottomButton menuSearch;
    
    protected MenuBottomButton      menuLiveEvent;
    private   UIBottomMenuLiveEvent _btnLiveEvent;

    [SerializeField] private Transform groupButton;

    [SerializeField]
    protected RectTransform rectBottomMenu;

    [SerializeField] private RectTransform rectBottomMenuBg;

    [SerializeField] protected Color32 colorOn;
    [SerializeField] protected Color32 colorOff;

    public Action btnHomeOnClick;
    public Action btnSearchOnClick;
    public Action btnPremiumOnClick;
    public Action btnLiveEventOnClick;

    [SerializeField] private BlackBGTooltip bgToolTip;
    [SerializeField] private Image          decalImage;

    protected bool                 isShowingEvent;
    private   LiveEventFeatureType _eventType;
    private   int                  _idLiveEventShowToolTip;

    [HideInInspector] public bool isInited = false;

    public bool IsNeedShowExploreNotice =>
        RemoteConfig.instance.Onboarding_Home_ExploreNotice_Enable &&
        PlayerPrefs.GetInt(CONFIG_STRING.OnboardingFeature_ExploreAppear, 0) == 0 &&
        RemoteConfig.instance.Onboarding_Home_ExploreNotice_LevelAppear <= Configuration.GetGameLevel();
    
    public Transform TfSearch => menuSearch.mainButton.transform;

    public static bool IsLockedAction = false;

    #region Unity Methods

    protected virtual void Awake() {
        menuHome.mainButton.onClick.AddListener(OnBtnHomeClick);
        menuSearch.mainButton.onClick.AddListener(OnBtnSearchClick);
        SetHeight();
        LocalizationManager.instance.OnLanguageChange += UpdateTextLiveEvent;
    }

    protected virtual void OnEnable() {
        BlackBGTooltip.OnClickAction += BlackBgTooltipOnOnClick;
        LiveEventManager.OnInitCompleted += LiveEvent_CheckActive;
        DiscoveryChallengeManager.OnEventStateChange += DiscoveryChallengeManagerOnOnEventStateChange;
        LiveEvent_CheckActive();
        CheckNoticeDiscover(DiscoveryChallengeManager.isInstanced
            ? DiscoveryChallengeManager.instanceSafe.EventState
            : ChallengeState.Disable);
        CalculateSizeBottom();
    }

    protected void Start() {
        if (decalImage != null) {
            UIHomeDecorationController.TryDecorBottomMenu(decalImage);
        }
    }

    protected virtual void OnDisable() {
        BlackBGTooltip.OnClickAction -= BlackBgTooltipOnOnClick;
        LiveEventManager.OnInitCompleted -= LiveEvent_CheckActive;
        DiscoveryChallengeManager.OnEventStateChange -= DiscoveryChallengeManagerOnOnEventStateChange;
    }

    protected virtual void OnDestroy() {
        if (LocalizationManager.instance != null) {
            LocalizationManager.instance.OnLanguageChange -= UpdateTextLiveEvent;
        }

        if (isShowingEvent) {
            UnregisterLiveEventEvents();
        }
    }

    #endregion

    #region Delegate Methods

    private bool CreateButtonLiveEvent() {
        if (_btnLiveEvent == null) {
            var uiButton = Resources.Load<UIBottomMenuLiveEvent>("icons/btnLiveEvent");
            _btnLiveEvent = Instantiate(uiButton, groupButton);
            _btnLiveEvent.transform.SetSiblingIndex(1);
            menuLiveEvent = _btnLiveEvent.main;
            menuLiveEvent.mainButton.onClick.AddListener(OnBtnLiveEventClick);
            menuLiveEvent.mainButton.gameObject.SetActive(true);
            return true;
        } else {
            menuLiveEvent.mainButton.gameObject.SetActive(true);
            return false;
        }
    }
    private void OnBtnLiveEventClick() {
        if (IsLockedAction) {
            Logger.Log("Locked action bottom menu");
            return;
        }

        btnLiveEventOnClick?.Invoke();
    }
    public void LiveEvent_CheckActive() {
        isShowingEvent = LiveEventManager.instance.IsActiveEvent || BallSpinnerManager.IsActiveEvent;
        if (isShowingEvent) {
            _eventType = LiveEventManager.instance.IsActiveEvent
                ? LiveEventFeatureType.Old2023
                : LiveEventFeatureType.BallSpinner;
            var isCreateNew = CreateButtonLiveEvent();
            if (isCreateNew) {
                RegisterLiveEventEvents();
            }

            CheckNoticeLiveEvent();
            UpdateTextLiveEvent();
        } else {
            menuLiveEvent?.mainButton.gameObject.SetActive(false);
        }

        if (isShowingEvent) { }

        CalculateSizeBottom();
        isInited = true;
    }

    private void UnregisterLiveEventEvents() {
        switch (_eventType) {
            case LiveEventFeatureType.Old2023:
                LiveEventManager.OnUnlockSong -= LiveEventManagerOnOnUnlockSong;
                LiveEventManager.OnChangeNoticeState -= CheckNoticeLiveEvent;
                break;

            case LiveEventFeatureType.BallSpinner:
                BallSpinnerManager.OnChangeData -= CheckNoticeLiveEvent;
                break;

            default:
                Logger.EditorLogError("Not handle this!");
                break;
        }
    }

    private void RegisterLiveEventEvents() {
        switch (_eventType) {
            case LiveEventFeatureType.Old2023:
                LiveEventManager.OnUnlockSong += LiveEventManagerOnOnUnlockSong;
                LiveEventManager.OnChangeNoticeState += CheckNoticeLiveEvent;
                break;

            case LiveEventFeatureType.BallSpinner:
                BallSpinnerManager.OnChangeData += CheckNoticeLiveEvent;
                break;

            default:
                Logger.EditorLogError("Not handle this!");
                break;
        }
    }

    private void OnBtnSearchClick() {
        if (IsLockedAction) {
            Logger.Log("Locked action bottom menu");
            return;
        }

        btnSearchOnClick?.Invoke();
    }

    private void OnBtnHomeClick() {
        if (IsLockedAction) {
            Logger.Log("Locked action bottom menu");
            return;
        }

        btnHomeOnClick?.Invoke();
    }

    private void LiveEventManagerOnOnUnlockSong(int idEvent) {
        CheckNoticeLiveEvent();
    }

    private void CheckNoticeLiveEvent() {
        if (!isShowingEvent || menuLiveEvent == null) {
            return;
        }

        bool hasNotice = false;
        switch (_eventType) {
            case LiveEventFeatureType.Old2023:
                hasNotice = LiveEventManager.instance.HaveNotice;
                break;

            case LiveEventFeatureType.BallSpinner:
                hasNotice = BallSpinnerManager.instanceSafe.HasNotice();
                break;

            default:
                Logger.EditorLogError("Not handle this!");
                break;
        }

        menuLiveEvent.objNotice.SetActive(hasNotice);
    }

    public void CheckNoticeDiscover(ChallengeState state) {
        if (menuSearch.objNotice == null) {
            return;
        }

        bool isClickedAccept =
            DiscoveryChallengeManager.isInstanced && DiscoveryChallengeManager.instanceSafe.isClickedAccept;
        bool isShowNotice = HardcoreSongCollection.isInstanced && HardcoreSongCollection.instanceSafe.CanCollectMore &&
                            state is ChallengeState.Unknown or ChallengeState.Accepted;

        menuSearch.objNotice.SetActive(isShowNotice && !isClickedAccept);
    }

    private void DiscoveryChallengeManagerOnOnEventStateChange(ChallengeState state) {
        CheckNoticeDiscover(state);
    }

    #endregion

    private void UpdateTextLiveEvent() {
        if (menuLiveEvent == null) {
            return;
        }

        menuLiveEvent.txtMain.text = LocalizationManager.instance.GetLocalizedValue("EVENT").ToUpper();
        LocalizationManager.instance.UpdateFont(menuLiveEvent.txtMain);
    }

    protected virtual void CalculateSizeBottom() {
        int numberTab = 2; //home (cố định) + search (cố định)

        bool isOpenLiveEvent = isShowingEvent && menuLiveEvent != null;
        if (isOpenLiveEvent) {
            numberTab += 1; //open live event
        }

        float fullSize = 480f / numberTab;
        var sizeDelta = rectBottomMenu.sizeDelta;

        int index = 0;

        SetMenuButtonSize(menuHome.mainButton.transform, index++, numberTab, fullSize, sizeDelta);

        if (isOpenLiveEvent) {
            SetMenuButtonSize(menuLiveEvent.mainButton.transform, index++, numberTab, fullSize, sizeDelta);
            menuLiveEvent.mainButton.gameObject.SetActive(true);
        } else {
            menuLiveEvent?.mainButton.gameObject.SetActive(false);
        }

        SetMenuButtonSize(menuSearch.mainButton.transform, index++, numberTab, fullSize, sizeDelta);
        menuSearch.mainButton.gameObject.SetActive(true);
    }

    protected void SetMenuButtonSize(Transform targetTransform, int index, int numberTab, float fullSize,
                                     Vector2 sizeDelta) {
        ((RectTransform) targetTransform).sizeDelta = new Vector2(fullSize, sizeDelta.y);
        targetTransform.localPosition = Vector3.right * (fullSize * (index - (numberTab - 1) / 2f));
    }

    private void SetHeight() {
        var currentSize = rectBottomMenu.sizeDelta;
        if (!Mathf.Approximately(currentSize.x, heightBottomMenu)) {
            //Set bg bottom menu scale
            var offsetMax = rectBottomMenuBg.offsetMax;
            float newTop = offsetMax.y * heightBottomMenu / currentSize.y;
            offsetMax = new Vector2(offsetMax.x, newTop);
            rectBottomMenuBg.offsetMax = offsetMax;

            //Set new bottom menu height
            currentSize.y = heightBottomMenu;
            rectBottomMenu.sizeDelta = currentSize;
        }
    }

    public void SetActive(bool isActive) {
        gameObject.SetActive(isActive);
    }

    public virtual void HighLight(Type type, bool hasAnimation = true) {
        HomeManager.CachedTabActived = type;
        menuHome.HighLight(type == Type.Home, colorOn, colorOff, hasAnimation);
        menuSearch.HighLight(type == Type.Search, colorOn, colorOff, hasAnimation);
        
        if (menuLiveEvent != null) {
            menuLiveEvent.HighLight(type == Type.LiveEvent, colorOn, colorOff, hasAnimation);
        }
    }

    private IEnumerator IEWaitInitComplete(Action callback) {
        while (!isInited) {
            yield return YieldPool.GetWaitForEndOfFrame();
        }

        yield return YieldPool.GetWaitForEndOfFrame();

        callback?.Invoke();
    }

    private void BlackBgTooltipOnOnClick() {
        if (menuSearch.isShowingTooltip) {
            if (RemoteConfig.instance.Onboarding_Home_ExploreNotice_RequiredClick) {
                return;
            }

            HideExploreNotice();
        } else if (menuLiveEvent != null && menuLiveEvent.isShowingTooltip) {
            menuLiveEvent.HideToolTip(groupButton, colorOff);
            bgToolTip.HideRequired();
        }
    }

    public void ShowExploreToolTip() {
        StartCoroutine(IEWaitInitComplete(() => {
            HomeManager.instance.blackBG.SetActive(true);
            menuSearch.ShowToolTip(colorOn);
            PlayerPrefs.SetInt(CONFIG_STRING.OnboardingFeature_ExploreAppear, 1);
        }));
    }

    public void HideExploreNotice() {
        if (!menuSearch.isShowingTooltip) {
            return;
        }

        bgToolTip.HideRequired();
        menuSearch.HideToolTip(groupButton, colorOff);
    }

    public void ShowEventToolTip(int idEvent) {
        StartCoroutine(IEWaitInitComplete(() => {
            HomeManager.instance.blackBG.SetActive(true);
            menuLiveEvent.ShowToolTip(colorOn);
            _idLiveEventShowToolTip = idEvent;
            var eventItem = LiveEventManager.instance.GetLiveEvent(idEvent);
            if (eventItem != null) {
                eventItem.CompleteShowTutorial();
                LiveEventTracker.Track_OnboardShow(eventItem.eventConfig.Id, eventItem.eventConfig.Title,
                    "home_songlist");
            }
        }));
    }

    public void HideLiveEventToolTip(int idEvent) {
        if (menuLiveEvent != null && menuLiveEvent.isShowingTooltip) {
            menuLiveEvent.HideToolTip(groupButton, colorOff);
            bgToolTip.HideRequired();
            var eventItem = LiveEventManager.instance.GetLiveEvent(idEvent);
            if (eventItem != null) {
                LiveEventTracker.Track_OnboardClick(eventItem.eventConfig.Id, eventItem.eventConfig.Title,
                    "home_songlist");
            }
        }
    }

    public bool IsShowingLiveEventToolTip() {
        return menuLiveEvent != null && menuLiveEvent.isShowingTooltip;
    }

    public int GetIdLiveEventToolTip() {
        return _idLiveEventShowToolTip;
    }
}