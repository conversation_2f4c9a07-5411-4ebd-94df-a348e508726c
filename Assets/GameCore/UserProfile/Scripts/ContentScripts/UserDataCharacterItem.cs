using System.Collections;
using UnityEngine;
using UnityEngine.UI;
using System;
using UnityEngine.Serialization;

public class UserDataCharacterItem : OptimizedCellView {
    public Action onButtonClick;

    [SerializeField] private Image  image;
    [SerializeField] private Text   ballName;

    private void Awake() {
        LocalizationManager.instance.UpdateFont(ballName);
    }

    public void SetImage(Sprite sprite) {
        if (sprite) {
            image.sprite = sprite;
        }
    }

    public void SetActive(bool isActive) {
        gameObject.SetActive(isActive);
    }

    public void SetBallName(string displayName) {
        ballName.text = displayName;
    }

    public override void SetData(IData _data) { }
}