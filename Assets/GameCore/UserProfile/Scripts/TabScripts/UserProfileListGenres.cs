using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Inwave;
using UnityEngine;
using UnityEngine.U2D;
using UnityEngine.UI;
using System;
using TileHop.Cores.Pooling;

public class UserProfileListGenres : TabScript {
	[SerializeField] private <PERSON>ton              btnBack;
	[SerializeField] private SpriteAtlas         atlasGenres;
	[SerializeField] private StandardScrollerAdapter scrollerGenre;
	[SerializeField] private RectTransform       prefRowGenres;
	
	private List<IData>  _data;
	private List<string> _listGenres;
	
	private double wantedPosition = -1f;

	#region Unity Methods

	protected override void Awake() {
		base.Awake();

		btnBack.onClick.AddListener(BtnBackOnClick);
		scrollerGenre.Init(prefRowGenres.transform as RectTransform);
	}

	private void OnEnable() {
		scrollerGenre.OnItemVisible += OnScroll_OnItemVisible;
		SoundManager.PlaySFX_PopupOpen();
	}

	public override void Start() {
		base.Start();
		LoadGenres();
	}

	protected override void OnDisable() {
		scrollerGenre.OnItemVisible -= OnScroll_OnItemVisible;
		base.OnDisable();
	}

	#endregion

	public override void Hide(Direction fromDirection, Action onHideDone = null)
	{
		base.Hide(fromDirection, onHideDone);
		string update = string.Join(FileHelper.Split, UserProfilePage.instance.UserEntry.favoriteGenres.ToArray());
		update = UserEntry.FormatFavoriteGenresString(update);
		PlayerPrefs.SetString(FB_DB_KEY.favoriteGenres, update);
	}

	void LoadGenres() {
		_listGenres = new List<string>();
		string strListGenresRemote = RemoteConfig.instance.SongDiscovery_ListGenres;
		if (!string.IsNullOrEmpty(strListGenresRemote)) {
			List<string> listGenreRemote = strListGenresRemote.ToUpper().Replace("/", "_").Split(';').ToList();
			if (listGenreRemote.Count > 0) {
				foreach (string genre in listGenreRemote.Where(genre => !string.IsNullOrEmpty(genre))) {
					_listGenres.Add(genre);
				}
			}
		}

		_data = new List<IData>();

		int totalRowGenre = (_listGenres.Count + 1) / 2;

		for (int i = 0; i < totalRowGenre; i++) {
			int index1 = 2 * i;
			int index2 = index1 + 1;

			string name1 = index1 >= _listGenres.Count ? null : _listGenres[index1];
			string name2 = index2 >= _listGenres.Count ? null : _listGenres[index2];

			_data.Add(new RowGenres(name1, GetSpriteByName(name1), name2, GetSpriteByName(name2)));
		}

		Configuration.instance.StartCoroutine(SetData());
	}

	private IEnumerator SetData() {
		while (!scrollerGenre.IsInitialized) {
			yield return YieldPool.GetWaitForEndOfFrame();
		}

		scrollerGenre.SetItems(_data);
		if (wantedPosition >= 0) {
			scrollerGenre.SetNormalizedPosition(wantedPosition);
			wantedPosition = -1;
		}
	}
	
	void BtnBackOnClick() {
		SoundManager.PlayGameButton();
		SoundManager.PlaySFX_PopupClose();
		UserProfilePage.instance.HideAllGenres();
	}
	
	private void OnScroll_OnItemVisible(StandardItemViewsHolder item) {
		if (_data.Count < item.ItemIndex) return;
		UserProfileRowGenre view = item.cellView as UserProfileRowGenre;
		if (view != null) {
			RowGenres rowArtists = (RowGenres)_data[item.ItemIndex];
			bool isSelected1 = UserProfilePage.instance.UserEntry.favoriteGenres.Contains(rowArtists.genreName01);
			bool isSelected2 = UserProfilePage.instance.UserEntry.favoriteGenres.Contains(rowArtists.genreName02);
			view.SetData(rowArtists, isSelected1, isSelected2);
		}
	}
	
	private Sprite GetSpriteByName(string genre) {
		Sprite sprite = atlasGenres.GetSprite(genre);

		if (sprite == null) {
			sprite = atlasGenres.GetSprite(GenreType.OTHERS);
			Logger.LogWarning(
				$"[GetSpriteByName] Cannot get sprite {genre.ToColor(Color.green)} in atlas {atlasGenres.name}");
		}

		return sprite;
	}
}
