using System.Collections;
using System.Collections.Generic;
using PathCreation;
using Sirenix.OdinInspector;
using UnityEngine;

public class TestGenerateMesh : MonoBehaviour {
    [SerializeField] private PathCreator pathCreator;
    [SerializeField] private Vector3[]   nodes;

    [Button]
    private void CreateMesh() {
        pathCreator.bezierPath.Space = PathSpace.xz;
        pathCreator.bezierPath.ControlPointMode = BezierPath.ControlMode.Free;
        for (int i = 0; i < nodes.Length; i++) {
            Vector3 currentNode = nodes[i];
            if (pathCreator.bezierPath.NumSegments < nodes.Length) {
                pathCreator.bezierPath.AddSegmentToEnd(currentNode);
            } else {
                pathCreator.bezierPath.MovePoint(i, currentNode);                
            }
        }
    }

    [Button]
    private void Clear() {
        var count = pathCreator.bezierPath.NumSegments;
        for (int i = 0; i <= count; i++) {
            pathCreator.bezierPath.DeleteSegment(i);
        }
    }
}
