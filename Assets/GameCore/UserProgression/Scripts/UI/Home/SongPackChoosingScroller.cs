using System;
using System.Collections;
using System.Collections.Generic;
using Com.TheFallenGames.OSA.Core;
using Com.TheFallenGames.OSA.CustomParams;
using Com.TheFallenGames.OSA.DataHelpers;
using TileHop.Cores.Pooling;
using TileHop.Cores.UserProgression;
using UnityEngine;
using UnityEngine.EventSystems;

public class SongPackChoosingScroller : OSA<SongPackPreviewParams, SongPackPreviewViewHolder> {
    public AnimationCurve                      curveItemHolderPositionY;
    public bool                                IsReady = false;
    public SimpleDataHelper<SongPackDataGroup> Data;
    public event Action BeginDrag;
    public event Action EndDrag;

    protected override void Start() {
        Data = new SimpleDataHelper<SongPackDataGroup>(this);
        base.Start();
    }

    public override void OnBeginDrag(PointerEventData eventData) {
        base.OnBeginDrag(eventData);
        BeginDrag?.Invoke();
    }

    public override void OnEndDrag(PointerEventData eventData) {
        base.OnEndDrag(eventData);
        EndDrag?.Invoke();
    }

    public void SetData(List<SongPackDataGroup> data) {
        Data.ResetItems(data);
        ResetItems(Data.Count);
        IsReady = true;
    }

    public void ScrollTo(int index, float duration = .5f, Action onDone = null) {
        if (index < 0 || index >= Data.Count)
            return;
        SmoothScrollTo(index, duration, 0.5f, 0.5f, onDone: onDone);
    }
    #region OSA implementation

    protected override SongPackPreviewViewHolder CreateViewsHolder(int itemIndex) {
        var instance = new SongPackPreviewViewHolder();
        instance.Init(_Params.ItemPrefab, _Params.Content, itemIndex);

        return instance;
    }

    protected override void Update() {
        base.Update();

        if (!IsInitialized)
            return;

        if (VisibleItemsCount == 0)
            return;

        //int middleVHIndex = VisibleItemsCount / 2;
        //var middleVH = GetItemViewsHolder(middleVHIndex);
        //(middleVH.cellView as DiskCellView).canvasGroup.DOFade(_Params.selectedAlpha, .1f);

        DiskCellView cellView;
        float curved;
        for (int i = 0; i < VisibleItemsCount; ++i) {
            cellView = (DiskCellView) GetItemViewsHolder(i).cellView;

            curved = curveItemHolderPositionY.Evaluate(Mathf.Clamp(Mathf.Abs(cellView.tfMainDisk.position.x / 3f), 0f,
                1f));

            cellView.tfMainDisk.localPosition = Vector3.up * (_Params.itemLowPositionY * curved);
            cellView.canvasGroup.alpha = (1f - curved) * (_Params.selectedAlpha - _Params.nonSelectedAlpha) +
                                         _Params.nonSelectedAlpha;
        }
    }

    protected override void UpdateViewsHolder(SongPackPreviewViewHolder newOrRecycled) {
        int stage = _Params.GetItemValueAtIndex(newOrRecycled.ItemIndex);
        var data = Data[stage];
        newOrRecycled.cellView.SetData(new DiskCellData(data));
    }

    #endregion
}

[Serializable]
public class SongPackPreviewParams : BaseParamsWithPrefab {
    public float itemLowPositionY = -30f;
    public int   startItemNumber  = 0;
    public int   increment        = 1;
    public float selectedAlpha, nonSelectedAlpha;

    /// <summary>The value of each item is calculated dynamically using its <paramref name="index"/>, <see cref="startItemNumber"/> and the <see cref="increment"/></summary>
    /// <returns>The item's value (the displayed number)</returns>
    public int GetItemValueAtIndex(int index) {
        return startItemNumber + increment * index;
    }
}

public class SongPackPreviewViewHolder : BaseItemViewsHolder {
    public OptimizedCellView cellView;

    /// <inheritdoc/>
    public override void CollectViews() {
        base.CollectViews();
        root.TryGetComponent(out cellView);
    }
}