using GameCore;
using TileHop.Cores.UserProgression;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class SongPackHeaderBaseItem : OptimizedCellView {
    public                     SongPackThemeInfo themeInfo;
    [SerializeField] protected TextMeshProUGUI   hintText;
    [SerializeField] protected GameObject        hintTextContainer;
    [SerializeField] protected Text              txtSongPackInfo;
    [SerializeField] protected RectTransform     rtfItem;
    [SerializeField] protected Image             headerLine;

    public GameObject packInfoContainer;

    protected int packId = -1;
    
    protected virtual void Awake() {
        LocalizationManager.instance.OnLanguageChange += UpdateLocalizedLabel;
        UpdateLocalizedLabel();
    }

    private void UpdateLocalizedLabel() { }
    
    public virtual void UpdateAnchor() {
        rtfItem.anchorMin = Vector2.up; //(0f, 1f);
        rtfItem.anchorMax = Vector2.one; //(1f, 1f);
        rtfItem.SetAnchoredPosition3DPosX(-20);
    }
    
    public override void SetData(IData data) { }
    
    protected virtual void SetupData(int packLevel) {
        packId = packLevel;
        int themeId = UserProgressionController.instanceSafe.GetIdTheme(packLevel);
        themeInfo = UserProgressionController.instanceSafe.packThemeData.GetInfo(themeId);
        txtSongPackInfo.text = Util.BuildString(" - ", packLevel, themeInfo.themeName.ToUpper());

        if (hintText) {
            int tilesToUnlock = UserProgressionController.instanceSafe.GetTargetExpForNextLevel(packLevel);
            int tilesCurrent = UserProgressionController.instanceSafe.levelManager.CurrentExp;

            if (tilesToUnlock > tilesCurrent) {
                const string key = "MORE_TILES_TO_UNLOCK";
                if (LocalizationManager.instance.TryGetLocalizedValue(key, out string formatPattern)) {
                    hintText.text = string.Format(formatPattern, tilesToUnlock - tilesCurrent);
                } else {
                    hintText.text = Util.BuildString(" ", tilesToUnlock - tilesCurrent, "<sprite=0> more");
                }
                
                hintTextContainer.SetActive(true);
            } else {
                hintTextContainer.SetActive(false);
            }
        }
    }
    
    protected virtual void SetupTheme() {
        if (headerLine != null) {
            headerLine.color = themeInfo.gradientLight;
        }
        
        if (hintText) {
            hintText.color = themeInfo.gradientBrilliant;
        }
    }
    
    public virtual void OnHeaderClick() {
        UserProgressionTracking.Track_LevelInfoClick(
            "songpack_id",
            UserProgressionController.instanceSafe.Level,
            packId);
    }
}
