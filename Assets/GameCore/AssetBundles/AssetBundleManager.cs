using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Networking;
using Object = UnityEngine.Object;
using Utils = Inwave.Utils;

public static class AssetBundleManager {
    private static          bool   _isGetAbFromEditor  = true;
    private static readonly string _url                = "https://d1xncywiopk9ma.cloudfront.net/Songs/AssetBundles/";
    public static readonly  string pathCardArtistSongs = "gamecore/sprites/songcards_2/artist/";
    public static readonly  string pathCharacters      = "gamecore/characters/assetbundles/";

    private static bool   _isInit = false;
    private static string _urlAssetBundle;

    public static string GetUrl(string path) {
        if (_isInit) {
            return _urlAssetBundle + path;
        }

        _isInit = true;

        if (Application.isMobilePlatform) {
            _isGetAbFromEditor = false;
        }

        if (_isGetAbFromEditor) {
            _urlAssetBundle = $"file:///{Application.dataPath.Replace("/Assets", "/AssetBundles")}/StandaloneWindows/";

        } else {
            _urlAssetBundle = _url + (Utils.IsAndroid() ? "Android/" : "iOS/");
        }

        return _urlAssetBundle + path;
    }

    public static IEnumerator LoadAsset<T>(string pathAsset, string assetName, Action<T> onDone) where T : Object {
        bool isReturned = false;
        yield return LoadAsset<T>(pathAsset, assetName, (returnCached => {
            if (returnCached != null && !isReturned) {
                onDone?.Invoke(returnCached);
                isReturned = true;
            }
        }), (returnDone) => {
            if (!isReturned) {
                onDone?.Invoke(returnDone != null ? returnDone : null);
            }
        });
    }

    public static IEnumerator LoadAsset<T>(string pathAsset, string assetName, Action<T> onCached, Action<T> onDone)
        where T : Object {
// #if UNITY_EDITOR
//         Logger.LogWarning("[LoadAsset] test delay load asset!");
//         yield return new WaitForSeconds(1);
// #endif
        assetName = assetName.ToLower();
        string path = pathAsset + assetName;

        //Get cached
        string oldHash = PlayerPrefs.GetString(PlayerPrefsKey.Hash + path, string.Empty);
        if (!string.IsNullOrEmpty(oldHash)) {
            yield return Configuration.instance.StartCoroutine(LoadAssetBundlesByHash(oldHash, path, assetName,
                onCached, false));
        }

        //Get new
        yield return Configuration.instance.StartCoroutine(GetHash(path, newHash => {
            if (newHash != null) {
                if (newHash != oldHash) {
                    Configuration.instance.StartCoroutine(
                        LoadAssetBundlesByHash(newHash, path, assetName, onDone, true));
                }
            } else {
                onDone?.Invoke(null);
            }
        }));
    }

    private static Dictionary<string, AssetBundle> _assetBundlesCached = new();

    public static bool isLoadingAssetBundlesByHash;

    private static IEnumerator LoadAssetBundlesByHash<T>(string hash, string path, string assetName, Action<T> result,
                                                         bool isNew) where T : Object {
        while (isLoadingAssetBundlesByHash) { //dont use WaitUntil
            yield return null;
        }

        isLoadingAssetBundlesByHash = true;
        while (!Caching.ready) {
            yield return null;
        }

        if (isNew) {
            AssetBundleTracking.LogEventLoad(AssetBundleTracking.Type.AssetBundle, path);
        }

        string uri = GetUrl(path);
        Hash128 hash128 = Hash128.Parse(hash);

        UnityWebRequest request = UnityWebRequestAssetBundle.GetAssetBundle(uri, hash128);
        yield return request.SendWebRequest();

        try {
            if (request.result != UnityWebRequest.Result.Success) {
                if (isNew) {
                    AssetBundleTracking.LogEventLoadFail(AssetBundleTracking.Type.AssetBundle, path, request.error);
                }

                LogErrorUnityWebRequest("[LoadAssetBundlesByHash]", request, uri);
                result?.Invoke(null);
                isLoadingAssetBundlesByHash = false;

                yield break;
            }

            if (_assetBundlesCached.ContainsKey(path)) {
                _assetBundlesCached[path].Unload(false);
                _assetBundlesCached.Remove(path);
            }

            AssetBundle myLoadedAssetBundle = DownloadHandlerAssetBundle.GetContent(request);
            if (myLoadedAssetBundle == null) {
                if (isNew) {
                    AssetBundleTracking.LogEventLoadFail(AssetBundleTracking.Type.AssetBundle, path,
                        "DownloadHandlerAssetBundle GetContent is null");
                }

                CustomException.Fire($"[LoadAssetBundlesByHash]{(isNew ? "_New" : "_Cached")} ",
                    $"Failed to load AssetBundle! => {uri} with hash: {hash}");
                result?.Invoke(null);
                isLoadingAssetBundlesByHash = false;

                yield break;
            }

            //delete old
            string oldHash = PlayerPrefs.GetString(PlayerPrefsKey.Hash + path, string.Empty);
            if (!string.IsNullOrEmpty(oldHash) && oldHash != hash) {
                Caching.ClearCachedVersion(assetName, Hash128.Parse(oldHash));
            }

            //cache new
            PlayerPrefs.SetString(PlayerPrefsKey.Hash + path, hash);

            if (typeof(T) == typeof(AssetBundle)) {
                result?.Invoke(myLoadedAssetBundle as T);
                _assetBundlesCached[path] = myLoadedAssetBundle;

            } else {
                T asset = myLoadedAssetBundle.LoadAsset<T>(assetName);
                result?.Invoke(asset);
                myLoadedAssetBundle.Unload(false);
            }

            if (isNew) {
                AssetBundleTracking.LogEventLoadDone(AssetBundleTracking.Type.AssetBundle, path);
            }

            //Logger.Log($"[LoadAssetBundlesByHash]{(isNew ? "_New" : "_Cached")} Loaded AssetBundle => {uri} with hash: {hash}");
            isLoadingAssetBundlesByHash = false;

        } catch (Exception ex) {
            CustomException.Fire("[LoadAssetBundlesByHash]", ex.Message + " => " + ex.StackTrace);
            if (isNew) {
                AssetBundleTracking.LogEventLoadFail(AssetBundleTracking.Type.AssetBundle, path, ex.Message);
            }

            result?.Invoke(null);
            isLoadingAssetBundlesByHash = false;
        }
    }

    public static void ClearCache() {
        if (Caching.ClearCache()) {
            Debug.Log("Successfully cleaned the cache");
        } else {
            Debug.Log("Cache is being used");
        }
    }

    public static void GetCachedVersions(string bundleName) {
        List<Hash128> listOfCachedVersions = new List<Hash128>();
        Caching.GetCachedVersions(bundleName, listOfCachedVersions);
        Debug.Log("listOfCachedVersions " + listOfCachedVersions.Count);
    }

    private static bool _isGettingHash;

    public static IEnumerator GetHash(string path, Action<string> onResult) {
        if (!Utils.isInternetReachable) {
            onResult?.Invoke(null);
            yield break;
        }

        while (_isGettingHash) { //dont use WaitUntil
            yield return null;
        }

        _isGettingHash = true;

        while (!Caching.ready) {
            yield return null;
        }

        string uriManifest = GetUrl(path) + ".manifest";
        AssetBundleTracking.LogEventLoad(AssetBundleTracking.Type.Manifest, path);

        UnityWebRequest request = UnityWebRequest.Get(uriManifest);
        yield return request.SendWebRequest();

        try {
            if (request.result != UnityWebRequest.Result.Success) {
                AssetBundleTracking.LogEventLoadFail(AssetBundleTracking.Type.Manifest, path, request.error);

                LogErrorUnityWebRequest("[GetHash]", request, uriManifest);
                onResult?.Invoke(null);
                _isGettingHash = false;
                yield break;
            }

            string manifest = request.downloadHandler?.text;
            string hash = GetHash(manifest);
            onResult?.Invoke(hash);
            _isGettingHash = false;

            AssetBundleTracking.LogEventLoadDone(AssetBundleTracking.Type.Manifest, path);

        } catch (Exception e) {
            AssetBundleTracking.LogEventLoadFail(AssetBundleTracking.Type.Manifest, path, e.Message);

            CustomException.Fire("[GetHash]", e.Message + " => " + e.StackTrace);
            onResult?.Invoke(null);
            _isGettingHash = false;
        }
    }

    private static string GetHash(string manifest) {
        if (string.IsNullOrEmpty(manifest)) {
            Logger.LogError("[GetHash] Manifest is empty!");
            return null;
        }

        string[] lines = manifest.Split('\n');
        foreach (string line in lines) {
            if (!line.Contains("Hash:")) {
                continue;
            }

            string[] parts = line.Split(':');
            string hash = parts[1].Trim();
            if (!string.IsNullOrEmpty(hash)) {
                return hash;
            }
        }

        return null;
    }

    public static string ConvertNameToBundleName(string spriteName) { //to can up to s3
        spriteName = spriteName.Replace(" ", "_").Replace("(", "-").Replace(")", "-");
        spriteName = spriteName.Replace("é", "e"); //beyoncé
        return spriteName;
    }

    private static void LogErrorUnityWebRequest(string method, UnityWebRequest request, string url) {
        switch (request.result) {
            case UnityWebRequest.Result.ConnectionError:
                Debug.LogError($"{method}Failed to get the feed from the url. [{url}] ERROR: {request.error}");
                break;

            case UnityWebRequest.Result.ProtocolError:
                CustomException.Fire(method, $"ProtocolError. [{url}] ERROR: {request.error}");
                break;

            case UnityWebRequest.Result.DataProcessingError:
                CustomException.Fire(method, $"DataProcessingError. [{url}] ERROR: {request.downloadHandler.error}");
                break;

            default:
                CustomException.Fire(method,
                    $"{request.result}. [{url}] ERROR: {request.error} => {request.downloadHandler.error}");
                break;
        }
    }
}