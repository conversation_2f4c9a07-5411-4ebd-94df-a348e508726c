using System;
using System.Collections;
using System.Collections.Generic;
using Music.ACM;
using UnityEngine;
using Newtonsoft.Json;

public class CacheData {
    public static  Dictionary<string, SongCachedData> dictSongCachedDatas;
    private const  string                             CACHE_FILE_NAME = "cachedData.txt";
    private static IFileService                       _fileService;
    private static string                             _savePath;

    public static void AddSongCardArtist(string acmId, string coverLocalPath) {
        if (dictSongCachedDatas == null) {
            LoadData();
        }

        if (dictSongCachedDatas == null) {
            dictSongCachedDatas = new Dictionary<string, SongCachedData>();
        }

        if (dictSongCachedDatas.ContainsKey(acmId)) {
            dictSongCachedDatas[acmId].UpdateData(coverLocalPath);
        } else {
            dictSongCachedDatas.Add(acmId, new SongCachedData() {
                acmId = acmId,
                coverLocalPath = coverLocalPath
            });
        }

        SaveData();
    }
    public static void AddSongCardSong(string acmId, string localPath) {
        if (dictSongCachedDatas == null) {
            LoadData();
        }

        if (dictSongCachedDatas == null) {
            dictSongCachedDatas = new Dictionary<string, SongCachedData>();
        }

        if (dictSongCachedDatas.ContainsKey(acmId)) {
            dictSongCachedDatas[acmId].UpdateLongSongCardData(localPath);
        } else {
            dictSongCachedDatas.Add(acmId, new SongCachedData() {
                acmId = acmId,
                longSongCardPath = localPath
            });
        }

        SaveData();
    }

    public static SongCachedData GetSongCardArtist(string acmId) {
        if (string.IsNullOrEmpty(acmId)) {
            CustomException.Fire("[GetSongCardArtist]", "acmId is null");
            return null;
        }

        if (dictSongCachedDatas == null) {
            LoadData();
        }

        if (dictSongCachedDatas != null && dictSongCachedDatas.TryGetValue(acmId, out SongCachedData artist)) {
            return artist;
        }

        return null;
    }
    public static SongCachedData GetLongSongCard(string acmId) {
        if (string.IsNullOrEmpty(acmId)) {
            CustomException.Fire("[GetLongSongCard]", "acmId is null");
            return null;
        }

        if (dictSongCachedDatas == null) {
            LoadData();
        }

        if (dictSongCachedDatas != null && dictSongCachedDatas.TryGetValue(acmId, out SongCachedData songCard)) {
            return songCard;
        }

        return null;
    }

    private static void LoadData() {
        _savePath = Utils.ToSavablePath(CACHE_FILE_NAME);
        _fileService = new FileService();

        if (!_fileService.FileExist(_savePath))
            return;

        try {
            var json = _fileService.ReadText(_savePath);
            var savedData = JsonConvert.DeserializeObject<Dictionary<string, SongCachedData>>(json);
            if (savedData != null)
                dictSongCachedDatas = savedData;
        } catch (Exception e) {
            ACMDebug.Warning($"Load SongIdLocalPathMap got problem {e.Message}");
            dictSongCachedDatas = new Dictionary<string, SongCachedData>();
        }
    }

    private static void SaveData() {
        _savePath = Utils.ToSavablePath(CACHE_FILE_NAME);
        var json = JsonConvert.SerializeObject(dictSongCachedDatas);

        if (!string.IsNullOrEmpty(json)) {
            _fileService.WriteAsync(_savePath, json).ContinueWith(taskWrite => {
                if (taskWrite.IsCanceled || taskWrite.IsFaulted) {
                    ACMDebug.Warning($"Save file SongIdLocalPathMap got problem : {taskWrite.Exception?.Message}");
                } else {
                    ACMDebug.Log("Save SongIdLocalPathMap successfully");
                }
            });
        }
    }
}

public class SongCachedData {
    public string acmId;
    public string coverLocalPath;
    public string longSongCardPath;

    public void UpdateData(string coverPath) {
        this.coverLocalPath = coverPath;
    }

    public void UpdateLongSongCardData(string localPath) {
        this.longSongCardPath = localPath;
    }
}