using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;
using System.Linq;
using Com.TheFallenGames.OSA.Core;
using Com.TheFallenGames.OSA.Core.CustomScroller.SongScroller;
using DG.Tweening;
using GameCore;
using JetBrains.Annotations;
using TileHop.GameCore;
using TileHop.LiveEvent;
using TileHop.Cores.UserProgression;
using UnityEngine.EventSystems;

public class SongItemRaw {
    public SONGTYPE songType;
    public Song     song;

    public SongItemRaw(SONGTYPE songtype, Song s) {
        songType = songtype;
        song = s;
    }

    public SongItemRaw() {
        song = null;
    }
}

public class SongPackHeaderItemRaw : SongItemRaw {
    public SongPackHeaderData songPackHeaderData;

    public SongPackHeaderItemRaw(SongPackHeaderData songPackHeaderData) {
        this.songPackHeaderData = songPackHeaderData;
        song = null;
    }
}

public class SongPackLockedHeaderItemRaw : SongItemRaw {
    public SongPackLockedHeaderData songPackLockedHeaderData;

    public SongPackLockedHeaderItemRaw(SongPackLockedHeaderData songPackLockedHeaderData) {
        this.songPackLockedHeaderData = songPackLockedHeaderData;
        song = null;
    }
}

public class SongPackLockedBigHeaderItemRaw : SongItemRaw {
    public SongPackBigLockedHeaderData songPackLockedHeaderData;

    public SongPackLockedBigHeaderItemRaw(SongPackBigLockedHeaderData songPackLockedHeaderData) {
        this.songPackLockedHeaderData = songPackLockedHeaderData;
        song = null;
    }
}

public class SongPackEndingHeaderItemRaw : SongItemRaw {
    public SongPackEndingHeaderData headerData;

    public SongPackEndingHeaderItemRaw(SongPackEndingHeaderData headerData) {
        this.headerData = headerData;
        song = null;
    }
}

public class SongList : MonoBehaviour {
    //static
    public static SongList instance;
    public static int      freeAdding = 1;
    public static float    itemHeight = 101;
    public static string   selectedTag;
    public static string   previewSongID;
    public static Song     lastSong;
    public static float    startPreviewTime;
    public static bool     isShowingFavorite = false;

    //SerializeField
    [SerializeField] private SongsScrollerScriptV2 songsScrollerScript;
    [SerializeField] public  Transform             safeAreaTransform;
    [SerializeField] public  Transform             noneSafeAreaTransform;
    [SerializeField] private GameObject            loadingIndicator;

    //private
    private Dictionary<string, SongItemRaw> _songItemRaws = new Dictionary<string, SongItemRaw>(); // path, indexSong

    private FileSelectorV2Script _fileSelector;
    RemoteConfig remoteConfig => RemoteConfigBase.instance;

    public SongsScrollerScriptV2 SongScroller => songsScrollerScript;

    private Tween _songPackPreviewTween;

    public Song FirstSongInSelectedPack {
        get {
            if (_songItemRaws == null || !UserProgressionController.EnableFeature)
                return null;

            return _songItemRaws.Values.ToList()[0].song;
        }
    }

    [HideInInspector] public SongPackChoosingScroller songPackChoosingScroller;
    private                  Snapper8                 _songPackChoosingSnapper;
    private                  bool                     _isListeningSnapper;

    #region Unity method

    private void Awake() {
        instance = this;

        if (Configuration.instance.enableContentTool) {
            DownloadManager.instanceSafe.LoadSongListWithContentTool();
        }
    }

    private void OnEnable() {
        HomeManager.ShowCharactersPreview();
        LiveEventManager.OnInitCompleted += LiveEventManagerOnInitCompleted;
    }

    private void OnDisable() {
        LiveEventManager.OnInitCompleted -= LiveEventManagerOnInitCompleted;
    }

    private void OnDestroy() {
        if (_isListeningSnapper && _songPackChoosingSnapper != null) {
            _songPackChoosingSnapper.SnappingEndedOrCancelled -= UserProgress_UpdateSongPack;
        }
    }

    private void Start() {
        UpdateUI();

        previewSongID = null;
        startPreviewTime = 0;
        BuildList();
    }

    #endregion

    //Purchase single ST song completed
    public static void ShopPaySingleCompleted(Song paySong) {
        if (paySong != null && SongManager.instance.songs.ContainsKey(paySong.path)) {
            Configuration.instance.SetOpenSong(paySong, 0, true, SongUnlockType.pay_single);
        } else {
            return;
        }

        OpenSong(paySong, 0, SongUnlockType.@default);
    }

    //Purchase ST Album completed
    public static void ShopPayPackageCompleted() {
        foreach (Song song in SongManager.instance.songs.Values) {
            if (song.type.Equals(SONGTYPE.PAY.ToString())) {
                if (song.savedType == SONGTYPE.PAY) {
                    Configuration.instance.SetOpenSong(song, 0, true, SongUnlockType.pay_album);
                }
            }
        }

        if (instance != null) {
            instance.BuildList();
        }
    }

    private void ShowGroup(Dictionary<string, Song> songList) {
        int totalItemInGroup = 0;
        int totalItemNotPay = 0;
        Dictionary<string, Song>.ValueCollection songs = songList.Values;
        string pay = SONGTYPE.PAY.ToString();

        foreach (Song song in songs) {
            if (song.type != pay) {
                continue;
            }

            totalItemInGroup++;
            if (song.savedType != SONGTYPE.OPEN) {
                totalItemNotPay++;
            }
        }

        if (totalItemNotPay >= 4) {
            ShowAlbumButton(totalItemInGroup);
        }
    }

    private void ShowLocalList() {
        if (remoteConfig.Enable_LocalSong) {
            Dictionary<string, Song> songList = SongManager.instance.userLocalSongs;

            ShowUploadButton();

            foreach (Song song in songList.Values) {
                AddSongItemRaw(song.savedType, song);
            }
        }
    }

    private void ShowOfficialList(Dictionary<string, Song> songList) {
        foreach (Song song in songList.Values) {
            SONGTYPE type = song.savedType;
            AddSongItemRaw(type, song);
        }
    }

    public void ScrollToTopBar() {
        songsScrollerScript.ScrollToTop();
    }

    public void BuildList(bool isForceReset = false) {
        selectedTag = SONG_TAG.ALL;

        if (UserProgressionController.EnableFeature) {
            BuildListAllPacks();
            return;
        }

        if (_songItemRaws.Count > 0 && !isForceReset) {
            songsScrollerScript.ForcedUpdateSongList(); //refresh song list

        } else {
            _songItemRaws.Clear();
            Dictionary<string, Song> songList = SongManager.instance.GetByTag(selectedTag);

            ShowGroup(songList);

            int countLocalSong = 0;
            if (!remoteConfig.Song_Tags_Exclude.Contains(SONG_TAG.MY_SONGS)) {
                ShowLocalList();
                countLocalSong = SongManager.instance.userLocalSongs.Count + 1;
            }

            if (remoteConfig.SongOfDay_IsShow && !SubscriptionController.IsSubscriptionVip()) {
                Song songOfDay = SongManager.instance.GetSongOfDay();
                if (songOfDay != null) {
                    songOfDay.savedType = SONGTYPE.OPEN;
                    songOfDay.isSongOfDay = true;
                    songList = SongManager.AddSongOfDay(songList, songOfDay, 2);
                }
            }

            ShowOfficialList(songList);
            songsScrollerScript.LoadSongs(_songItemRaws.Values.ToList(), countLocalSong, selectedTag, null);
        }
    }

    public IEnumerator IEFadeSongList(float alpha, float duration) {
        yield return SongScroller.CanvasGroup.DOFade(alpha, duration).WaitForCompletion();
    }

    public List<Song> CurrentSongPack { get; set; }

    public void BuildList(int userProgresslevel) {
        _songItemRaws.Clear();
        UserProgressionController.WaitUpdateSongListDone(() => {
            CurrentSongPack = SongManager.instance.GetSongsBySongPackData(UserProgressionController.instanceSafe
                .SongPacksHolder.dictSongPacks[userProgresslevel].songs);
            foreach (Song song in CurrentSongPack) {
                AddSongItemRaw(song.savedType, song);
            }

            songsScrollerScript.LoadSongs(_songItemRaws.Values.ToList(), 0, string.Empty, null);
            UpdateSelectedSongPackInformation();
        });
    }

    public void BuildListAllPacks() {
        selectedTag = SONG_TAG.ALL;
        _songItemRaws?.Clear();

        loadingIndicator.SetActive(true);
        UserProgressionController.WaitUpdateSongListDone(() => {
            int packCount = UserProgressionController.instanceSafe.SongPacksHolder.dictSongPacks.Values.Count;
            var previewSong = Configuration.instance.GetCurrentSong();
            bool inSongPack = false;
            Song firstSong = null;
            bool isMeetClosestLockedPack = false;
            loadingIndicator.SetActive(false);

            for (int level = 1; level <= packCount; level++) {
                var songPackGroup = UserProgressionController.instanceSafe.GetSongPackGroup(level);

                bool isLocked = level > UserProgressionController.instanceSafe.lastLevel;
                bool isLastPack = level == packCount;
                if (isLastPack) {
                    if (remoteConfig.UserProgression_ShowDiscoverBeforeUnlockAll) {
                        continue;
                    }
                }
                if (isLocked) {
                    if (!isMeetClosestLockedPack) {
                        isMeetClosestLockedPack = true;
                        AddSongPackItemRawLocked(songPackGroup, level, isLastPack, true);
                    } else {
                        AddSongPackItemRawLocked(songPackGroup, level, isLastPack, false);
                    }
                } else {
                    int totalStars = songPackGroup.maxStar;
                    int collectedStars = songPackGroup.currentStar;
                    AddSongPackItemRaw(level, totalStars, collectedStars, isLastPack);

                    if (isLastPack)
                        continue;

                    var songPack = SongManager.instance.GetSongsBySongPackData(songPackGroup.songs);

                    foreach (Song song in songPack) {
                        if (song.IsHideSong()) {
                            continue;
                        }

                        AddSongItemRaw(song.savedType, song);
                        if (!inSongPack && previewSong != null) {
                            inSongPack = song.Equals(previewSong);
                        }

                        firstSong ??= song;
                        if (previewSong == null) {
                            if (song.IsOpened()) {
                                previewSong = song;
                            }
                        }
                    }
                }
            }

            songsScrollerScript.LoadSongs(_songItemRaws.Values.ToList(), 0, selectedTag, null);
            if (previewSong == null || !inSongPack) {
                previewSong = firstSong;
            }

            HomeManager.instance.TryPreviewSong(previewSong);
        });

    }

    public void BuildListByCategory(string category) {
        _songItemRaws.Clear();
        Dictionary<string, Song> songList = SongManager.instance.GetSongsByCategory(category);

        ShowGroup(songList);

        int countLocalSong = 0;

        if (category == "All") {
            if (!RemoteConfig.instance.Song_Tags_Exclude.Contains(SONG_TAG.MY_SONGS)) {
                ShowLocalList();
                countLocalSong = SongManager.instance.userLocalSongs.Count + 1;
            }

            if (RemoteConfig.instance.SongOfDay_IsShow && !SubscriptionController.IsSubscriptionVip()) {
                Song songOfDay = SongManager.instance.GetSongOfDay();
                if (songOfDay != null) {
                    songOfDay.savedType = SONGTYPE.OPEN;
                    songOfDay.isSongOfDay = true;
                    songList = SongManager.AddSongOfDay(songList, songOfDay, 2);
                }
            }
        }

        ShowOfficialList(songList);
        songsScrollerScript.LoadSongs(_songItemRaws.Values.ToList(), countLocalSong, selectedTag, null, false);
    }

    private Dictionary<string, Song> FilterSongListByKeyword(Dictionary<string, Song> songs, string keyword) {
        Dictionary<string, Song> filterSongs = new Dictionary<string, Song>();

        keyword = keyword.Trim().ToUpper();
        foreach (Song song in songs.Values) {
            if (song.name.ToUpper().Contains(keyword)) {
                filterSongs.Add(song.path, song);
            }
        }

        return filterSongs;
    }

    private void AddSongItemRaw(SONGTYPE type, Song song) {
        if (song == null || string.IsNullOrEmpty(song.path)) {
            return;
        }

        if (_songItemRaws.ContainsKey(song.path)) {
            return;
        }

        _songItemRaws.Add(song.path, new SongItemRaw {songType = type, song = song});
    }

    private void AddSongPackItemRaw(int level, int totalStars, int starsCollected, bool isLastPack = false) {
        string key = level.ToString();
        if (_songItemRaws.ContainsKey(key)) {
            return;
        }

        if (isLastPack) {
            _songItemRaws.Add(key, new SongPackEndingHeaderItemRaw(new SongPackEndingHeaderData(level, false)));
        } else {
            _songItemRaws.Add(key,
                new SongPackHeaderItemRaw(new SongPackHeaderData(level, totalStars, starsCollected, false)));
        }
    }

    private void AddSongPackItemRawLocked(SongPackDataGroup songPackGroup, int level, bool isLastPack = false,
                                          bool isBigHeader = false) {
        string key = level.ToString();
        if (_songItemRaws.ContainsKey(key)) {
            return;
        }

        if (isLastPack) {
            _songItemRaws.Add(key, new SongPackEndingHeaderItemRaw(new SongPackEndingHeaderData(level, true)));
        } else {
            if (remoteConfig.UserProgression_ShowLockedSong) {
                _songItemRaws.Add(key,
                    new SongPackHeaderItemRaw(new SongPackHeaderData(level, songPackGroup.maxStar, 0, true)));
                var songPack = SongManager.instance.GetSongsBySongPackData(songPackGroup.songs);
                foreach (Song song in songPack) {
                    AddSongItemRaw(song.savedType, song);
                }
            } else {
                if (isBigHeader) {
                    _songItemRaws.Add(key, new SongPackLockedBigHeaderItemRaw(new SongPackBigLockedHeaderData(level)));
                } else {
                    _songItemRaws.Add(key, new SongPackLockedHeaderItemRaw(new SongPackLockedHeaderData(level)));
                }
            }
        }
    }

    public void RemoveSongItemRaw(string songPath) {
        if (string.IsNullOrEmpty(songPath)) {
            return;
        }

        if (!_songItemRaws.ContainsKey(songPath)) {
            return;
        }

        SongItemRaw songItemRaw = _songItemRaws[songPath];
        _songItemRaws.Remove(songPath);
        songsScrollerScript.RemoveItem(songItemRaw);
    }

    private void ShowAlbumButton(int totalItemInGroup) {
        Song stItem = new Song {
            path = "album",
            savedType = SONGTYPE.ALBUM,
            name = LocalizationManager.instance.GetLocalizedValue("ST_ALBUM"),
            desc = LocalizationManager.instance.GetLocalizedValue("X_SONGS").Replace("{0}", totalItemInGroup.ToString())
        };
        AddSongItemRaw(SONGTYPE.ALBUM, stItem);
    }

    private void ShowUploadButton() {
        Song stItem = new Song {
            path = "button",
            savedType = SONGTYPE.BUTTON,
            name = LocalizationManager.instance.GetLocalizedValue("IMPORT_SONG")
        };
        AddSongItemRaw(SONGTYPE.BUTTON, stItem);
    }

    public static int GetUploadType() {
        if (SubscriptionController.IsSubscriptionVip() || (SongManager.instance.userLocalSongs.Count < freeAdding &&
                                                           Configuration.FreeAddSongIsOn())) {
            return 1; //FREE_FIRST_TIME
        } else if (!Configuration.IsNoAllAds() &&
                   (SongManager.instance.userLocalSongs.Count <
                       freeAdding + RemoteConfigBase.instance.Video_LocalSong || Configuration.instance.GetDiamonds() <
                       RemoteConfigBase.instance.Price_Upload_Song)) {
            return 2; //videoButton
        } else {
            return 3; //buyButton
        }
    }

    public static void OpenSongAndPlay(Song song, int price, string location,
                                       SongUnlockType songUnlockType = SongUnlockType.@default) {
        if (price == 0) {
            OpenSong(song, 0, songUnlockType);
        }

        if (TransitionInOut.isInstanced && EventSystem.current.currentSelectedGameObject != null) {
            TransitionInOut.instance.TransitionOut(EventSystem.current.currentSelectedGameObject.transform.position,
                () => { Util.GoToGamePlay(song, location, isSongClick: true); });
        } else {
            Util.GoToGamePlay(song, location, isSongClick: true);
        }
    }

    public static event Action<string> OnUnlockSong;

    public static void OpenSong(Song song, int price, SongUnlockType songUnlockType) {
        if (song != null) {
            Configuration.instance.SetOpenSong(song, price, true, songUnlockType);
            if (instance != null && instance._songItemRaws.ContainsKey(song.path)) {
                SongItemRaw newItem = instance._songItemRaws[song.path];
                newItem.songType = SONGTYPE.OPEN;
                instance._songItemRaws[song.path] = newItem;
                instance.songsScrollerScript.UpdateItem(song.path, newItem);
            }

            OnUnlockSong?.Invoke(song.acm_id_v3);
        }
    }

    #region UPLOAD FILES

    private const string STORAGE_PERMISSION_12_OR_OLDER = "android.permission.READ_EXTERNAL_STORAGE";
    private const string STORAGE_PERMISSION_13_OR_NEWER = "android.permission.READ_MEDIA_AUDIO";

    [CanBeNull] private string _storagePermission;

    private string STORAGE_PERMISSION {
        get {
            if (_storagePermission == null) {
                _storagePermission = AndroidPermissionsManager.IsAndroid13OrNewer()
                    ? STORAGE_PERMISSION_13_OR_NEWER
                    : STORAGE_PERMISSION_12_OR_OLDER;
            }

            return _storagePermission;
        }
    }

    //Upload button click
    public void UploadButton_Click() {
        int type = GetUploadType();
        SoundManager.PlayGameButton();
        AnalyticHelper.FireEvent(FIRE_EVENT.LocalSong_Click);
        // Type == 1: Free 
        if (type == 1) {
            AskConfirmPermission();
        }
        // Type == 2: Video 
        else if (type == 2) {
            AdsManager.instance.ShowRewardAds(VIDEOREWARD.local_song, null, location: LOCATION_NAME.home.ToString(),
                true, OnRewardVideoCompleted);
        }
        // Type == 3: Diamonds 
        else {
            if (Configuration.instance.GetDiamonds() >= remoteConfig.Price_Upload_Song) {
                AskConfirmPermission();
            } else {
                Util.ShowNeedMore(LOCATION_NAME.upload_song, remoteConfig.Price_Upload_Song, VIDEOREWARD.local_song);
            }
        }
    }

    private void OnRewardVideoCompleted(bool isCompleted) {
        if (!isCompleted) {
            return;
        }

        if (instance != null) { // UI1
            instance.AskConfirmPermission(0.3f);
        }
    }

    //PrepareShowFileSelector: Wait for permission
    public void AskConfirmPermission(float waitTime = 0) {
#if UNITY_ANDROID
        if (Application.platform == RuntimePlatform.Android &&
            !AndroidPermissionsManager.IsPermissionGranted(STORAGE_PERMISSION)) {
            AnalyticHelper.FireEvent(FIRE_EVENT.LocalSong_AskPermission);
            Util.ShowPopUp(PopupName.PermissionNeeded);
        } else {
            PrepareShowFileSelector(waitTime);
        }
#endif
    }

    public void PrepareShowFileSelector(float waitTime = 0) {
        StartCoroutine(ShowFileSelector(waitTime));
    }

    PermissionResult[] permissionResult;
    GameObject         popup;

    private IEnumerator ShowFileSelector(float waitTime) {
        yield return new WaitForSeconds(waitTime);

        if (Application.platform == RuntimePlatform.Android) {
            if (!AndroidPermissionsManager.IsPermissionGranted(STORAGE_PERMISSION)) {
                permissionResult = null;
                AnalyticHelper.FireEvent(FIRE_EVENT.LocalSong_RequestPermission);
                AndroidPermissionsManager.RequestPermission(new[] {STORAGE_PERMISSION},
                    new AndroidPermissionCallback(results => { permissionResult = results; }));
                StartCoroutine(WaitPermissionResult());
            } else {
                if (PermissionNeeded.instance != null) {
                    PermissionNeeded.instance.Close();
                }

                SetActiveFileSelector(true);
            }
        } else {
            SetActiveFileSelector(true);
        }
    }

    private IEnumerator WaitPermissionResult() {
        while (permissionResult == null) {
            yield return null;
        }

        bool isNeedOpenAppSetting = PlayerPrefs.GetInt("permission_neverAskAgain", 0) == 1;

        if (permissionResult.Length > 0) {
            PermissionResult result = permissionResult[0];
            if (PermissionNeeded.instance != null) {
                PermissionNeeded.instance.Close();
            }

            if (result.neverAskAgain && result.permissionGranted == false) {
                if (isNeedOpenAppSetting) {
                    AndroidPermissionsManager.OpenAppSetting();
                } else {
                    PlayerPrefs.SetInt("permission_neverAskAgain", 1);
                }
            }

            if (result.permissionGranted) {
                SetActiveFileSelector(true);
            } else {
                AnalyticHelper.FireEvent(FIRE_EVENT.LocalSong_PermissionDenied);
                SetActiveFileSelector(false);
            }
        }
    }

    private void SetActiveFileSelector(bool isActive) {
        if (isActive) {
            if (_fileSelector == null) {
                //create new
                _fileSelector = Util.ShowPopUp(PopupName.FileSelectorV2).GetComponent<FileSelectorV2Script>();
            }

            _fileSelector.Show(Direction.Right);
        } else {
            if (_fileSelector != null) {
                _fileSelector.Hide(Direction.None);
            }
        }
    }

    #endregion

    private float _heightScreen;

    private void UpdateUI() {
        if (_heightScreen <= 0) {
            RectTransform rtfMain = GetComponentInParent<Canvas>().GetComponent<RectTransform>();
            _heightScreen = rtfMain.GetSizeDeltaY();
        }
    }

    private void LiveEventManagerOnInitCompleted() {
        string currentPathSongOfDay = PlayerPrefs.GetString(PlayerPrefsKey.SongOfDay + Util.GetCurrentDay(), "");
        Song songOfDay = SongManager.instance.GetSongOfDay();
        if (songOfDay != null && (songOfDay.savedType == SONGTYPE.EVENT || songOfDay.path != currentPathSongOfDay)) {
            BuildList();
        }

        songsScrollerScript.Refresh();
    }

    public void AddHideSongs() {
        bool isNeedUpdateSongList = LiveEventManager.instance.IsNeedUpdateSongList();
        if (isNeedUpdateSongList) {
            SongManager.instance.ClearHidedSongs(LiveEventManager.instance.GetHideEventSongs());
            //BuildList();
        }
    }

    public static bool TryToUnlockSongByAds(Song song) {
        song.viewedAds++;
        return song.viewedAds >= song.ads;
    }

    /// <summary>
    /// NEXT SONG PACK
    /// </summary>
    public void UserProgress_NextSongPack() {
        // update disk icons scroller, scroll to right position
        UserProgress_UpdateDiskStageScroll(SongManager.UserProgress_NextSelectedLevel());
    }

    /// <summary>
    /// PREVIOUS SONG PACK
    /// </summary>
    public void UserProgress_PrevSongPack() {
        // update disk icons scroller, scroll to right position
        UserProgress_UpdateDiskStageScroll(SongManager.UserProgress_PrevSelectedLevel());
    }

    /// <summary>
    /// SOCK PACK list when scroll disks
    /// </summary>
    public void UserProgress_UpdateSongPack() {
        if (_songPackChoosingSnapper.LastSnappedItemIndex == -1)
            return;
        if (SongManager.UserProgress_CurrentSelectedPackLevel == _songPackChoosingSnapper.LastSnappedItemIndex + 1)
            return;

        SongManager.UserProgress_CurrentSelectedPackLevel = _songPackChoosingSnapper.LastSnappedItemIndex + 1;

        BuildList(SongManager.UserProgress_CurrentSelectedPackLevel);
    }

    /// <summary>
    /// gắn data songpack vào disks scroller
    /// </summary>
    private void UserProgress_SetDataForDiskIconsScroller() {
        if (songPackChoosingScroller) {
            UserProgressionController.WaitUpdateSongListDone(() => {
                songPackChoosingScroller.SetData(UserProgressionController.instanceSafe.SongPacksHolder.dictSongPacks.Values
                    .ToList());
                UserProgress_UpdateDiskStageScroll(SongManager.UserProgress_CurrentSelectedPackLevel);
            });
        }
    }

    IEnumerator IESetDataForDiskIconsScroller() {
        yield return new WaitUntil(() => songPackChoosingScroller.Data != null);

        UserProgress_SetDataForDiskIconsScroller();
    }

    /// <summary>
    /// Update disks scroller khi click button next, prev
    /// </summary>
    public void UserProgress_UpdateDiskStageScroll(int packLevel) {
        if (songPackChoosingScroller) {
            songPackChoosingScroller.ScrollTo(packLevel - 1, onDone: () => {
                if (_songPackChoosingSnapper) {
                    _songPackChoosingSnapper.OnSnappingForceEnd();
                }
            });
        }
    }

    private void UpdateSelectedSongPackInformation() {
        UpdateSongPackText();

        if (PreviewSongController.isInstanced) {
            PreviewSongController.instanceSafe.StopPreviewMusic(null, false);
        }

        _songPackPreviewTween?.Kill();
        _songPackPreviewTween = DOVirtual.DelayedCall(0.5f, PreviewSongInPack);
    }

    /// <summary>
    /// phát nhạc preview bài đầu tiên trong song pack đang chọn
    /// </summary>
    public void PreviewSongInPack() {
        if (UserProgressionController.instanceSafe.IsRandomPreviewSong &&
            songsScrollerScript.GetRandomVisibleSongToPreview(out Song song)) {
            PreviewSongController.instanceSafe.StartPreviewMusic(song, location: LOCATION_NAME.home.ToString(),
                false);
        } else {
            PreviewSongController.instanceSafe.StartPreviewMusic(FirstSongInSelectedPack,
                location: LOCATION_NAME.home.ToString(), false);
        }
    }

    public void UpdateMainScrollDiskIcon(Song song) {
        if (song == null)
            return;

        var viewHolder =
            songPackChoosingScroller.GetItemViewsHolderIfVisible(_songPackChoosingSnapper.LastSnappedItemIndex);
        if (viewHolder != null && viewHolder.cellView is DiskCellView diskCellView) {
            diskCellView.SetDiskIcon(song);
        }
    }

    /// <summary>
    /// Cập nhật text hiển thị level của song pack đang chọn
    /// </summary>
    public void UpdateSongPackText() {
        int unlockedCount = 0;
        int collectedStars = 0;
        foreach (SongItemRaw songRaw in _songItemRaws.Values) {
            if (songRaw.song == null)
                continue;

            if (songRaw.song.IsOpened()) {
                unlockedCount++;
            }

            collectedStars += Configuration.GetBestStars(songRaw.song.path);
        }

        HomeManager.instance.UserProgress_UpdateSongPackInformation(unlockedCount, collectedStars,
            _songItemRaws.Values.Count);
    }

    public void SetSongPackScrollView(UISelectDiskPacks mainDiskHome) {
        songPackChoosingScroller = mainDiskHome.songPackChoosingScroller;
        _songPackChoosingSnapper = mainDiskHome.songPackChoosingSnapper;
        if (!_isListeningSnapper && _songPackChoosingSnapper) {
            _isListeningSnapper = true;
            _songPackChoosingSnapper.SnappingEndedOrCancelled += UserProgress_UpdateSongPack;
        }
    }
    public IEnumerator IESmoothPackScrollView(int idPack, float time, float normalizeOffset = 0.5f,
                                              float normalizedPosition = 0.5f) {
        yield return songsScrollerScript.SetScrollToHeader(idPack, time, normalizeOffset, normalizedPosition);
    }

    public IEnumerator IESmoothScrollToItem(Song song, float time, float normalizeOffset = 0.5f,
                                            float normalizedPosition = 0.5f) {
        yield return songsScrollerScript.SmoothScrollToSongItem(song, time, normalizeOffset, normalizedPosition);
    }

    public OptimizedCellView GetLastPlayedSongItemView() {
        return SongScroller.GetSongObjectByPath(NotesManager.instance.song.path);
    }

    public Song GetLastPlayedSong() {
        return SongScroller.GetSongByPath(NotesManager.instance.song.path);
    }
}