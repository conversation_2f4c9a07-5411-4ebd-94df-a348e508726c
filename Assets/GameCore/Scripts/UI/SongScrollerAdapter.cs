using System;
using System.Collections.Generic;
using Com.TheFallenGames.OSA.CustomParams;
using Com.TheFallenGames.OSA.DataHelpers;
using TileHop.Cores.UserProgression;
using UnityEngine;
using UnityEngine.EventSystems;

namespace Com.TheFallenGames.OSA.Core.CustomScroller.SongScroller {

    public class SongScrollerAdapter : OSA<SongParamsWithPrefab, SongItemViewsHolder> {

        // Helper that stores data and notifies the adapter when items count changes
        // Can be iterated and can also have its elements accessed by the [] operator
        public SimpleDataHelper<IData> Data { get; private set; }
        public event Action<SongItemViewsHolder> OnItemVisible;
        public event Action<SongItemViewsHolder> OnItemDisabled;
        public event Action<PointerEventData> OnBeginDragEvent;
        public event Action<PointerEventData> OnDragEvent;
        public event Action<PointerEventData> OnEndDragEvent;
        public event Action<SongPackHeaderBaseItem> OnUpdateHeader;

        #region OSA implementation

        protected override void OnEnable() {
            base.OnEnable();
            
            // reset header để update lại background sau khi back về home (vd từ shop)
            _topHeader = null;
        }

        protected override void Start() {
            Data = new SimpleDataHelper<IData>(this);
            base.Start();
        }


        public void Init(RectTransform prefSongItem) {
            Parameters.ItemPrefab = prefSongItem;

            // remote config cho sự giảm tốc của scroller
            Parameters.effects.InertiaDecelerationRate = Mathf.Clamp01(RemoteConfig.instance.Scroller_DeclearationRate);
            // remote config cho bật / tắt navigation
            if (Parameters.Scrollbar != null) {
                Parameters.Scrollbar.gameObject.SetActive(RemoteConfig.instance.ScrollerNavigation_IsEnable);
            }
        }

        // This is called initially, as many times as needed to fill the viewport, 
        // and anytime the viewport's size grows, thus allowing more items to be displayed
        // Here you create the "ViewsHolder" instance whose views will be re-used
        // *For the method's full description check the base implementation
        protected override SongItemViewsHolder CreateViewsHolder(int itemIndex) {
            var modelType = Data[itemIndex].GetType(); // _ModelTypes[itemIndex];
            SongItemViewsHolder instance;
            
            if (modelType == typeof(DataCategory) && _Params.prefLoginForm != null) {
                instance = new SongItemViewsHolder();
                instance.Init(_Params.prefLoginForm, _Params.Content, itemIndex);
                return instance;
            }

            // OWNED pack header
            if (modelType == typeof(SongPackHeaderData)) {
                instance = new SongPackHeaderViewHolder();
                ((SongPackHeaderViewHolder) instance).SetLocked(((SongPackHeaderData) Data[itemIndex]).isLocked);
                instance.Init(_Params.prefSongPackHeader, _Params.Content, itemIndex);
                return instance;
            }

            // LOCKED pack header
            if (modelType == typeof(SongPackLockedHeaderData)) {
                instance = new SongPackLockedHeaderViewHolder();
                instance.Init(_Params.prefSongPackLockedHeader, _Params.Content, itemIndex);
                return instance;
            }
            
            // LOCKED pack big header
            if (modelType == typeof(SongPackBigLockedHeaderData)) {
                instance = new SongPackBigLockedHeaderViewHolder();
                instance.Init(_Params.prefSongPackBigLockedHeader, _Params.Content, itemIndex);
                return instance;
            }
            
            // LAST pack header
            if (modelType == typeof(SongPackEndingHeaderData)) {
                instance = new SongPackEndingHeaderViewHolder();
                instance.Init(_Params.prefSongPackEndingHeader, _Params.Content, itemIndex);
                return instance;
            }

            // SONG CARD
            instance = new SongCardViewHolder();
            instance.Init(_Params.ItemPrefab, _Params.Content, itemIndex);
            return instance;
        }

        // This is called anytime a previously invisible item become visible, or after it's created,
        // or when anything that requires a refresh happens
        // Here you bind the data from the model to the item's views
        // *For the method's full description check the base implementation
        protected override void UpdateViewsHolder(SongItemViewsHolder newOrRecycled) {
            // In this callback, "newOrRecycled.ItemIndex" is guaranteed to always reflect the
            // index of item that should be represented by this views holder. You'll use this index
            // to retrieve the model from your data set

            var modelData = Data[newOrRecycled.ItemIndex];
            newOrRecycled.cellView.SetData(modelData);
            OnItemVisible?.Invoke(newOrRecycled);

            if (modelData is SongPackHeaderData headerData) {
                ((SongPackHeaderViewHolder) newOrRecycled).SetLocked(headerData.isLocked);
            }
            
#if UNITY_EDITOR
            // if (modelData is Song) {
            //     newOrRecycled.cellView.gameObject.name = $"SongItem {newOrRecycled.ItemIndex}";
            // } else if (modelData is SongPackHeaderData) {
            //     newOrRecycled.cellView.gameObject.name = $"Header {newOrRecycled.ItemIndex}";
            // } else if (modelData is SongPackLockedHeaderData lockedHeaderData) {
            //     newOrRecycled.cellView.gameObject.name = $"{(lockedHeaderData.isBigHeader? "Big" : "")} HeaderLocked {newOrRecycled.ItemIndex}";
            // }
#endif
        }

        protected override bool IsRecyclable(SongItemViewsHolder potentiallyRecyclable,
                                             int indexOfItemThatWillBecomeVisible,
                                             double sizeOfItemThatWillBecomeVisible) {
            var model = Data[indexOfItemThatWillBecomeVisible];
            return potentiallyRecyclable.CanPresentModelType(model.GetType());
        }

        public override void OnBeginDrag(PointerEventData eventData) {
            base.OnBeginDrag(eventData);
            OnBeginDragEvent?.Invoke(eventData);
        }

        public override void OnDrag(PointerEventData eventData) {
            base.OnDrag(eventData);
            OnDragEvent?.Invoke(eventData);
        }

        public override void OnEndDrag(PointerEventData eventData) {
            base.OnEndDrag(eventData);
            OnEndDragEvent?.Invoke(eventData);
        }


        #region Difference Item Size

        private readonly HashSet<int>           _resizeHashSet = new();
        private          SongPackHeaderBaseItem _topHeader;

        public void ResetTopHeader() {
            _topHeader = null;
        }
        
        /// <summary>
        /// Khi lần đầu build list, do kích thước các loại item khác nhau
        /// mà OSA tính toán size theo default item size => dẫn đến lần đầu scrollTo sẽ bị sai normalized position
        /// Giải pháp: Request change size với tất cả các item có kích thước khác default item size từ đầu
        /// </summary>
        public void RefreshVirtualScrollSize() {
            for (int i = 0; i < Data.Count; i++) {
                if (Data[i] is SongPackHeaderData headerData) {
                    RequestChangeItemSizeAndUpdateLayout(
                        i, 
                        UserExpSongPackProgress.headerHeightMap[headerData.isLocked? HeaderType.OpenLocked : HeaderType.Open],
                        false, 
                        true, 
                        false, 
                        true);
                    
                } else if (Data[i] is SongPackEndingHeaderData) {
                    RequestChangeItemSizeAndUpdateLayout(
                        i,
                        UserExpSongPackProgress.headerHeightMap[HeaderType.EndedOpen], 
                        false, 
                        true, 
                        false, 
                        true);
                    
                } else if (Data[i] is SongPackLockedHeaderData) {
                    RequestChangeItemSizeAndUpdateLayout(
                        i, 
                        UserExpSongPackProgress.headerHeightMap[HeaderType.Locked],
                        false, 
                        true, 
                        false, 
                        true);
                    
                } else if (Data[i] is SongPackBigLockedHeaderData) {
                    RequestChangeItemSizeAndUpdateLayout(
                        i, 
                        UserExpSongPackProgress.headerHeightMap[HeaderType.BigLocked],
                        false, 
                        true, 
                        false, 
                        true);
                }
            }
        }

        protected override void PostRebuildLayoutDueToScrollViewSizeChange() {
            RefreshVirtualScrollSize();
        }

        private float _timeToFindHeader;

        protected override void Update() {
            base.Update();
            int count = VisibleItemsCount;
            bool isStopFindingHeader = !UserProgressionController.EnableFeature;
            
            for (int i = 0; i < count; i++) {
                var item = GetItemViewsHolder(i);
                if (item == null) {
                    continue;
                }
                
                if (!_resizeHashSet.Contains(item.ItemIndex)) {
                    RequestChangeItemSizeAndUpdateLayout(item.ItemIndex, item.height, false, true, false, true);
                    _resizeHashSet.Add(item.ItemIndex);
                    if (item is SongPackHeaderViewHolder header) {
                        ((SongPackHeaderBaseItem) header.cellView)?.UpdateAnchor();
                    }
                }

                if (!isStopFindingHeader && UnityEngine.Time.time > _timeToFindHeader && item is SongPackHeaderViewHolder {
                        cellView: SongPackHeaderBaseItem headerItem
                    }) {
                    if (headerItem is SongPackLockedHeaderItem) {
                        if (!RemoteConfigBase.instance.UserProgression_ShowLockedSong) {
                            continue;
                        }
                    }

                    _timeToFindHeader = UnityEngine.Time.time + 0.1f;
                    
                    // nếu đã tìm ra được Header ở trên cùng => không tìm nữa
                    if (headerItem == _topHeader) {
                        isStopFindingHeader = true;
                        continue;
                    }

                    // invoke update background gradient mỗi khi tìm được top header mới
                    _topHeader = headerItem;
                    isStopFindingHeader = true;
                    OnUpdateHeader?.Invoke(_topHeader);
                }
            }
        }

        public void RefreshSongItemsDiskUI(Color darkColor, Color lightColor, bool isFirstTime = false) {
            int count = VisibleItemsCount;
            for (int i = 0; i < count; i++) {
                var item = GetItemViewsHolder(i);
                if (item == null) {
                    continue;
                }

                if (item is SongCardViewHolder songCard) {
                    if (isFirstTime) {
                        var disk = ((SongItem) songCard.cellView).bgDiskWrapper;
                        var material = disk.material;
                        
                        material.SetColor(SongPackThemeData.colorDarkId, darkColor);
                        material.SetColor(SongPackThemeData.colorLightId, lightColor);
                    }
                    
                    if (!songCard.materialForRendering) {
                        songCard.materialForRendering = ((SongItem) songCard.cellView).bgDiskWrapper.materialForRendering;
                    }
                    
                    songCard.materialForRendering.SetColor(SongPackThemeData.colorDarkId, darkColor);
                    songCard.materialForRendering.SetColor(SongPackThemeData.colorLightId, lightColor);
                    return;
                }
            }
        }

        protected override void OnBeforeRecycleOrDisableViewsHolder(SongItemViewsHolder inRecycleBinOrVisible,
                                                                    int newItemIndex) {
            if (_resizeHashSet.Contains(inRecycleBinOrVisible.ItemIndex)) {
                _resizeHashSet.Remove(inRecycleBinOrVisible.ItemIndex);
            }

            OnItemDisabled?.Invoke(inRecycleBinOrVisible);
            base.OnBeforeRecycleOrDisableViewsHolder(inRecycleBinOrVisible, newItemIndex);
        }

        #endregion

        // This is the best place to clear an item's views in order to prepare it from being recycled, but this is not always needed,
        // especially if the views' values are being overwritten anyway. Instead, this can be used to, for example, cancel an image
        // download request, if it's still in progress when the item goes out of the viewport.
        // <newItemIndex> will be non-negative if this item will be recycled as opposed to just being disabled
        // *For the method's full description check the base implementation

        #endregion

        // protected override void OnBeforeRecycleOrDisableViewsHolder(SongItemViewsHolder inRecycleBinOrVisible, int newItemIndex)
        // {
        //     base.OnBeforeRecycleOrDisableViewsHolder(inRecycleBinOrVisible, newItemIndex);
        // }


        // You only need to care about this if changing the item count by other means than ResetItems,
        // case in which the existing items will not be re-created, but only their indices will change.
        // Even if you do this, you may still not need it if your item's views don't depend on the physical position
        // in the content, but they depend exclusively to the data inside the model (this is the most common scenario).
        // In this particular case, we want the item's index to be displayed and also to not be stored inside the model,
        // so we update its title when its index changes. At this point, the Data list is already updated and
        // shiftedViewsHolder.ItemIndex was correctly shifted so you can use it to retrieve the associated model
        // Also check the base implementation for complementary info
        /*
        protected override void OnItemIndexChangedDueInsertOrRemove(MyListItemViewsHolder shiftedViewsHolder, int oldIndex, bool wasInsert, int removeOrInsertIndex)
        {
            base.OnItemIndexChangedDueInsertOrRemove(shiftedViewsHolder, oldIndex, wasInsert, removeOrInsertIndex);

            shiftedViewsHolder.titleText.text = Data[shiftedViewsHolder.ItemIndex].title + " #" + shiftedViewsHolder.ItemIndex;
        }
        */

        // These are common data manipulation methods
        // The list containing the models is managed by you. The adapter only manages the items' sizes and the count
        // The adapter needs to be notified of any change that occurs in the data list. Methods for each
        // case are provided: Refresh, ResetItems, InsertItems, RemoveItems

        #region data manipulation

        public void AddItemsAtEnd(IList<IData> items) {
            // Commented: the below 2 lines exemplify how you can use a plain list to manage the data, instead of a DataHelper, in case you need full control
            //YourList.InsertRange(index, items);
            //InsertItems(index, items.Length);
            Data.InsertItems(Data.Count, items);
        }

        public void AddItemAtEnd(IData item) {
            Data.InsertOneAtEnd(item, true);
        }

        public void AddItemsAt(int index, IList<IData> items) {
            // Commented: the below 2 lines exemplify how you can use a plain list to manage the data, instead of a DataHelper, in case you need full control
            //YourList.InsertRange(index, items);
            //InsertItems(index, items.Length);
            Data.InsertItems(index, items);
        }

        public void RemoveItemsFrom(int index, int count) {
            // Commented: the below 2 lines exemplify how you can use a plain list to manage the data, instead of a DataHelper, in case you need full control
            //YourList.RemoveRange(index, count);
            //RemoveItems(index, count);

            Data.RemoveItems(index, count);
        }

        public void SetItems(IList<IData> items) {
            // Commented: the below 3 lines exemplify how you can use a plain list to manage the data, instead of a DataHelper, in case you need full control
            //YourList.Clear();
            //YourList.AddRange(items);
            //ResetItems(YourList.Count);
            Data.ResetItems(items);
        }

        public void SetItems(IData[] items) {
            // Commented: the below 3 lines exemplify how you can use a plain list to manage the data, instead of a DataHelper, in case you need full control
            //YourList.Clear();
            //YourList.AddRange(items);
            //ResetItems(YourList.Count);
            Data.ResetItems(items);
        }

        #endregion

        public float GetViewHeight() {
            return _Params.Content.rect.height;
        }
    }

    public class SongItemViewsHolder : BaseItemViewsHolder {
        public OptimizedCellView cellView;
        public virtual float height => 100;


        // Retrieving the views from the item's root GameObject
        public override void CollectViews() {
            base.CollectViews();
            cellView = root.GetComponent<OptimizedCellView>();
        }

        public virtual bool CanPresentModelType(Type modelType) {
            return true;
        }
    }


    public class SongCardViewHolder : SongItemViewsHolder {
        public Material materialForRendering;
        
        public override bool CanPresentModelType(Type modelType) {
            return modelType == typeof(Song);
        }

        public override float height => 90f;
    }

    public class SongPackHeaderViewHolder : SongItemViewsHolder {
        private bool _isLocked;

        public void SetLocked(bool isLocked) {
            _isLocked = isLocked;
        }
        
        public override bool CanPresentModelType(Type modelType) {
            return modelType == typeof(SongPackHeaderData);
        }

        public override float height =>
            UserExpSongPackProgress.headerHeightMap[_isLocked ? HeaderType.OpenLocked : HeaderType.Open];
    }
    
    public class SongPackLockedHeaderViewHolder : SongPackHeaderViewHolder {
        public override bool CanPresentModelType(Type modelType) {
            return modelType == typeof(SongPackLockedHeaderData);
        }

        public override float height =>  UserExpSongPackProgress.headerHeightMap[HeaderType.Locked];
    }
    
    public class SongPackBigLockedHeaderViewHolder : SongPackHeaderViewHolder {
        public override bool CanPresentModelType(Type modelType) {
            return modelType == typeof(SongPackBigLockedHeaderData);
        }

        public override float height =>  UserExpSongPackProgress.headerHeightMap[HeaderType.BigLocked];
    }
    
    public class SongPackEndingHeaderViewHolder : SongPackHeaderViewHolder {
        public override bool CanPresentModelType(Type modelType) {
            return modelType == typeof(SongPackEndingHeaderData);
        }

        public override float height =>  UserExpSongPackProgress.headerHeightMap[HeaderType.EndedOpen];
    }

    [Serializable] // serializable, so it can be shown in inspector
    public class SongParamsWithPrefab : BaseParamsWithPrefab {
        public bool          isInited;
        public RectTransform prefLoginForm;

        #region UserProgression Headers

        private RectTransform _prefSongPackHeader;
        private RectTransform _prefEndingHeader;
        private RectTransform _prefLockedHeader;
        private RectTransform _prefBigLockedHeader;

        public RectTransform prefSongPackHeader {
            get {
                if (!_prefSongPackHeader) {
                    _prefSongPackHeader = Resources.Load<RectTransform>("SongPackHeaders/[UP2] SongPackHeader");
                }

                return _prefSongPackHeader;
            }
        }

        public RectTransform prefSongPackLockedHeader {
            get {
                if (!_prefLockedHeader) {
                    _prefLockedHeader = Resources.Load<RectTransform>("SongPackHeaders/[UP2] SongPackLockedHeader");
                }

                return _prefLockedHeader;
            }
        }
        
        public RectTransform prefSongPackBigLockedHeader {
            get {
                if (!_prefBigLockedHeader) {
                    _prefBigLockedHeader = Resources.Load<RectTransform>("SongPackHeaders/[UP2] SongPackBigLockedHeader");
                }

                return _prefBigLockedHeader;
            }
        }

        public RectTransform prefSongPackEndingHeader {
            get {
                if (!_prefEndingHeader) {
                    _prefEndingHeader = Resources.Load<RectTransform>("SongPackHeaders/[UP2] SongPackEndingHeader");
                }

                return _prefEndingHeader;
            }
        }

        #endregion

        public override void InitIfNeeded(IOSA iAdapter) {
            base.InitIfNeeded(iAdapter);

            if (!isInited)
                return;

            if (prefLoginForm != null) {
                AssertValidWidthHeight(prefLoginForm);
            }
        }
    }

    public class DataCategory : IData { }

    public class SongPackHeaderBaseData : IData {
        public int packLevel;

        protected SongPackHeaderBaseData(int packLevel) {
            this.packLevel = packLevel;
        }
    }

    public class SongPackHeaderData : SongPackHeaderBaseData {
        public int  totalStarsCanCollect;
        public int  starsCollected;
        public bool isLocked;

        public SongPackHeaderData(int packLevel, int totalStarsCanCollect, int starsCollected, bool isLocked) : base(packLevel) {
            this.packLevel = packLevel;
            this.totalStarsCanCollect = totalStarsCanCollect;
            this.starsCollected = starsCollected;
            this.isLocked = isLocked;
        }

    }

    public class SongPackLockedHeaderData : SongPackHeaderBaseData {
        
        public SongPackLockedHeaderData(int packLevel) : base(packLevel) {
            this.packLevel = packLevel;
        }
    }
    
    public class SongPackBigLockedHeaderData : SongPackHeaderBaseData {
        public SongPackBigLockedHeaderData(int packLevel) : base(packLevel) {
            this.packLevel = packLevel;
        }
    }
    
    public class SongPackEndingHeaderData : SongPackHeaderBaseData {
        public bool isLocked;

        public SongPackEndingHeaderData(int packLevel, bool isLocked) : base(packLevel) {
            this.packLevel = packLevel;
            this.isLocked = isLocked;
        }
    }
}