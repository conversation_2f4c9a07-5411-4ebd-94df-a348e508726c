using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class FreeDiamondInTimeButton : MonoBehaviour {
    [SerializeField] private Text       diamondBonusText;
    [SerializeField] private Text       txtRemainTime;
    [SerializeField] private GameObject btnFree;
    [SerializeField] private GameObject btnWatchAds;

    private int   _amount;
    private bool  _isReady;
    private float _remainTime;
    private float _count1Second;
    private bool  _runTime;
    private int   _free_gem_no;

    private void OnEnable() {
        _count1Second = 0f;
        LoadData();
    }

    private void LateUpdate() {
        if (!_runTime)
            return; // Nếu k có RunTime -> k cần tính countDown

        if (!_isReady) {
            //count down time
            _count1Second += Time.deltaTime;
            if (_count1Second >= 1f) {
                _count1Second -= 1f;
                _remainTime -= 1;
                if (_remainTime <= 0) {
                    //done
                    LoadData(); // reload data
                } else {
                    txtRemainTime.text = Configuration.ToStringDateTimeHMS(Mathf.RoundToInt(_remainTime));
                }
            }
        }
    }

    private void LoadData() {
        var data = Configuration.instance.GetFreeGemData();
        _amount = data.amount;
        _isReady = data.isFreeGem;
        _remainTime = data.remainTime;
        _runTime = data.runTime;
        UpdateUI();
    }

    private void UpdateUI() {
        if (_runTime) {
            txtRemainTime.text = Configuration.ToStringDateTimeHMS(Mathf.RoundToInt(_remainTime));
        }

        txtRemainTime.gameObject.SetActive(_runTime);

        if (_amount != 0) {
            btnFree.SetActive(_isReady && !_runTime);
            btnWatchAds.SetActive(!_isReady && !_runTime);
            diamondBonusText.text = RemoteConfigBase.instance.ShopUI_Revamp_Enable
                ? $"<b>{_amount}</b> {LocalizationManager.instance.GetLocalizedValue("DIAMONDS").ToLower()}"
                : $"+<b>{_amount}</b> {LocalizationManager.instance.GetLocalizedValue("DIAMONDS").ToLower()}";
            LocalizationManager.instance.UpdateFont(diamondBonusText);

            if (Util.IsHomeScene()) {
                HomeManager.instance.SetActiveFreeBadge(Configuration.instance.IsShowFreeGemPack? !_runTime : _isReady);
            }
        } else {
            btnFree.SetActive(false);
            btnWatchAds.SetActive(false);
            diamondBonusText.text = string.Empty;
            txtRemainTime.text = string.Empty;
            if (Shop.instance != null) {
                Shop.instance.EnablePackViewAds(); //mở lại gói thg
            }
        }
    }

    public void OnBtnClick() {
        SoundManager.PlayGameButton();
        if (_isReady) {
            // nhận gem luôn
            LogEvent(TRACK_NAME.free_gem_claim, "free");
            LogEvent(TRACK_NAME.free_gem_success, "free");

            Configuration.instance.ReceiveFreeGem(_amount);
            _free_gem_no = 1;
            if (Util.IsHomeScene()) {
                HomeManager.instance.SetActiveFreeBadge(false);
            }

            LoadData();
        } else {
            // watch video vì không đủ thời gian
            _free_gem_no++;
            LogEvent(TRACK_NAME.free_gem_claim, "ads");
            AdsManager.instance.ShowRewardAds(VIDEOREWARD.currency, null,
                location: LOCATION_NAME.free_gem_iap_shop.ToString(), true, result => {
                    if (result) {
                        // nhận gem, reset count
                        LogEvent(TRACK_NAME.free_gem_success, "ads");
                        Configuration.instance.ReceiveFreeGem(_amount);
                        LoadData();
                    }
                });
        }
    }

    private void LogEvent(string eventName, string claimType) {
        Dictionary<string, object> param = new Dictionary<string, object> {
            {TRACK_PARAM.screen, "iap_shop"},
            {TRACK_PARAM.claim_type, claimType},
            {TRACK_PARAM.free_gem_no, _free_gem_no},
        };

        if (eventName == TRACK_NAME.free_gem_claim) {
            param.Add(TRACK_PARAM.currency_inventory, Configuration.instance.GetDiamonds());
        }

        AnalyticHelper.UpdateParamsAccumulatedCount(param, eventName);
        AnalyticHelper.LogEvent(eventName, param);
    }
}