using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class HomeTooltip : MonoBehaviour
{
    public Button vipButton;
    public Button shopButton;

    private void Awake() {
        vipButton.onClick.AddListener(ShowSubscriptionOnboarding);
        shopButton.onClick.AddListener(ShowShop);
    }

    private void OnEnable() {
        vipButton.gameObject.SetActive(false);
        shopButton.gameObject.SetActive(false);
    }

    public void ShowSubscriptionOnboarding() {
        HomeManager.instance.ShowSubscriptionOnboarding();
        Close();
    }
    public void ShowShop() {
        HomeManager.instance.ShowBallList();
        Close();
    }
    public void Close() {
        gameObject.SetActive(false);
    }
}
