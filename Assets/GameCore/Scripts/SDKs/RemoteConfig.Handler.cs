using System.Collections.Generic;
using UnityEngine;
using System;
using System.Globalization;
using System.Reflection;
using Facebook.MiniJSON;
using Firebase.RemoteConfig;
using Inwave;
using TileHop.ChallengeOldUser;
using TileHop.EconomySystem;

public partial class RemoteConfig {
    // End load remote config and process the final config
    protected override void FinalizeConfig() {
        //Disable if local and remote event is difference
        UpdateNativeAds();

        //Force to disable/enable these functions:
        Enable_ACM = true;

        if (!OnboardingFlow_EnableProgressBar) {
            PlayerPrefs.SetInt(CONFIG_STRING.OnboardingFeature_ProgressFirstStar, 1);
        }

        if (Utils.IsAndroid()) {
            CustommizeSub_FsPromoSubVideo = false;
            CustommizeSub_RwPromoSubVideo = false;
            downloadWhenWatchingRW = false;
        } else {
            Reborn_AddVipButton = true;
        }

        if (Configuration.IntroIsOn() && !Enable_Tutorial) {
            Configuration.SetIntroOff();
            Configuration.instance.UpdateIsTutorial();
        }
#if UNITY_IOS
        Enable_LocalSong = false;
#endif

        if (Configuration.isAdmin) {
            Theme_ForceID = PlayerPrefs.GetInt(CONFIG_STRING.ForceTheme, Theme_ForceID);
        }

        if (_defaultEarningConfig == null) {
            //Logger.Log("[Earning Config] - Create default earning config");
            _defaultEarningConfig = new DefaultEarningConfig();
            _defaultEarningConfig.Economy_MileStone_Enable = Economy_MileStone_Enable;
            _defaultEarningConfig.Economy_Diamond_Ingame_StartDiamond = Economy_Diamond_Ingame_StartDiamond;
            _defaultEarningConfig.LongTile_IndexLine = LongTile_IndexLine;
            _defaultEarningConfig.SevenDayMission_IsEnable = SevenDayMission_IsEnable;
        }

        if (ChallengeOldUser_IsEnable) {
            ChallengeOldUserController.instanceSafe.Init(ChallengeOldUser_Config);
        }

        if (TrySkin_Enable) {
            TrySkinFeature.InitData();
        }

        SeasonalPackManager.SetupIapSeasonalPack();
        CustomException.SetKey(FirebaseKey.IronSourceSegment, IronSourceSegment);

        PlayerPrefs.SetFloat(PlayerPrefsKey.RemoteConfig_TimeCached, RemoteConfig_TimeCached);
        if (LiveEvent_BallSpinner_Enable) {
            BallSpinnerManager.instanceSafe.Init();
        }
    }

    #region Multiple Difficult

    public float GetPerfectSize() {
        float perfectSize = Tile_PerfectSize; //default

        if (NotesDifficult_IsEnable) {
            switch (NotesManager.instance.GetDifficultiesOfCurrentSong()) {
                case NotesManager.Difficulties.Easy:
                    perfectSize = NotesDifficult_Easy_PerfectSize;
                    break;

                case NotesManager.Difficulties.Normal:
                    perfectSize = NotesDifficult_Normal_PerfectSize;
                    break;

                case NotesManager.Difficulties.Hard:
                    perfectSize = NotesDifficult_Hard_PerfectSize;
                    break;
            }
        }

        return perfectSize;
    }

    public float GetTileMaxPositionX() {
        if (NotesDifficult_IsEnable &&
            NotesManager.instance.GetDifficultiesOfCurrentSong() == NotesManager.Difficulties.Hard) {
            return NotesDifficult_Hard_MaxPositionX;
        }

        return Tile_MaxPositionX;
    }

    public float GetTileSize() {
        if (NotesDifficult_IsEnable &&
            NotesManager.instance.GetDifficultiesOfCurrentSong() == NotesManager.Difficulties.Hard) {
            return NotesDifficult_Hard_Tile_Size;
        }

        return Tile_Size;
    }

    public float GetTileMaxSize() {
        if (NotesDifficult_IsEnable &&
            NotesManager.instance.GetDifficultiesOfCurrentSong() == NotesManager.Difficulties.Hard) {
            return NotesDifficult_Hard_Tile_MaxSize;
        }

        return Tile_MaxSize;
    }

    public float GetTileMinSize() {
        if (NotesDifficult_IsEnable &&
            NotesManager.instance.GetDifficultiesOfCurrentSong() == NotesManager.Difficulties.Hard) {
            return NotesDifficult_Hard_Tile_MinSize;
        }

        return Tile_MinSize;
    }

    public float GetLevelBotHardPercent() {
        if (NotesDifficult_IsEnable) {
            switch (NotesManager.instance.GetDifficultiesOfCurrentSong()) {
                case NotesManager.Difficulties.Hard:
                    return NotesDifficult_Hard_LevelBot_HardPercent; //0.6

                case NotesManager.Difficulties.Easy:
                    return NotesDifficult_Easy_LevelBot_HardPercent; //0.85
            }

            return NotesDifficult_Hard_LevelBot_HardPercent; //0.85
        }

        return LevelBot_HardPercent; //0.7
    }

    public string GetMidiFilter_Min() {
        if (NotesDifficult_IsEnable &&
            NotesManager.instance.GetDifficultiesOfCurrentSong() == NotesManager.Difficulties.Hard) {
            return NotesDifficult_Hard_MidiFilter_Min;
        }

        return MidiFilter_Min;
    }

    public string GetMidiFilter_Max() {
        if (NotesDifficult_IsEnable &&
            NotesManager.instance.GetDifficultiesOfCurrentSong() == NotesManager.Difficulties.Hard) {
            return NotesDifficult_Hard_MidiFilter_Max;
        }

        return MidiFilter_Max;
    }

    public int GetTileTotalFront() {
        if (NotesDifficult_IsEnable &&
            NotesManager.instance.GetDifficultiesOfCurrentSong() == NotesManager.Difficulties.Hard) {
            return NotesDifficult_Hard_Tile_TotalFront;
        }

        return Tile_TotalFront;
    }

    public int GetMusicalization_MoodChange_ColorTileId() {
        if (NotesDifficult_IsEnable) {
            switch (NotesManager.instance.GetDifficultiesOfCurrentSong()) {
                case NotesManager.Difficulties.Normal:
                    return NotesDifficult_MoodChange_ColorTileId_Normal;

                case NotesManager.Difficulties.Hard:
                    return NotesDifficult_MoodChange_ColorTileId_Hard;
            }
        }

        return Musicalization_MoodChange_ColorTileId;
    }

    #endregion

    #region GamePlay

    public float GetTile_MovingTime() {
        if (NotesDifficult_IsEnable &&
            NotesManager.instance.GetDifficultiesOfCurrentSong() == NotesManager.Difficulties.Hard) {
            return NotesDifficult_Hard_Tile_MovingTime;
        }

        return Tile_MovingTime;
    }

    public int GetTile_PerfectTrigger() {
        if (NotesDifficult_IsEnable &&
            NotesManager.instance.GetDifficultiesOfCurrentSong() == NotesManager.Difficulties.Hard) {
            return NotesDifficult_Hard_Tile_PerfectTrigger;
        }

        return Tile_PerfectTrigger;
    }

    public int GetTile_BadTrigger() {
        if (NotesDifficult_IsEnable &&
            NotesManager.instance.GetDifficultiesOfCurrentSong() == NotesManager.Difficulties.Hard) {
            return NotesDifficult_Hard_Tile_BadTrigger;
        }

        return Tile_BadTrigger;
    }

    #endregion

    private Shop.ShopRemoteConfig remoteShopConfig = null;

    public Shop.ShopRemoteData GetShopRemoteData(string idPack) {
        if (remoteShopConfig == null) {
            try {
                if (string.IsNullOrEmpty(DiamondShop_Config)) { //get default levels
                    remoteShopConfig = new Shop.ShopRemoteConfig();
                } else {
                    remoteShopConfig = JsonUtility.FromJson<Shop.ShopRemoteConfig>(DiamondShop_Config);
                }
            } catch (Exception e) {
                remoteShopConfig = new Shop.ShopRemoteConfig();
                Debug.LogError(e.Message);
            }
        }

        if (remoteShopConfig == null || remoteShopConfig.packages == null || remoteShopConfig.packages.Count == 0) {
            return null;
        } else {
            foreach (var item in remoteShopConfig.packages) {
                if (item.idPack.Equals(idPack))
                    return item;
            }

            return null;
        }
    }

    public Shop.ShopRemoteData GetSalePackage() {
        try {
            var remoteData = JsonUtility.FromJson<Shop.ShopRemoteData>(Economy_dia_sale_IAP_packageSale);
            return remoteData;
        } catch (Exception) {
            Logger.LogError($"Sale config error!!! {Economy_dia_sale_IAP_packageSale}");
            return null;
        }
    }

    private Dictionary<string, int> freeGiftConfig;

    public int GetFreeGiftValue() {
        if (freeGiftConfig == null) {
            freeGiftConfig = new Dictionary<string, int>();
            try {
                var dict = (Dictionary<string, System.Object>) Json.Deserialize(FreeGift_Config);
                foreach (var item in dict) {
                    freeGiftConfig.Add(item.Key, int.Parse(item.Value.ToString()));
                }
            } catch (Exception ex) {
                Logger.LogError(ex.Message);
            }
        }

        if (freeGiftConfig == null || !freeGiftConfig.ContainsKey("value")) {
            return -1;
        } else {
            return freeGiftConfig["value"];
        }
    }

    public int GetFreeGiftTime() {
        if (freeGiftConfig == null) {
            freeGiftConfig = new Dictionary<string, int>();
            try {
                var dict = (Dictionary<string, System.Object>) Json.Deserialize(FreeGift_Config);
                foreach (var item in dict) {
                    freeGiftConfig.Add(item.Key, int.Parse(item.Value.ToString()));
                }
            } catch (Exception ex) {
                Logger.LogError(ex.Message);
            }
        }

        if (freeGiftConfig == null || !freeGiftConfig.ContainsKey("cooldown")) {
            return -1;
        } else {
            return freeGiftConfig["cooldown"];
        }
    }

    public void RestoreEconomyRemoteConfig() {
        if (_defaultEarningConfig == null) {
            return;
        }

        Economy_MileStone_Enable = _defaultEarningConfig.Economy_MileStone_Enable;
        Economy_Diamond_Ingame_StartDiamond = _defaultEarningConfig.Economy_Diamond_Ingame_StartDiamond;
        if (!Configuration.isAdmin) {
            LongTile_IndexLine = _defaultEarningConfig.LongTile_IndexLine;
        }

        SevenDayMission_IsEnable = _defaultEarningConfig.SevenDayMission_IsEnable;
    }

    protected override void TrackAfterLoad(LastFetchStatus status, string errorMsg) {
        switch (status) {
            case LastFetchStatus.Failure:
            case LastFetchStatus.Pending:
                EconomyTrackingEvents.TrackLoadEconomyConfig(LoadStatus.fail, errorMsg, Economy_Version);
                break;

            case LastFetchStatus.Success:
                if (Economy_IsEnable) {
                    EconomyTrackingEvents.TrackLoadEconomyConfig(LoadStatus.success, string.Empty, Economy_Version);
                } else {
                    EconomyTrackingEvents.TrackLoadEconomyConfig(LoadStatus.fail, "Load success but not enable economy",
                        Economy_Version);
                }

                break;
        }
    }

    private string                     _economyAppliedIsoCode;
    private string[]                   _economySupportedCountries;
    private Dictionary<string, string> _localEconomySettings;
    private DeviceCarrierInfo deviceCarrier => Configuration.instance.deviceCarrier;

    private bool CheckEconomySupportedCountry() {
        if (deviceCarrier == null) {
            return false;
        }

        if (string.IsNullOrEmpty(_economyAppliedIsoCode)) {
            _economyAppliedIsoCode = deviceCarrier.GetIsoCountryCode();
        }

        if (_economySupportedCountries == null) {
            _economySupportedCountries = Economy_AppliedCountries.Split(",");
        }

        for (int i = 0; i < _economySupportedCountries.Length; i++) {
            if (_economySupportedCountries[i].Equals(_economyAppliedIsoCode)) {
                return true;
            }
        }

        return false;
    }

    protected override void HandleLoadLocalEconomy() {
        if (string.IsNullOrEmpty(Economy_AppliedCountries) || Economy_LocalEconomySettings == null) {
            return;
        }

        if (CheckEconomySupportedCountry() || dev_forceEconomyCountries) {
            LoadLocalEconomyConfig();
            ApplyDefaultLocalConfigs(_localEconomySettings);
        }
    }

    private Dictionary<string, string> LoadResourcesConfig(string csv) {
        var rows = csv.Split('\n');
        var dict = new Dictionary<string, string>();
        for (int i = 1; i < rows.Length; i++) {
            var rowObject = CSVReader.SplitCsvLine(rows[i], true);
            if (rowObject.Length > 1) {
                dict.Add(rowObject[0], rowObject[1]);
            }
        }

        return dict;
    }

    private void LoadLocalEconomyConfig() {
        Logger.LogError("[Economy]: load local eco config");
        var csv = Economy_LocalEconomySettings.text;
        var rows = csv.Split('\n');
        _localEconomySettings = new Dictionary<string, string>();
        for (int i = 1; i < rows.Length; i++) {
            var rowObject = CSVReader.SplitCsvLine(rows[i], true);
            if (rowObject.Length > 1) {
                _localEconomySettings.Add(rowObject[0], rowObject[1]);
            }
        }
    }

    private void ApplyDefaultLocalConfigs(Dictionary<string, string> configs) {
        if (configs != null && configs.Count > 0) {
            foreach (var keyValuePair in configs) {
                var key = keyValuePair.Key.Trim();
                if (key.Contains("Settings")) {
                    try {
                        //reformat from escaped json to normal json
                        var json = keyValuePair.Value.Replace("\\\"", string.Empty);
                        Dictionary<string, System.Object> jsonDict =
                            (Dictionary<string, System.Object>) Json.Deserialize(json);
                        MergePreKeys(jsonDict, key.Replace("Settings", string.Empty));
                    } catch (Exception e) {
                        Logger.EditorLogError("[ApplyDefaultLocalConfigs]",
                            $"Key {key} Value {keyValuePair.Value} Error: {e.Message}");
                    }
                } else {
                    try {
                        FieldInfo item = typeof(RemoteConfig).GetField(key);
                        var value = keyValuePair.Value.Trim();
                        if (item != null) {
                            if (item.FieldType == typeof(string)) {
                                value = value.Replace("\\\"", string.Empty);
                                item.SetValue(instance, value);
                            } else if (item.FieldType == typeof(float)) {
                                item.SetValue(instance, float.Parse(value, CultureInfo.InvariantCulture.NumberFormat));
                            } else if (item.FieldType == typeof(int)) {
                                item.SetValue(instance, int.Parse(value, CultureInfo.InvariantCulture.NumberFormat));
                            } else if (item.FieldType == typeof(bool)) {
                                if (value.ToLower() == "true" || value == "1") {
                                    item.SetValue(instance, true);
                                } else {
                                    item.SetValue(instance, false);
                                }
                            } else if (item.FieldType == typeof(int[])) {
                                item.SetValue(instance, GetIntArray(value));
                            } else if (item.FieldType == typeof(float[])) {
                                item.SetValue(instance, GetFloatArray(value));
                            } else if (item.FieldType == typeof(long)) {
                                item.SetValue(instance, long.Parse(value, CultureInfo.InvariantCulture.NumberFormat));
                            } else {
                                Debug.LogWarning("[ApplyDefaultLocalConfigs] cannot process type " + item.FieldType);
                            }
                        }
                    } catch (Exception ex) {
                        Logger.EditorLogError("[ApplyDefaultLocalConfigs]", "Invalid key: " + key + "--" + ex.Message);
                    }
                }
            }
        }
    }

    private void UpdateNativeAds() {
        if (DeviceHelper.IsLowEnd()) {
            isEnableAnzu = false;
            Adverty_IsEnable = false;
            NativeAds_Style = -1;
        }

        switch (NativeAds_Style) {
            case 0:
                isEnableAnzu = true;
                Adverty_IsEnable = false;
                break;

            case 1:
                isEnableAnzu = true;
                Adverty_IsEnable = true;

                if (!CanShowAdverty()) {
                    Adverty_IsEnable = false;
                }

                break;

            case 2:
                isEnableAnzu = false;
                Adverty_IsEnable = true;

                if (!CanShowAdverty()) {
                    isEnableAnzu = true;
                    Adverty_IsEnable = false;
                }

                break;
        }
    }

    private bool CanShowAdverty() {
        List<string> ignoreModels = Util.StringToList(Adverty_IgnoreModel, new[] { ';' });
        string deviceModel = SystemInfo.deviceModel;
        foreach (string s in ignoreModels) {
            if (deviceModel.Contains(s)) {
                if (Configuration.instance.isTutorial) {
                    CustomException.Fire("ForceIgnoreModel",
                        "Device is ignore model to show adverty => " + deviceModel);
                }
                return false;
            }
        }

        return true;
    }

    public string GetBannerIDs() {
        return Utils.IsAndroid() ? Android_LevelPlay_BannerIDs : IOS_LevelPlay_BannerIDs;
    }

    public string GetInterstitialIDs() {
        return Utils.IsAndroid() ? Android_LevelPlay_InterstitialIDs : IOS_LevelPlay_InterstitialIDs;
    }

    public string GetRewardIDs() {
        return Utils.IsAndroid() ? Android_LevelPlay_RewardIDs : IOS_LevelPlay_RewardIDs;
    }
}