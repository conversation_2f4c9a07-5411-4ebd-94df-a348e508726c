#if UNITY_EDITOR
using UnityEngine;

public class UITestBanner : MonoBehaviour {
    [SerializeField] private RectTransform canvasRectTransform;
    [SerializeField] private RectTransform rectBanner;

    private Vector2 _canvasSize;

    private void Awake() {
        DontDestroyOnLoad(this.gameObject);
    }

    private void OnEnable() {
        _canvasSize = canvasRectTransform.sizeDelta;
        AdsManager.instance.OnBannerChange += InstanceOnOnBannerChange;
        InstanceOnOnBannerChange();
    }

    private void OnDisable() {
        if (AdsManager.instance) {
            AdsManager.instance.OnBannerChange -= InstanceOnOnBannerChange;
        }
    }

    private void InstanceOnOnBannerChange() {
        float height = ConvertDpToPixel(AdsManager.instance.GetBannerHeight(), true);
        rectBanner.sizeDelta = new Vector2(rectBanner.sizeDelta.x, height);

        if (AdsManager.instance.IsBottomBannerShown()) {
            rectBanner.anchorMin = Vector2.zero;
            rectBanner.anchorMax = Vector2.right;
            rectBanner.pivot = new Vector2(0.5f, 0);
            rectBanner.anchoredPosition = Vector2.zero;
        } else {
            rectBanner.anchorMin = Vector2.up;
            rectBanner.anchorMax = Vector2.one;
            rectBanner.pivot = new Vector2(0.5f, 1);
            rectBanner.anchoredPosition = Vector2.zero;
        }
    }

    private float ConvertDpToPixel(float dp, bool isBottom) {
        //Logger.Log($"[Banner] dp: {dp}");
        //Logger.Log($"[Banner] ScreenH: {Screen.height}");
        //Logger.Log($"[Banner] ScreenDPI: {Screen.dpi}");
//#if UNITY_EDITOR
//        float bannerSizePixels = dp;
#if UNITY_ANDROID
        float bannerSizePixels = dp * (Screen.dpi / 160);
#elif UNITY_IOS
        float bannerSizePixels = dp * (iOSDPI.dpi / 160);
#else
        float bannerSizePixels = dp;
#endif
        //Logger.Log($"[Banner] pixel: {bannerSizePixels}");

        //Logger.Log($"[Banner] systemH: {Display.main.systemHeight}");
        //Logger.Log($"[Banner] renderH: {Display.main.renderingHeight}");
        if (Display.main.systemHeight == Display.main.renderingHeight) {
            Rect safeArea = Screen.safeArea;
            float offsetWithSafeArea = (isBottom ? safeArea.y : (Inwave.Utils.GetHeight() - (safeArea.y + safeArea.height)));
            bannerSizePixels -= offsetWithSafeArea;
            //Logger.Log($"[Banner] pixel fixed: {bannerSizePixels}");
        }

        float bannerSizeCanvas = bannerSizePixels / Inwave.Utils.GetHeight() * _canvasSize.y;
        //Logger.Log($"[Banner] incanvas: {bannerSizeCanvas}");
        return bannerSizeCanvas;
    }
}
#endif