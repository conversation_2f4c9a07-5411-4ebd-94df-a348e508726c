using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using AppsFlyerConnector;
using UnityEngine;
using AppsFlyerSDK;
using DG.Tweening;
using Inwave.CommunicationSystem;
using Sirenix.OdinInspector;
using UnityEngine.Serialization;

public class AppsFlyerInit : AppsFlyer, IAppsFlyerConversionData {
    public        string Deeplink_onConversionDataSuccess = "onConversionDataSuccess";
    private const string Campaign                         = "campaign";
    private const string Scheme                           = "af_dp";
    private const string Navigate                         = "navigate";
    private const string Popup                            = "popup";

    #region properties

    public static AppsFlyerInit instanceAFI => PreSDKs.isInstanced ? PreSDKs.instance.appsFlyerInit : null;
    public static bool isInstanced => instanceAFI != null;

    public enum KEY {
        tad_impression,
    }

    public string iosAppId;
    public string appsFlyerKey;

    [HideInInspector] public bool         isOpenGameByOnelink;
    [HideInInspector] public string       artist_id;
    [HideInInspector] public List<string> songIds;

    private string _deeplinkType;
    private string _navigate;

    const string clickEventName = "click_event";

    [ShowInInspector] private ArrayList _fullEventList;

    // Mark AppsFlyer CallBacks
    const string DIC_AF_STATUS               = "af_status";
    const string DIC_IS_FIRST_LAUNCH         = "is_first_launch";
    const string DIC_INSTALL_TIME            = "install_time";
    const string DIC_AF_MESSAGE              = "af_message";
    const string EVENT_NEW_USER_INSTALLATION = "new_user_installation";

    public static bool IsInited = false;
    public static event Action OnInited;
    public static event Action OnReceiveCampaign;
    public static event Action OnReceiveScheme;

    #endregion

    #region Unity functions

    private void Awake() {
        Util.WaitRemoteConfigDone(UpdateFirebaseAllowEvents);
    }

    private void Start() {
        // For detailed logging
        if (Configuration.instance.isDebug) {
            AppsFlyer.setIsDebug(true);
        }

        var appId = Application.platform == RuntimePlatform.IPhonePlayer ? iosAppId : Application.identifier;
        AppsFlyer.initSDK(appsFlyerKey, appId, this);
        InitPurchaseConnector(this);

#if UNITY_IOS //&& !UNITY_EDITOR
        // 2024/04/29 Hotfix for DnI tracking
        AppsFlyer.waitForATTUserAuthorizationWithTimeoutInterval(60);
#endif

        AppsFlyer.startSDK();
        AppsFlyer.OnDeepLinkReceived += OnProcessDeepLinkReceived;

#if UNITY_EDITOR
        //TestOnelinkOpenPopup();
        InitDone();
        //TestDelayCampaign();
        //TestDelayScheme();
#endif

        //AppsFlyerAdRevenue.start(); //TH-762
    }

    private void OnDestroy() {
        AppsFlyer.OnDeepLinkReceived -= OnProcessDeepLinkReceived;
    }

    #endregion

    private void InitPurchaseConnector(MonoBehaviour appsflyerMonoBehaviour = null) {
        AppsFlyerPurchaseConnector.init(appsflyerMonoBehaviour, Store.GOOGLE); // appsflyerMonoBehaviour is us
        AppsFlyerPurchaseConnector.setIsSandbox(false);
        AppsFlyerPurchaseConnector.setAutoLogPurchaseRevenue(
            AppsFlyerAutoLogPurchaseRevenueOptions.AppsFlyerAutoLogPurchaseRevenueOptionsAutoRenewableSubscriptions,
            AppsFlyerAutoLogPurchaseRevenueOptions.AppsFlyerAutoLogPurchaseRevenueOptionsInAppPurchases);
        AppsFlyerPurchaseConnector.setPurchaseRevenueValidationListeners(true);
        AppsFlyerPurchaseConnector.build();
        AppsFlyerPurchaseConnector.startObservingTransactions();
    }

    private void UpdateFirebaseAllowEvents() {
        //Inwave.Utils.ShowTimeAction(GetType().Name, System.Reflection.MethodBase.GetCurrentMethod()?.Name);

        AfEventProcess.Init(RemoteConfigBase.instance.AfEventsConfig);

        if (!string.IsNullOrEmpty(RemoteConfigBase.instance.AfFullEventsConfig)) {
            var events = RemoteConfigBase.instance.AfFullEventsConfig.Split(';', StringSplitOptions.RemoveEmptyEntries);
            _fullEventList = new ArrayList(events);
        }
    }

    #region Conversion events

    private void InitDone() {
        if (IsInited)
            return;

        IsInited = true;
        OnInited?.Invoke();
    }

    // Mr.Trung request update 2023-03-09
    public void onConversionDataSuccess(string conversionData) {
        InitDone();
        AFLog(Deeplink_onConversionDataSuccess, conversionData);
        if (string.IsNullOrWhiteSpace(conversionData))
            return;

        Dictionary<string, object> dic = AppsFlyer.CallbackStringToDictionary(conversionData);
        if (dic == null || dic.Count == 0) {
            return;
        }

        /////////// Fire one time only to map user for DnI Team
        // true if this ref is not exist, it means not fire yet
        bool canFireNewUserInstallation = PlayerPrefs.GetInt(EVENT_NEW_USER_INSTALLATION, -1) <= 0;
        if (canFireNewUserInstallation) {
            string mediaSource = dic.ContainsKey(DIC_AF_STATUS) ? dic[DIC_AF_STATUS].ToString() : "";
            if (dic.ContainsKey(DIC_IS_FIRST_LAUNCH)) {
                bool isFirstLaunch = (bool) dic[DIC_IS_FIRST_LAUNCH];
                if (isFirstLaunch) {
                    string installTime = "";
                    if (dic.ContainsKey(DIC_INSTALL_TIME))
                        installTime = dic[DIC_INSTALL_TIME].ToString();
                    string afMessage = "";
                    if (dic.ContainsKey(DIC_AF_MESSAGE))
                        afMessage = dic[DIC_AF_MESSAGE].ToString();

                    Dictionary<string, object> param = new Dictionary<string, object>();
                    param.Add("appsflyer_id", AppsFlyer.getAppsFlyerId());
                    param.Add("media_source", mediaSource);
                    param.Add(DIC_INSTALL_TIME, installTime);
                    param.Add(DIC_AF_MESSAGE, afMessage);
                    //param.Add("ama_device_id", PlayerPrefs.GetString("ama_device_id"));
                    param.Add("ama_id", PlayerPrefs.GetString("AMA_ID_GENERATE_MAPPING"));

                    AnalyticHelper.FireString(EVENT_NEW_USER_INSTALLATION, param);
                }
            }

            PlayerPrefs.SetInt(EVENT_NEW_USER_INSTALLATION, 1); // mark as sent
        }
        /////////// End

        // add deferred deeplink logic here
        ApplyDeepLink(dic, Deeplink_onConversionDataSuccess);

        //string irsUserID = "AF-" + AppsFlyer.getAppsFlyerId();
        //IronSource.Agent.setUserId(irsUserID);
        //UnityEngine.Debug.Log("Set IronSource UserID :::::::::::: " + irsUserID);
    }

    public void onConversionDataFail(string error) {
        AFLog("onConversionDataFail", error);
        InitDone();
    }

    public void onAppOpenAttribution(string attributionData) {
        AFLog("onAppOpenAttribution", attributionData);
        ApplyDeepLink(AppsFlyer.CallbackStringToDictionary(attributionData), "onAppOpenAttribution");
    }

    /// <summary>
    /// TestOnelink
    /// </summary>
    private void TestOnelinkNavigate() {
        Debug.LogWarning("TestOnelink => only for editor");
        isOpenGameByOnelink = true;
        _deeplinkType = Navigate;
        _navigate = "HOME/SONG_LIST/95a88e74-2fdd-4a5e-8a6e-cce4120e46ac";
    }

    private void TestOnelinkOpenPopup() {
        Debug.LogWarning("TestOnelink => only for editor");
        isOpenGameByOnelink = true;
        _deeplinkType = Popup;
        _navigate = "FreeChallenge";
    }

    public void onAppOpenAttributionFailure(string error) {
        AFLog("onAppOpenAttributionFailure", error);
    }

    /// <summary>
    /// OnProcessDeepLinkReceived
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void OnProcessDeepLinkReceived(object sender, EventArgs e) {
        if (e is DeepLinkEventsArgs args) {
            if (args.status == DeepLinkStatus.FOUND) {
                AnalyticHelper.LogEvent("OnProcessDeepLinkReceived_FOUND", args.getDeepLinkDictionary());
                AFLog("OnProcessDeepLinkReceived", "OnProcessDeepLinkReceived");
                ApplyDeepLink(args.getDeepLinkDictionary(), "OnProcessDeepLinkReceived");
            } else {
                AnalyticHelper.LogEvent("OnProcessDeepLinkReceived_" + args.status);
            }
        } else {
            AnalyticHelper.LogEvent("OnProcessDeepLinkReceived_" + e.GetType().Name);
        }
    }

    Dictionary<string, object> deepLinkDatas = new();

    /// <summary>
    /// Get artist_id,song_id from deepLinkData and apply to variables: artist_id & songIds 
    /// </summary>
    /// <param name="deepLinkData">data contains deep link params</param>
    /// <param name="source">For debug log</param>
    private void ApplyDeepLink(Dictionary<string, object> deepLinkData, string source) {
        if (deepLinkData == null) {
            return;
        }

        //Debug log
        if (Configuration.instance.isDebug) {
            string log = Logger.GetString(deepLinkData);
            Debug.Log($"[ApplyDeepLink] [{source}] deepLinkData: " + log);
        }

        //Save deepLinkData
        SaveParam(deepLinkData, "media_source");
        bool hasCampaign = SaveParam(deepLinkData, Campaign);
        if (hasCampaign) {
            OnReceiveCampaign?.Invoke();
        }

        bool hasScheme = SaveParam(deepLinkData, Scheme);
        if (hasScheme) {
            OnReceiveScheme?.Invoke();
        }

        SaveParam(deepLinkData, "is_deferred");

        if (Configuration.instance.isTutorial || source != Deeplink_onConversionDataSuccess) {
            GetOneLink(deepLinkData);
        }
    }

    private void GetOneLink(Dictionary<string, object> deepLinkData) {
        //Artist
        string artist_id = getDeepLinkParameter(deepLinkData, "artist_id");
        if (!string.IsNullOrEmpty(artist_id)) {
            this.artist_id = artist_id;
            isOpenGameByOnelink = true;
        }

        //Song
        string listSongId = getDeepLinkParameter(deepLinkData, "song_id");
        if (!string.IsNullOrEmpty(listSongId)) {
            songIds = listSongId.Replace(" ", "").Split(',').ToList();
            isOpenGameByOnelink = true;
        }

        //path
        string deepLinkNavigate = getDeepLinkParameter(deepLinkData, Navigate);
        if (!string.IsNullOrEmpty(deepLinkNavigate)) {
            if (deepLinkNavigate.Contains("__")) {
                deepLinkNavigate = deepLinkNavigate.Replace("__", "/");
            }

            _navigate = deepLinkNavigate;
            isOpenGameByOnelink = true;
            _deeplinkType = Navigate;
            SaveParam(TRACK_PARAM.type, _deeplinkType);
            SaveParam(TRACK_PARAM.path, deepLinkNavigate);
        }

        string deepLinkOpenPopup = getDeepLinkParameter(deepLinkData, Popup);
        if (!string.IsNullOrEmpty(deepLinkOpenPopup)) {
            if (deepLinkOpenPopup.Contains("__")) {
                deepLinkOpenPopup = deepLinkOpenPopup.Replace("__", "/");
            }

            _navigate = deepLinkOpenPopup;
            isOpenGameByOnelink = true;
            _deeplinkType = Popup;
            SaveParam(TRACK_PARAM.type, _deeplinkType);
            SaveParam(TRACK_PARAM.path, deepLinkOpenPopup);
        }
    }

    private bool SaveParam(Dictionary<string, object> deepLinkData, string param) {
        string value = getDeepLinkParameter(deepLinkData, param);
        if (!string.IsNullOrEmpty(value)) {
            deepLinkDatas[param] = value;
            return true;
        }

        return false;
    }

    private void SaveParam(string param, string value) {
        deepLinkDatas[param] = value;
    }

    /// <summary>
    /// getDeepLinkParameter
    /// </summary>
    /// <param name="name"></param>
    /// <returns></returns>
    private string getDeepLinkParameter(Dictionary<string, object> deepLinkData, string name) {
        if (deepLinkData != null) {
            if (deepLinkData.ContainsKey(name) && deepLinkData[name] != null) {
                return deepLinkData[name].ToString();
            } else if (deepLinkData.ContainsKey(clickEventName) &&
                       deepLinkData[clickEventName] is Dictionary<string, object>) {
                return getDeepLinkParameter(deepLinkData[clickEventName] as Dictionary<string, object>, name);
            }
        }

        return null;
    }

    #endregion

    #region Uninstall measurement Tracking

    private bool _tokenSent;

    private void Update() {
        if (!_tokenSent && Application.platform == RuntimePlatform.IPhonePlayer && Time.frameCount % 30 == 0) {
            byte[] token = null;
#if UNITY_IOS
            token = UnityEngine.iOS.NotificationServices.deviceToken;
#endif
            // ReSharper disable once ConditionIsAlwaysTrueOrFalse
            if (token != null) {
                registerUninstall(token); //IOS
                _tokenSent = true;
            }
        }
    }

    public void RegisterToken(string token) {
        updateServerUninstallToken(token); //Android
    }

    #endregion

    #region Log events

    public void LogAppsFlyerEvent(string eventName, Dictionary<string, object> param = null) {
        if (IsCustomEvent(eventName)) {
            SendCustomEvent(eventName, param);
            
        } else {
            StartCoroutine(AfEventProcess.ProcessAndLogEventName(eventName, param));
        }
    }

    private bool IsCustomEvent(string eventName) {
        if (_fullEventList != null && _fullEventList.Contains(eventName)) {
            return true;
        }

        if (eventName.Equals(SONG_STATUS.song_end.ToString())) {
            return true;
        }

        return false;
    }

    private void SendCustomEvent(string eventName, Dictionary<string, object> fullParams) {
        if (eventName.Equals(SONG_STATUS.song_end.ToString())) {
            Dictionary<string, object> afParam = new();
            if (fullParams != null && fullParams.TryGetValue(TRACK_PARAM.accumulated_count, out object value)) {
                afParam.Add(TRACK_PARAM.accumulated_count, value);
            }

            LogEvent(eventName, afParam);
            
        } else {
            LogEvent(eventName, fullParams);
        }
    }

    public static void LogEvent(string eventName, Dictionary<string, object> param = null) {
        Dictionary<string, string> afParams = new();

        if (param != null) {
            foreach (var item in param) {
                if (!string.IsNullOrEmpty(item.Key) && item.Value != null) {
                    afParams.Add(item.Key, item.Value.ToString());
                }
            }
        }

        if (Application.isEditor || Configuration.isAdmin) {
            Log(eventName, afParams);
        }

        AppsFlyer.sendEvent(eventName, afParams);
    }

    #endregion

    public void GetParams(Dictionary<string, object> dicParam, params string[] paramNames) {
        foreach (string paramName in paramNames) {
            if (deepLinkDatas.TryGetValue(paramName, out object data)) {
                dicParam[paramName] = data;
            }
        }
    }

    private void TestDelayCampaign() {
        DOVirtual.DelayedCall(10f, () => {
            Dictionary<string, object> deepLinkData = new Dictionary<string, object>() {
                { Campaign, "BHaUSRemerge" }
            };
            bool hasCampaign = SaveParam(deepLinkData, Campaign);
            if (hasCampaign) {
                OnReceiveCampaign?.Invoke();
            }
        });
    }

    private void TestDelayScheme() {
        DOVirtual.DelayedCall(10f, () => {
            Dictionary<string, object> deepLinkData = new Dictionary<string, object>() {
                { Scheme, "tileshop://challenge" }
            };
            bool hasScheme = SaveParam(deepLinkData, Scheme);
            if (hasScheme) {
                OnReceiveScheme?.Invoke();
            }
        });
    }

    public object GetParam(string paramName) {
        return deepLinkDatas.GetValueOrDefault(paramName);
    }

    public string GetCampaign() {
        if (deepLinkDatas.TryGetValue(Campaign, out object data)) {
            return data.ToString();
        }

        return null;
    }

    public string GetScheme() {
        if (deepLinkDatas.TryGetValue(Scheme, out object data)) {
            return data.ToString();
        }

        return null;
    }

    public void DeeplinkAction() {
        if (!isOpenGameByOnelink || string.IsNullOrEmpty(_navigate)) {
            return;
        }

        if (_deeplinkType.Equals(Navigate)) {
            ActionSystemManager.instance.Navigate(_navigate);
        } else if (_deeplinkType.Equals(Popup)) {
            ActionSystemManager.instance.OpenPopup(_navigate);
        }

        //event
        Dictionary<string, object> lsParam = new() {
            { TRACK_PARAM.type, _deeplinkType },
            { TRACK_PARAM.path, _navigate },
        };
        GetParams(lsParam, "media_source", Campaign, Scheme, "is_deferred");
        AnalyticHelper.FireEvent(FIRE_EVENT.deep_link_navigate, lsParam);

        _navigate = string.Empty;
    }

    public void SetCampaign(string campaignName) {
        if (deepLinkDatas != null) {
            deepLinkDatas[Campaign] = campaignName;
        }
    }

    public void SetScheme(string scheme) {
        if (deepLinkDatas != null) {
            deepLinkDatas[Scheme] = scheme;
        }
    }

    private static void Log(string eventName, Dictionary<string, string> afParams) {
        if (afParams != null && afParams.Count != 0) {
            StringBuilder logBuilder = new();
            logBuilder.Append("[AppsFlyer] ");
            logBuilder.Append(eventName);

            logBuilder.Append("{ ");
            foreach (var p in afParams) {
                logBuilder.Append(" ");
                logBuilder.Append(p.Key);
                logBuilder.Append(" : \"");
                logBuilder.Append(p.Value);
                logBuilder.Append("\";");
            }

            logBuilder.Remove(logBuilder.Length - 1, 1); // xóa bỏ dấu ; cuối cùng
            logBuilder.Append(" }");

            Logger.Log(logBuilder.ToString());
        } else {
            Logger.Log("[AppsFlyer] " + eventName);
        }
    }
}