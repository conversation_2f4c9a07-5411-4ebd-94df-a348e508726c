using System;
using System.Collections.Generic;
using GoogleMobileAds.Ump.Api;
using UnityEngine;

public class CMPWrapper : MonoBehaviour {
    public static CMPWrapper instance;

    public static bool IsEeaOrUK =>
        ConsentInformation.ConsentStatus == ConsentStatus.Obtained ||
        ConsentInformation.ConsentStatus == ConsentStatus.Required;

    public static Action OnConsentCheckPassed;
    public        Action OnConsentFormLoaded;
    public bool ConsentCheckPassed => _consentCheckPassed;

    private bool        _consentCheckPassed;
    private bool        _finishedCheckConsentInfo = false;
    private ConsentForm _consentForm;

    public bool FinishedCheckConsentInfo => _finishedCheckConsentInfo;

    private void Awake() {
        instance = this;
    }

    private void Start() {
        if (RemoteConfigBase.instance.UserPermissions_CMP_IsEnable) {
            CheckConsentInformation();
        }
    }

    /// <summary>
    /// Load the consent form if possible
    /// </summary>
    public void TryLoadConsentForm() {
        if (_finishedCheckConsentInfo) {
            LoadConsentForm();
        }
    }

    private void LoadConsentForm() {
        bool skipShowConsent = !RemoteConfigBase.instance.UserPermissions_CMP_IsEnable ||
                               !UserPermissions.IsATTAllowed() || _consentCheckPassed;

        if (skipShowConsent) {
            PassConsentCheck();
            return;
        }

        Time.timeScale = 0;
        //Logger.Log("[CMP] Setting time scale to 0 to wait for form completion.");

        ConsentForm.Load((form, error) =>
            UnityMainThreadDispatcher.Instance().Enqueue(() => { OnLoadConsentForm(form, error); }));
        //Logger.Log("[CMP] Loading consent form...");
    }

    private void PassConsentCheck() {
        Time.timeScale = 1;
        _consentCheckPassed = true;
        //Logger.Log("[CMP] Setting time scale to 1 as consent check has been passed.");
        OnConsentCheckPassed?.Invoke();
    }

    private void TrySkipConsentCheck() {
        //Logger.Log("[CMP] Trying to skip consent check...");
        if (_consentCheckPassed || _consentForm != null)
            return;

        PassConsentCheck();
        //Logger.Log("[CMP] Consent check skipped");
    }

    public void CheckConsentInformation() {
        // Do not remove this part, no ConsentDebugSettings is known to cause issues in GMA Unity plugin 7.4.0 & 7.4.1
        // this issue is fixed in 8.0.0, can safely remove if you are using that version
        // ref: https://github.com/googleads/googleads-mobile-unity/issues/2543
        var debugSettings = new ConsentDebugSettings {
            // Geography appears as in EEA for debug devices.
            DebugGeography = DebugGeography.EEA,
            TestDeviceHashedIds = new List<string> {
                "9A1B0FBB7064C2B2FC685D21D8611C5F", // Inwave's OnePlus BE2029
                "99CD5CC4-EBC1-48B8-9BB3-AF3E17F29CCC", // Inwave's iPhone 11 RED
                "19404A70E44D281641CFF81CFB4F570A", //trungvt
                "E338C1E674118A30A3A7E837254EDFF1", //Tung
            }
        };

        // Here false means users are not under age.
        ConsentRequestParameters request = new ConsentRequestParameters {
            TagForUnderAgeOfConsent = false,
            ConsentDebugSettings = debugSettings,
        };

        // Check the current consent information status.
        ConsentInformation.Update(request, OnConsentInfoUpdated);
        //Logger.Log("[CMP] Updating consent info...");
    }

    private void OnConsentInfoUpdated(FormError error) {
        if (error != null) {
            Logger.LogError($"[CMP] {error.Message}. Code: {error.ErrorCode}");
#if UNITY_ANDROID
            if (error.ErrorCode == 2 || error.ErrorCode == 3) {
                // no internet or or form is setup incorrectly
                // ref: https://developers.google.com/admob/android/privacy/api/reference/com/google/android/ump/FormError.ErrorCode
                PassConsentCheck();
            }
#elif UNITY_IOS
			if (error.ErrorCode == 3 || error.ErrorCode == 4) {
				//no internet or form is setup incorrectly
				//ref: https://developers.google.com/admob/ios/privacy/api/reference/Enums/UMPRequestErrorCode
				PassConsentCheck();
			}
#endif
            return;
        }

        _finishedCheckConsentInfo = true;

        if (ConsentInformation.IsConsentFormAvailable()) {
            if (ConsentInformation.ConsentStatus == ConsentStatus.Obtained) {
                PassConsentCheck();
            }

            //($"[CMP] Consent form available, consent {(_consentCheckPassed ? "" : "not ")}obtained");
        } else if (ConsentInformation.ConsentStatus == ConsentStatus.NotRequired) {
            PassConsentCheck();
            //Logger.Log("[CMP] Consent form not required");
        }
    }

    private void OnLoadConsentForm(ConsentForm consentForm, FormError error) {
        if (error != null) {
            Logger.LogError($"[CMP] {error.Message}. Code: {error.ErrorCode}");
            TrySkipConsentCheck();
            return;
        }

        //Logger.Log("[CMP] Consent form loaded");

        _consentForm = consentForm;

        if (ConsentInformation.ConsentStatus == ConsentStatus.Required) {
            _consentForm.Show(OnFormDismissed);
            OnConsentFormLoaded?.Invoke();
            AnalyticHelper.LogEvent(TRACK_NAME.cmp_popup_show);
            //Logger.Log("[CMP] Consent form required, showing form");
        } else if (ConsentInformation.ConsentStatus == ConsentStatus.NotRequired ||
                   ConsentInformation.ConsentStatus == ConsentStatus.Obtained) {
            PassConsentCheck();
            //Logger.Log("[CMP] Consent form either completed or not required");
        }
    }

    /// <summary>
    /// Callback for when the form is completed (consent obtained) or user closes the consent toast.
    /// </summary>
    /// <param name="error">Thrown error, if any.</param>
    private void OnFormDismissed(FormError error) {
        if (error != null) {
            Logger.LogError($"[CMP] {error.Message}. Code: {error.ErrorCode}");
            TrySkipConsentCheck();
            return;
        }

        PassConsentCheck();
        Dictionary<string, object> param = new Dictionary<string, object> {
            {TRACK_PARAM.status, ConsentInformation.ConsentStatus},
        };
        AnalyticHelper.LogEvent(TRACK_NAME.cmp_popup_finish, param);
        //Logger.Log($"[CMP] Consent check passed. Status: {ConsentInformation.ConsentStatus}");
    }

    public static bool IsAllowed() {
        return ConsentInformation.ConsentStatus == ConsentStatus.Obtained;
    }
}