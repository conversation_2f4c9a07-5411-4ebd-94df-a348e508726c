using UnityEngine;
using System;
using System.Diagnostics.CodeAnalysis;
using UnityEngine.Serialization;

/// <summary>
///  Firebase Remote config
/// </summary>
///
[SuppressMessage("ReSharper", "InconsistentNaming")]
public partial class RemoteConfig : RemoteConfigBase {
    #region Fields

    public static string DefaultSongConfigVersion_Local = "2023.06.11";

    public static string DefaultSongConfigPath_Local =
        "https://d1xncywiopk9ma.cloudfront.net/Songs/SongConfig/acmv4.csv";

    [Header("Admin")] // 
    public bool Admin_isShowLogEvent; //logcat tất cả event đã bắn lên firebase kể cả khi ko là admin

    public bool   Admin_isForceFireEvent; //bắn event lên firebase kể cả khi là admin
    public string Admin_Password = "inwave123";

    [Header("Core:")] // 
    public string RootPath = "https://d1xncywiopk9ma.cloudfront.net/";

    public string SongList_Path      = "";
    public string SongList_Version   = "";
    public bool   SongList_ForceWait = false;

    public string APIKey_ContentReader_Android =
        "U2FsdGVkX19XQILyTaQd+uV8MyMv2yhrp7fPrQZkFejx0LlHl2M4X+OWobpOPKfmGpXt3RA50l/YlXf1/F2huw==";

    public string APIKey_ContentReader_IOS = "U2FsdGVkX19W5P+Yx0hWY03xD6ytqVe1NqhulJKAdO13zPnGo4odB8tIaWYiSoNk";
    public string DMCA_Link                = "https://amanotes.com/copyright";
    public string TOSLink                  = "https://amanotes.com/terms-and-conditions/";

    public string PolicyLink = "https://amanotes.com/privacy-policy/";

    //public string ContentTestLink = "https://dw7aag3976kk8.cloudfront.net/ACS/Config/v1/TEST_EngcQAy4mrlmCAXAI69v/ACM_Config.json";
    public string ContentTestLink =
        "https://sheets.googleapis.com/v4/spreadsheets/14KIaIrBAz5pmycu-dRHs0pfLkXRND9Yw2N_--pnMNuc/values/songconfig-content-tool";

    // nếu thay đổi chú ý sửa LocalizationManager.LanguageVersionLocal + giá trị trên prefab
    public string LanguageRemoteVersion;
    public string LanguageRemotePath         = "https://d1xncywiopk9ma.cloudfront.net/localization.csv";
    public bool   EnableVietnameseFont       = false;
    public bool   EnableVietnameseFontForAll = false;

    public int    ForceUpdate                 = 0;
    public string LatestVersion               = "1.0";
    public string IronSourceSegment           = "";
    public int    Unlock_NewChallenge         = 5;
    public bool   Enable_Tutorial             = true;
    public bool   preventSleepOnDownload      = false;
    public bool   enableLogin                 = true;
    public string EnableDeleteUserAccountType = "Facebook, Google, Apple";
    public string AfEventsConfig              = "";
    public string AfFullEventsConfig          = "";

    //New Profile
    public bool UserProfile_UseNewProfile = false;

#if UNITY_EDITOR
    [SerializeField] private TextAsset AfEventsConfig_test;
#endif

    public bool downloadWhenWatchingRW = false;
    public bool anzuSetGDPRConsent     = true;

    public bool  isUseRhythmTool       = false;
    public bool  isUseRhythmToolForACM = false;
    public float rhythmToolDelay       = 0.05f;
    public float rhythmToolMaxDuration = 2f;
    public bool  isEnableAnimationUI3  = false;

    [Header("GamePlay:")] // 
    public string gameLevels;

    public bool gameLevelShuffleStage = false;

    public float  MapSize             = 1;
    public string CameraPosition      = "0;-4;-10";
    public float  CammeraFollowSpeed  = 0.05f;
    public float  CammeraBallDistance = 7f;
    public float  Tile_Size           = 4.3f;
    public float  Tile_MaxSize        = 5f;
    public float  Tile_MinSize        = 2f;
    public float  Tile_Sag            = 1.0f;
    public float  Tile_MovingTime     = 1.7f;

    public int     Tile_TotalFront             = 5;
    public float   Tile_DeltaSize              = 0.2f;
    public float   Tile_PerfectSize            = 0.45f;
    public bool    Tile_PerfectIcon_Enable     = true;
    public Vector3 Tile_PerfectIcon_Size       = new Vector3(0.33f, 0.29f, 1f);
    public bool    Tile_PerfectIcon_AnimEnable = true; // can thiệp sự nhấp nháy của icon perfect
    public bool    CenterAnimation_Enable      = false; // ẩn hiện vfx center perfect
    public int     Tile_PerfectTrigger         = 6;
    public int     Tile_BadTrigger             = 6;
    public float   Tile_MaxPositionX           = 6.5f;
    public int     Distance_Interval           = 30;
    public string  Distance_MinMax1            = "1:2";
    public string  Distance_MinMax2            = "1:3";
    public string  Distance_MinMax3            = "1:4";
    public string  Distance_MinMax4            = "1:5";

    #region Reborn Config

    public int  Reborn_MaxRebornVideo      = 5;
    public int  Reborn_MinScore            = 10;
    public int  Reborn_CountDownTime       = 3;
    public bool Reborn_AlwaysKeep          = false;
    public bool Reborn_AddVipButton        = true;
    public int  Reborn_MaxRebornVip        = 5;
    public int  Reborn_StraightTile        = 999;
    public bool Reborn_ShowRebornLeft      = false;
    public bool Reborn_ShowAdFirst         = true;
    public bool Reborn_ShowButtonAnimation = false;
    public bool Reborn_ShowMusicBackground = true;

    #endregion

    public bool  EndlessMode_Enable       = true;
    public float EndlessMode_SpeedUp      = 0.3f;
    public int   EndlessMode_UnlockByStar = 3; //default 3 stars

    public bool Enable_TwoResultScene = true;

    public float fakeTileAlpha   = 0.85f;
    public int   forcePlatformId = 0;

    public int   LongTile_IndexLine = 0;
    public int[] LongTile_RangeRandom; //= { 0, 1, 3, 4 };
    public int   LongTile_TotalPoint      = 10; // áp dụng cho tile dài và cầu vồng
    public float LongTile_MinDistance     = 0.3f; // khoảng cách ngắn nhất từ tile dài đến tile tiếp theo
    
    public bool   LongTile_Increase_ByCode              = false;
    public int    LongTile_Increase_MinTile             = 2;
    public int    LongTile_Increase_MaxTile             = 3;
    public float  LongTile_Increase_MaxTime             = 2f;
    public bool   LongTile_Increase_Separate            = false;
    public int    LongTile_Increase_AmountPerMoodChange = 2;
    public string LongTile_Increase_Apply               = string.Empty;

    [Header("LongNote Ver2")] public bool   LongNote_v2_IsEnable = false;
    public                           string LongNote_Config      = string.Empty;

    public bool EnablePerfectScoreFly = false;

    [Header("Ball:")] // 
    public int Default_BallID = 0;

    public float JumpHeight                   = 5.3f;
    public float FingerSensitivity            = 4.1f;
    public float FingerSpeed                  = 28.5f;
    public float FingerMaxX                   = 0;
    public float FingerSpeedScreenFix         = 0;
    public float BallSpeed                    = 15f;
    public float BallSpeedFixBpm              = 0;
    public float BallSize                     = 1.5f;
    public float BallRotateSpeed_Forward      = 1;
    public float BallRotateSpeed_LeftRight    = 1;
    public bool  BouncingBall_UseBouncingBall = false;
    public bool  BouncingBall_UseBallRotation = false;
    public float BouncingBall_MaxStretch_X    = 1.8f;
    public float BouncingBall_MaxStretch_Y    = 3f;
    public float BouncingBall_MinStretch_X    = 0.5f;
    public float BouncingBall_MinStretch_Y    = 0.5f;
    public int   BouncingBall_Version         = 1;
    public float BouncingBall_MinPercentage   = 0f;

    public int    LowEndDevice_Memory        = 2048;
    public int    LowEndDevice_Processor     = 3;
    public int    LowEndDevice_GraphicMemory = 128;
    public string LowEndDevice_Model         = "vivo vivo 1904;vivo vivo 1906;vivo vivo 2015";

    public int MiddleDevice_Memory        = 4096;
    public int MiddleDevice_Processor     = 6;
    public int MiddleDevice_GraphicMemory = 256;

    public bool BallRotateFollowFinger = false;

    public bool  CharacterRotation_EnableCharacterRotation = false;
    public float CharacterRotation_CharacterRotateSpeed    = 1f;
    public bool  CharacterRotation_CharacterRotateAtStart  = false;

    [Header("Song & Music")] //
    public float SwitchColorDuration = 1.5f;

    public string MidiFilter_Min                         = "0.32;0.17;0.1";
    public string MidiFilter_Max                         = "0.5;0.25;0.1";
    public int    MidiFilter_realtile_element_song_start = 4;
    public int    MidiFilter_faketile_element_song_start = 6;
    public int    MaxDifficultyLvl                       = 5;
    public float  Modify_MidiTime                        = 0.05f;
    public bool   Enable_ACM                             = false;

    public string Songs_MIDI_Path =
        "{\"1\":\"https://d1xncywiopk9ma.cloudfront.net/Songs/MIDI/\",\"2\":\"https://d3vvl57xnvh6za.cloudfront.net/prototype0000BK/Songs/MIDI/\",\"3\":\"https://d1xdl3mobxyv8c.cloudfront.net/Songs/MIDI/\",\"4\":\"https://beat-hopper.firebaseapp.com/Songs/MIDI/\"}";

    public float FixLatency = 0.0f;

    public string Song_Tags         = "ALL, POPULAR, FAVORITES , MY_SONGS, MORE";
    public string Song_Tags_Exclude = "";
    public bool   Enable_SongInfo   = false;

    public bool   Enable_LocalSong              = true;
    public float  Song_Impression_Time          = 2f;
    public bool   Recommend_OneTime             = true;
    public int    Recommend_RelatedGenreSongs   = 4;
    public int    Recommend_UnrelatedGenreSongs = 4;
    public int    Recommend_EcoPlayableSong     = 0;
    public string Recommend_SongResultTutorial  = "";

    [Header("Price & Diamond")] // 
    public int Diamond_StarBonus = 1;

    public int Diamond_SpawnRate       = 20;
    public int Diamond_Init            = 15;
    public int Diamond_Video_1st       = 20;
    public int Diamond_Video_Step      = 5;
    public int Diamond_Video_Max       = 40;
    public int Diamond_LOGIN_1ST       = 20;
    public int Diamond_SCORE_100       = 20; //use in GetField
    public int Diamond_SCORE_250       = 30; //use in GetField
    public int Diamond_SCORE_500       = 40; //use in GetField
    public int Diamond_SCORE_1000      = 50; //use in GetField
    public int Diamond_STARS_1         = 10; //use in GetField
    public int Diamond_STARS_10        = 20; //use in GetField
    public int Diamond_STARS_ALL       = 50; //use in GetField
    public int Diamond_UNLOCK_SONG_5   = 10; //use in GetField
    public int Diamond_UNLOCK_SONG_10  = 20; //use in GetField
    public int Diamond_UNLOCK_SONG_ALL = 50; //use in GetField
    public int Diamond_PERFECT_10      = 50; //use in GetField
    public int Diamond_UNLOCK_BALL_5   = 10; //use in GetField
    public int Diamond_UNLOCK_BALL_10  = 20; //use in GetField
    public int Diamond_UNLOCK_BALL_ALL = 50; //use in GetField
    public int Diamond_RETRY_50        = 20; //use in GetField
    public int Diamond_RETRY_100       = 50; //use in GetField
    public int Diamond_REVIVE_20       = 20; //use in GetField
    public int Diamond_REVIVE_50       = 50; //use in GetField
    public int Diamond_HIGHEST_SCORE   = 100; //use in GetField
    public int Diamond_FB_SHARE        = 20; //use in GetField
    public int Diamond_TWITTER_SHARE   = 20; //use in GetField
    public int Diamond_DailyVipReward  = 100;
    public int Price_Ball              = 50;
    public int Price_Upload_Song       = 50;

    [Header("IAP Shop")] public string DiamondShop_Config;
    public                      string FreeGift_Config;
    public                      bool   RedirectToShopWhenNotEnoughDiamond = true;
    public                      bool   ShopUI2_Enable                     = true;
    public                      bool   ShopUI3_Enable                     = true;
    public                      bool   ShopUI_Revamp_Enable               = false;

    public bool   StarterPack_IsEnable        = false;
    public string StarterPack_Config          = string.Empty;
    public int    StarterPack_Version = 1;
    
    public bool   ShopAdvancedPack_IsEnable        = false;
    public int    ShopAdvancedPack_Gems            = 1500;
    public int    ShopAdvancedPack_UnlimitedRevive = 10;
    public string ShopAdvancedPack_Booster         = String.Empty;

    public bool   ShopMasterPack_IsEnable        = false;
    public int    ShopMasterPack_Gems            = 3000;
    public int    ShopMasterPack_UnlimitedRevive = 15;
    public string ShopMasterPack_Booster         = String.Empty;

    public bool  ShopDailyDeal_IsEnable     = false;
    public int[] ShopDailyDeal_Reward       = new int[] { 20, 50, 50 };
    public int[] ShopDailyDeal_Require      = new int[] { 0, 1, 2 };
    public float ShopDailyDeal_IntervalTime = 20;

    [Header("Ads:")] //
    public int Video_LocalSong = 1;

    public bool Ad_Enable_TutorialAd      = true;
    public int  Ad_DelayBeforeFirstFS     = 30;
    public int  Ad_DelayBetweenFS         = 30;
    public int  Ad_MaxGamesBetweenFS      = 3;
    public int  Ad_DelayBetweenRVAndFS    = 15;
    public int  Ad_WaitMaxTime            = 10;
    public bool Ad_WaitMaxAllowUnlock     = false;
    public int  Ad_SongTimeShowFS         = 0;
    public int  Ad_CacheRetryTimeout      = 15;
    public bool VideoDiamondAtHome_Enable = true;

    public bool  BannerAd_Enable     = false;
    public int   BannerAd_x_session = 0;
    public int   BannerAd_x_song_results = 0;
    public bool  BannerAd_IsBottom   = true;
    public float BannerAd_TopSize    = 65;
    public float BannerAd_BottomSize = 65;
    public bool  BannerAd_Tutorial   = true; //Note: if this flag is true, means don't show banner at the tutorial time 
    public bool  BannerAd_GamePlay          = true;
    public bool  BannerAd_Home              = true; 
    public bool  BannerAd_guideline = true; // old BannerAd_Setting_HowToPlay
    public bool  BannerAd_discover = true;
    public bool  BannerAd_loading        = true; //old BannerAd_Transition
    public bool  BannerAd_hardcorechallenge = true;
    public bool  BannerAd_7daymission = true;
    public bool  BannerAd_setting = true;
    public bool  BannerAd_leaderboard = true;
    public bool  BannerAd_achievement = true;
    public bool  BannerAd_shop      = true;
    public bool  BannerAd_sub_offer = true;     //old BannerAd_SubscriptionPopup 
    public bool  BannerAd_sub_catalog = true;
    public bool  BannerAd_ExitGame = true; 
    public bool  BannerAd_AfterAd  = true;
    public bool  BannerAd_Continue = true;
    public bool  BannerAd_result = true;
    public bool  BannerAd_shopball = true;

    public bool  AdStretch_Enable             = false;
    public bool  AdStretch_Reverse            = false;
    public int   AdStretch_MinInterval        = 20;
    public int   AdStretch_MaxInterval        = 200;
    public int   AdStretch_MaxGame            = 20;
    public int   AdStretch_MinGame            = 3;
    public float AdStretch_LevelRate          = 1;
    public float AdStretch_PlayDayRate        = 1;
    public float AdStretch_RwCountRate        = 1;
    public float AdStretch_SongStartCountRate = 1;

    public bool   AdBreak_Overlay               = false;
    public bool   AdBreak_InterstitialReward    = true;
    public int    AdBreak_RewardDiamondAmount   = 30;
    public int    AdBreak_RewardSongCount       = 0;
    public string AdBreak_Rewards               = "CHARACTER, BALL, SONG, SONG, SONG";
    public bool   AdBreak_Rewards_Pool_IsEnable = false;
    public bool   AdBreak_RewardBall_Random     = false;
    public int[]  AdBreak_Rewards_Ball_Pool     = { 0, 1 };

    public string AdOpenApp_Config = string.Empty;

    [Header("IAP:")] public bool Enable_IAP = true;

    [Header("Subscription:")] //
    public bool Subscription_Enable = true;

    public bool   Subscription_CTA_ShowPrice          = false;
    public bool   Subscription_SessionPopup           = false;
    public int    Subscription_FirstSession_SongStart = 2;
    public string Subscription_WeekPackageID;
    public string Subscription_Price           = "5.99$";
    public int    Subscription_TrialPeriod     = 3;
    public bool   SubscriptionCatalogue_Enable = false;
    public string SubscriptionCatalogue_Type   = "artist";
    public string Subscription_TrialColor;
    public string Subscription_PriceColor;
    public bool   Subscription_DiscountOffer = false;

    public bool   Subscription_NoAds_IsEnable  = false;
    public string Subscription_NoAds_PackageID = "no_ads_package";
    public string Subscription_NoAds_Price     = "1.11$";

    public float CustommizeSub_ShowCloseBtnDelay = 0;

    //public bool CustommizeSub_AdMainOffer = true;
    public bool CustommizeSub_FsPromoSubVideo      = true;
    public int  CustommizeSub_FsPromoSubVideoSkip  = 5;
    public bool CustommizeSub_RwPromoSubVideo      = true;
    public int  CustommizeSub_FsPromoSubVideoLimit = 5;
    public int  CustommizeSub_RvPromoSubVideoLimit = 5;
    public int  CustommizeSub_RvMinRequired        = 0;
    public int  CustommizeSub_FsMinRequired        = 0;

    public bool   MultiSubPackages_Enable = false;
    public bool   MultiSubPackages_2Tier  = false;
    public string SubPackages;

    public string SubPackages_Week_Old =
        "com.amanotes.beathopper.vipsubweek.test;com.amanotes.beathopper.vipsubweek.09;com.amanotes.beathopper.vipsubweek.16;com.amanotes.beathopper.vipsubweek.01;com.amanotes.pamadr_sub_1w_6.99usd_freetrial_3d0.202;com.amanotes.pamabh_sub_1w_5.99usd";

    public string SubPackages_Month_Old =
        "com.amanotes.beathopper.vipsubmonth.01;com.amanotes.beathopper.vipsubmonth.20";

    public string SubPackages_Year_Old = "com.amanotes.beathopper.vipsubyear.01;com.amanotes.beathopper.vipsubyear.40";

    [Header("Theme:")] // 
    public bool Theme_Random = true;

    public int    Theme_TutorialID = 1;
    public int    Theme_ForceID = -1;
    public string Theme_ConfigUrl = "https://d1xncywiopk9ma.cloudfront.net/Songs/ThemeConfig/themeSelectionConfig.csv";
    public int    Theme_Type = 1; //phase 1 || 2
    public bool   Theme_DisableSelectionFixTheme = false;

    public bool Theme_IsEnableSelectTheme            = false;
    public bool Theme_IsEnableSelectThemeAfterReward = true;

// #if UNITY_EDITOR
//     public TextAsset Theme_TestConfigs = null;
// #endif
    public float Theme_MoodChangePreviewInterval = 1f;
    public bool  Theme_CanPreviewThemeVariants   = false;
    public int   Theme_UIVariant                 = 1;

    public string Theme_Tags            = "{\"Xmas\":\"8, 15, 28\"}";
    public string Character_Tags        = "{\"29\":\"23\"}";
    public int    Theme_Default_SkinSet = -1;

    [Header("Perfect")] public int Perfect_VisualEffectMax = 10;

    public float resultBgOpacity = 1f;
    //public bool isNewDefaultBall = true;

    //public bool enableSearchBar = true;
    public bool AllowPartnerBuild           = false;
    public bool isGetLeaderBoardFromStorage = true;

    [Header("UI Revamp")] //
    public int totalCacheSearchSong = 5;

    [Header("Anzu Adv")] //~~~~~~~~~~~~~~~~ 
    public bool isEnableAnzu = true;

    public bool isShowAnzuConfigOnHome = false;

    public bool debug_isShowAnzuStats    = false; //only for dev
    public bool showAnzuBannerOnPlayback = true;

    public int    totalAdvShow              = 4;
    public float  distanceAdv               = 120;
    public float  posStartX                 = 17;
    public float  scaleAnzuBanner_4_3       = 0.26f;
    public float  scalePower                = 0.4f;
    public float  scaleEffectDuration       = 0.3f;
    public string typeAnzuBanner            = "left;right";
    public float  rotationX                 = 0f;
    public float  rotationY                 = 35f;
    public float  rotationZ                 = 0f;
    public bool   isShrinkToFitAspectRatio  = true;
    public bool   isShowBannerAlternate     = false;
    public bool   isDirectGpuTextureUpdates = false;
    public string excludeSongNameOnAnzu;
    public string AnzuOffThemeIds = "";

    //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

    [Header("Adverty")] //~~~~~~~~~~~~~~~~ 
    public bool Adverty_IsEnable;

    public bool   Adverty_ShowBannerOnPlayback      = true;
    public int    Adverty_TotalAdvShow              = 4;
    public float  Adverty_DistanceAdv               = 120;
    public float  Adverty_PosStartX                 = 17;
    public float  Adverty_ScaleBanner_4_3           = 0.26f;
    public float  Adverty_ScalePower                = 0.4f;
    public float  Adverty_ScaleEffectDuration       = 0.3f;
    public string Adverty_TypeBanner                = "left;right";
    public float  Adverty_RotationX                 = 0f;
    public float  Adverty_RotationY                 = 35f;
    public float  Adverty_RotationZ                 = 0f;
    public bool   Adverty_IsShrinkToFitAspectRatio  = true;
    public bool   Adverty_IsShowBannerAlternate     = false;
    public bool   Adverty_IsDirectGpuTextureUpdates = false;
    public string Adverty_ExcludeSongName;
    public string Adverty_OffThemeIds = "";
    public string Adverty_IgnoreModel = "SM-S721U";//samsung r12s (Galaxy S24 FE)

    //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    public int NativeAds_Style = -1; //0: Anzu, 1: Anzu -> Adverty, 2: Adverty

    //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

    public bool   isEnableUrlArtistCover = true; //TH-163
    public string urlArtistCover; // = "https://inwave.vn/data-test/TH-163/";

    #region Musicalization ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

    [Header("Musicalization")] public bool Musicalization_IsEnable = true;

    public bool  Musicalization_DownloadMidiOnly             = true;
    public bool  Musicalization_Timbre_IsEnable              = true;
    public bool  Musicalization_NoteLength_IsEnable          = true;
    public bool  Musicalization_Pitch_IsEnable               = true;
    public bool  Musicalization_Pitch_Motif_or_not           = false;
    public int   Musicalization_Pitch_FromLevel              = 0;
    public bool  Musicalization_Intensity_IsEnable           = true;
    public bool  Musicalization_MoodChange_IsEnable          = true;
    public float Musicalization_MoodChange_TransitionTime    = 0.5f;
    public float Musicalization_MoodChange_TimeModify        = 0.5f;
    public int   Musicalization_MoodChange_ColorTileId       = 0; //TH-657
    public bool  Musicalization_MoodChange_FullTileSameColor = true; //TH-819 

    public float Musicalization_ShortNoteDistance = 0.2f; //TH-302
    public float Musicalization_LongNoteDuration  = 1; //TH-301
    public bool  Musicalization_Vibration         = true; //TH-301
    public float Musicalization_NoPitchTime       = 0; //TH-386
    public bool  Musicalization_EnableShortNote   = true; //TH-386

    #endregion

    //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    [Header("Vibration")] //~~~~~~~~~~~~~~~~
    public float Vibration_Intensity = 0.5f;

    public bool Vibration_DefaultOn = false;

    public float Vibration_Sharpness = 0;
    //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

    [Header("Shop balls")] //~~~~~~~~~~~~~~~~
    public string HumanAmin_RatioJumpHeightByBall =
        "{\"23\":\"0.6|0.5|0.4\",\"24\":\"0.6|0.5|0.4\",\"25\":\"0.85|0.75|0.65\"}"; //23 24 25 = ballID => class BallList

    public float HumanAmin_NormalJumpTime = 0.5f;
    public float HumanAmin_LongJumpTime   = 1f;

    //TH-380
    //public int[] vipBalls = { 23, 24 }; //TH-417
    //public int[] videoBalls = { 5, 10, 15, 21 }; //TH-444

    public bool ShopBallInGame_IsEnable = true;
    public bool ShopBallInHome_IsEnable = true;

    [Header("Weekly Mission")] //~~~~~~~~~~~~~~~~
    public bool WeeklyMission_IsEnable = false;

    public bool WeeklyMission_IsCountMaxStarOfSong = false;

    //public int[] WeeklyMission_VipBalls = { 23, 2, 24, 25, 7 }; 
    public bool WeeklyMission_RandomBall = true;

    //TH-453 Next Btn
    public bool NextBtn_IsEnable           = true;
    public bool NextBtn_VideoSongFirst     = false;
    public bool NextBtn_RecommendSongFirst = false;

    [Space] public bool   OnboardingFlow_IsEnable;
    public         bool   OnboardingFlow_IsShowSubOffer;
    public         float  OnboardingFlow_SafetyNetTime = 15;
    public         bool   OnboardingFlow_IsShowPlayNext;
    public         bool   OnboardingFlow_EnableProgressBar;
    public         string OnboardingFlow_Version          = "v2";
    public         string OnboardingFlow_SelectSongType   = "typeA"; //typeA typeB
    public         int    OnboardingFlow_AdAfterTutorial  = 0; // 0 1 2
    public         bool   OnboardingFlow_EnableAnzu       = true; // 0 1 2
    public         bool   OnboardingFlow_NextFreeSongList = true;

    public int dailyMaxReward = 3; //TH-503

    //LevelBot
    public bool  LevelBot_IsEnable        = true;
    public bool  LevelBot_ApplyFilter     = true;
    public int   LevelBot_PitchFromSecond = 10;
    public float LevelBot_HardPercent     = 0.7f;

    public string ACMv4_SongType             = "VIDEO";
    public int    ACMv4_SongPrice            = 0;
    public bool   ACMv4_IsLoadMore           = true;
    public bool   ACMv4_EnableCheckLicense   = true;
    public bool   ACMv4_FireLicenseException = false;

    #region song of day ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

    public bool   SongOfDay_IsShow        = false; //TH-520
    public int    SongOfDay_LimitDay      = 3; //TH-520
    public string SongOfDay_SongTag       = "ALL";
    public int    SongOfDay_IndexSongFrom = 3;
    public int    SongOfDay_IndexSongTo   = 50;

    #endregion

    #region 7 day mission ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

    [Header("Seven Day Mission")] //
    public bool SevenDayMission_IsEnable = false;

    public int SevenDayMission_DayStart = 0;
    public int SevenDayMission_DayEnd   = 7;

    public string SevenDayMission_MissionDays =
        "day,missionType,total,missionReward,totalReward,points||1,start_x_songs,3,Diamond,15,||1,play_x_songs_and_get_at_least_1_star,2,Diamond,20,||1,finish_x_songs,1,Diamond,20,||1,,,ball,,||2,get_x_stars,4,Diamond,15,||2,start_x_songs,3,Diamond,20,||2,play_x_songs_and_get_at_least_2_stars,2,Diamond,20,||2,,,song,,||3,finish_x_songs,2,Diamond,10,||3,replay_x_times,2,Diamond,20,||3,get_at_least_x_point,1,Diamond,10,250||3,,,song,,||4,replay_x_times,3,Diamond,10,||4,get_at_least_x_point,1,Diamond,20,500||4,get_x_stars,6,diamond,20,||4,,,ball,,||5,replay_x_times,3,Diamond,20,||5,play_x_songs_in_endless_mode,1,Diamond,25,||5,get_x_stars,8,diamond,20,||5,,,song,,||6,continue_x_times,2,Diamond,20,||6,play_x_songs_and_get_at_least_2_stars,4,Diamond,10,||6,play_x_songs_in_endless_mode,2,Diamond,20,||6,,,song,,||7,finish_x_songs,3,Diamond,20,||7,get_x_stars,10,Diamond,20,||7,get_at_least_x_point,1,Diamond,20,1000||7,,,song,,";

    public bool SevenDayMission_UpdateMissionAllDays = true;

    public string SevenDayMission_RewardSongs =
        "ff1e4bb4-b47a-4dc6-b775-0d9b21f64cee;9265c0ea-a53d-4dfd-a353-10366292c071;55e72efc-0643-4f8c-88e8-30f46d428d59;646fe5fd-e17d-413c-a3cf-32c55f9ddd3a;e6b0dbf0-31d4-4e80-8d7f-83bae78b1678;6f1dcf3e-b56b-4795-9d84-077c9a315841;bc25a4b3-7b8a-472c-bd01-329808df299f;22eec3db-c56a-43b3-98bb-41a95dec0b3c"; //list acmID

    public int SevenDayMission_TimePushNotiDay1 = 5; //hours 

    public int
        SevenDayMission_DefaultRewardDiamond = 100; //TH-1263: 6. Dùng thay thế khi không còn ball/song để nhận reward

    public bool  SevenDayMission_RewardBall_Pool_IsEnable = false;
    public bool  SevenDayMission_RewardBall_Random        = false;
    public int[] SevenDayMission_RewardBall_Pool;

    #endregion

    #region SongDiscovery ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

    [Space] public string SongDiscovery_UrlArtistsData = "https://beat-hopper.web.app/artists.csv";
    public string SongDiscovery_UrlAlbumData = "https://d1xncywiopk9ma.cloudfront.net/Songs/SongConfig/AlbumList.csv";

    public string
        SongDiscovery_DataOrder =
            "Populars;Artists;Albums;Genres;FavouriteSong;RecentSong"; // string from DiscoveryType

    public string SongDiscovery_ListGenres =
        "Acoustic;Alternative/Indie;Ballad;Blues;Classical/Opera/Orchestra;Country;Electronic;Hip-Hop/Rap;Jazz;Latin;Pop;R&B;Reggae/Ska;Religious;Rock;Soundtracks;Traditional/Folk;World;Chinese Song;Japanese Song;Korean Song;Children;Holiday;Dance;EDM;House;Soul;Disco;Chill-out;Club/Club Dance;Funk;Beat Music";

    public string SongDiscovery_DataBanners;

    #endregion

    //~~~
    [Space] public bool isShowCharactersInHome;
    public         bool SilentMode_IsEnable = true;

    public string urlBallConfig = "https://d1xncywiopk9ma.cloudfront.net/Songs/BallConfig/ballconfig-rarity.csv";

    public bool RarityBall_IsEnable      = true; //TH-926
    public bool UseVFXCommonOnChangeSkin = false; //TH-1346

    #region Notes Difficult ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

    public bool  NotesDifficult_IsEnable                  = false;
    public float NotesDifficult_Easy_LevelBot_HardPercent = 0.6f;
    public float NotesDifficult_Easy_TimeLongNote         = 1;
    public float NotesDifficult_Easy_PerfectSize          = 0.7f;
    public bool  NotesDifficult_Easy_UseBpm               = false;
    public int   NotesDifficult_Easy_GameLevel            = 0;
    public bool  NotesDifficult_Easy_UseBpmLocalSong      = false;

    public float NotesDifficult_Normal_PerfectSize = 0.55f;

    public float  NotesDifficult_Hard_MaxPositionX         = 6.5f;
    public float  NotesDifficult_Hard_PerfectSize          = 0.45f;
    public float  NotesDifficult_Hard_Tile_Size            = 3.44f;
    public float  NotesDifficult_Hard_Tile_MaxSize         = 4.0f;
    public float  NotesDifficult_Hard_Tile_MinSize         = 1.6f;
    public float  NotesDifficult_Hard_LevelBot_HardPercent = 0.85f;
    public string NotesDifficult_Hard_MidiFilter_Min       = "0.27;0.12;0.07";
    public string NotesDifficult_Hard_MidiFilter_Max       = "0.4;0.2;0.07";
    public float  NotesDifficult_Hard_MultiplySpeed        = 1.2f;
    public int    NotesDifficult_Hard_Tile_TotalFront      = 3;
    public float  NotesDifficult_Hard_Tile_MovingTime      = 0.8f;
    public int    NotesDifficult_Hard_Tile_PerfectTrigger  = 4;
    public int    NotesDifficult_Hard_Tile_BadTrigger      = 4;

    public int NotesDifficult_Endless_CountChangeMidi = 0;

    public bool NotesDifficult_Diamond_InEnable           = true;
    public int  NotesDifficult_Diamond_Progression_Easy   = 20;
    public int  NotesDifficult_Diamond_Progression_Normal = 40;
    public int  NotesDifficult_Diamond_Progression_Hard   = 60;
    public int  NotesDifficult_Diamond_Perfect_Easy       = 30;
    public int  NotesDifficult_Diamond_Perfect_Normal     = 50;
    public int  NotesDifficult_Diamond_Perfect_Hard       = 70;

    public int NotesDifficult_MoodChange_ColorTileId_Normal = 6;
    public int NotesDifficult_MoodChange_ColorTileId_Hard   = 7;

    #endregion

    [Header("Improve Sensitive")] //
    public bool ImproveSensitive_IsEnable = true;

    public bool  ImproveSensitive_UseNewNotes    = true;
    public float ImproveSensitive_TimeClickClose = 5;
    public float ImproveSensitive_TimeShowClose  = 3;
    public bool  ImproveSensitive_ShowInSetting  = true;

    #region Ball Trail Perfect

    [Header("Ball Trail Perfect")] //
    public bool BallTrailPerfect_Enable = false;

    public int   BallTrailPerfect_PerfectStep  = 5;
    public float BallTrailPerfect_LifeTimeStep = 0.15f; // for ball
    public float BallTrailPerfect_MaxLifeTime  = 1f;
    public float BallTrailPerfect_TimeStep     = 0.1f; // for character
    public float BallTrailPerfect_MaxTime      = 0.4f;

    #endregion

    #region Subscription follow location

    [Header("Subscription Follow Location")]
    public bool SubscriptionFollowLocation_Enable = false;

    #endregion

    #region Subscription OnBoarding

    [Header("Subscription OnBoarding")] public bool SubscriptionOnboarding_Enable = false;

    #endregion

    #region 2PhutHon

    [Header("2PhutHon")] public bool   OverTwoMinutes_Enable    = false;
    public                      float  OverTwoMinutes_StartTime = 46.875f;
    public                      float  OverTwoMinutes_EndTime   = 76.875f;
    public                      string OverTwoMinutes_SongName  = "2 Phút Hơn";

    #endregion

    public bool NativeReview_IsEnable = true; //TH-967

    [Header("AudioMixer")] public bool AudioMixer_IsEnable = true;

    [Header("Tutorial Config")] // ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    public bool TutorialSongList_IsEnableRemote = true;

    public string TutorialSongList_Config;

    public bool TutorialSong_IsMustComplete = true; //Apply for new + old version
    public int  TutorialSong_Rule           = 1; //TH-1455 (0 default, 1 rule 1, 2 rule2)

    public bool TutorialTheme_ImproveExperience = false;

    #region TrySkin ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

    [Header("TrySkin")] // ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    public bool TrySkin_Enable = false;

    public string TrySkin_TypeBall      = "VIDEO";
    public int    TrySkin_Style         = 1; //1: chạy tiếp sức 2: đổi character
    public int    TrySkin_PercentAppear = 50;

    public int TrySkin_LimitInDay = 5;

    public int   TrySkin_FirstSession_FirstShow = 2;
    public int   TrySkin_FirstSession_NextShow  = 2;
    public int   TrySkin_NextSession_FirstShow  = 0;
    public int   TrySkin_NextSession_NextShow   = 2;
    public bool  TrySkin_UniqueShow             = false;
    public bool  TrySkin_IsRandomSkin           = false;
    public int[] TrySkin_SkinIdList;
    public int   TrySkin_MaxTimeOffer = 3; //Sau 1 số lần liên tiếp mà user ko unlock thì tắt tính năng này

    public bool TrySkin_ShowBeforeFSAds          = false;
    public bool TrySkin_ShowFSAdsAfterUnlockBall = true;

    #endregion

    #region Demographic Survey

    [Header("Demographic Survey")] //TH-987 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    public bool DemographicSurvey_IsEnable = true;

    public bool DemographicSurvey_IsEnableNewUser = true;

    public bool DemographicSurvey_IsEnableOldUser = true;

    //public string DemographicSurvey_UrlConfig="https://test.inwave.vn/upload-files/2023-01-12/DataSurvey_Demographic_v1.json";
    public string DemographicSurvey_DataConfig;

    #endregion

    #region Onboarding Feature

    [Header("Onboarding Feature")] //TH-987 
    public bool Onboarding_Tutorial_PreviewSong_Enable = false;

    public int  Onboarding_Tutorial_PreviewSong_LevelAppear   = 1;
    public bool Onboarding_Tutorial_PreviewSong_RequiredClick = false;

    public bool Onboarding_Result_Home_RedDot_Enable      = false;
    public int  Onboarding_Result_Home_RedDot_LevelAppear = 1;
    public int  Onboarding_Result_Home_RedDot_TimeAppear  = 1;

    public bool Onboarding_Home_ExploreNotice_Enable        = false;
    public int  Onboarding_Home_ExploreNotice_LevelAppear   = 0;
    public bool Onboarding_Home_ExploreNotice_RequiredClick = false;

    public bool Onboarding_Ingame_ShopballNotice_Enable        = false;
    public int  Onboarding_Ingame_ShopballNotice_LevelAppear   = 1;
    public bool Onboarding_Ingame_ShopballNotice_RequiredClick = false;

    public bool Onboarding_Ingame_Progressbar_Enable        = false;
    public int  Onboarding_Ingame_Progressbar_LevelAppear   = 0;
    public bool Onboarding_Ingame_Progressbar_RequiredClick = false;

    public bool Onboarding_Result_IntroGem_Enable                = false;
    public int  Onboarding_Result_IntroGem_LevelAppear           = 0;
    public bool Onboarding_Result_IntroGem_RequiredClick         = false;
    public bool Onboarding_Result_UnlockSongBuyGem_RequiredClick = false;

    #endregion

    #region NEW UI RESULT SCREEN

    [Header("NEW UI RESULT SCREEN")] // TH-1072
    public float ResultScreen_AdsMultiply = 2;

    public int  ResultScreen_AdsMinDiamondInStorage = 150; // minimum diamond in storage to show up multiply ads button
    public int  ResultScreen_AdsMinDiamond          = 10; // minimum collected diamond to show up multiply ads button
    public int  ResultScreen_AdsMaxDiamond          = 150; // maximum collected diamond to show up multiply ads button
    public int  ResultScreen_AdsCapping             = 3; // capping / day for multiply ads
    public int  ResultScreen_StyleIndex;
    public bool ResultScreen_ShowBeforeInterAd; //true => show 1/2 -> inter ads -> show 1/2
    public bool ResultScreen_IsShowCrownEndless         = true; //for result UI2 UI3 UI4
    public int  ResultScreen_NewFlowVariant             = -1;
    public bool ResultScreen_UseEconomyResult           = false;
    public bool ResultScreen_StarFlyWithScreenVibration = false;

    #endregion

    #region SongCard Style

    //TH-1039
    [Header("SongItem Style")] [Tooltip("Value = 1;2;3")]
    public int SongItem_StyleIndex = 1; //1 như cũ; 2; 3;

    public string SongItem_DefaultAction; //Leaderboard || Play || Preview 

    public bool SongItem_ShowCompletedStatus = false;

    #endregion

    #region ReviveTutorial

    [Header("ReviveTutorial")] //TH-1047 
    public bool ReviveTutorial_Enable = false;

    public int ReviveTutorial_NumberTile = 0;

    #endregion

    #region Economy System

    [Header("Economy System")] public bool Economy_IsEnable;
    public string Economy_Version;
    public bool Economy_MileStone_Enable = true; // TH-1132: user story gem milestone
    public bool Economy_MileStone_Endless_Detailed = true; // TH-1132: user story gem milestone
    public int Economy_MileStone_1st_Percent = 10; //TH-1132: user story gem milestone
    public int Economy_MileStone_1st_Value = 10; //TH-1132: user story gem milestone
    public int Economy_MileStone_2nd_Percent = 50; //TH-1132: user story gem milestone
    public int Economy_MileStone_2nd_Value = 50; //TH-1132: user story gem milestone
    public int Economy_MileStone_3rd_Percent = 100; //TH-1132: user story gem milestone
    public int Economy_MileStone_3rd_Value = 100; //TH-1132: user story gem milestone
    public int Economy_MileStone_Endless_Value = 150; //TH-1132: user story gem milestone
    public bool Economy_MileStone_RepeatedEat_Normal = false; // TH-1390: cho phép ăn lại các milestone normal 1,2,3
    public bool Economy_MileStone_RepeatedEat_Endless = false; // TH-1390: cho phép ăn lại các milestone endless (>3)
    public bool Economy_Diamond_Ingame_StartDiamond = false;
    public int Economy_Diamond_Ingame_XStartTiles = 0;
    public bool Economy_Diamond_Ingame_XStartTilesRepeat = true;
    public int Economy_Diamond_InGame_YStartAmount = 1;
    public int Economy_SongPrice = 0;
    public bool Economy_Topbar_IsFixed = true;
    public bool Economy_TutorialReward_Enable = false;
    public int Economy_TutorialReward_Value = 100;
    public bool Economy_VIP_BenefitAds_IsEnable = true; //true là cho phép kiếm thêm gem after VIP

    public int Economy_Variant_Config = 0;

    //TH-1471: number of diamonds a user will receive for watching an ad.
    public int Economy_dia_watched_ads_value = 200;

    //TH-1471: Minimum number of diamonds required to show the popup
    public int Economy_dia_watched_ads_required_diamond = 100;

    //TH-1471: Minimum number of song start required to show the popup
    public int Economy_dia_watched_ads_required_songstart = 1;

    //TH-1471: location required to show the popup
    public string Economy_dia_watched_ads_required_location = "Home;Result";

    //TH-1471: Số lần hiện tối đa popup trong ngày
    public int Economy_dia_watched_ads_session_per_day = 10;

    //TH-1471: Thời gian tối thiểu giữa 2 lần hiển thị popup (tính bằng phút) (lần trước user k xem reward)
    public int Economy_dia_watched_ads_time_interval_popup_minute = 10;

    //TH-1471: Thời gian tối thiểu giữa 2 lần nhận rewarded (tính bằng phút) (lần trước user đã nhận reward)
    public int Economy_dia_watched_ads_time_interval_claim_turn = 1440;

    public string Economy_dia_sale_IAP_packageSale =
        "{\"idPack\":\"eco_diamond\",\"value\":325,\"androidId\":\"diamond1\",\"iosId\":\"SmallDiamondPack\"}"; //TH-1471: packageID for sale IAP

    //TH-1471: number of diamonds a user will receive for sale IAP
    public int Economy_dia_sale_IAP_value = 325;

    //TH-1471: the saleoff percent. Exp: 4.99 saleoff 40% to 2.99
    public float Economy_dia_sale_IAP_saleoff_percent = 40;

    //TH-1471: Minimum number of diamonds required to show the popup
    public int Economy_dia_sale_IAP_required_diamond = 100;

    //TH-1471: Minimum number of song start required to show the popup
    public int Economy_dia_sale_IAP_required_songstart = 1;

    public string Economy_dia_sale_IAP_required_location = "Home;Result"; //TH-1471: location required to show the popup
    public int    Economy_dia_sale_IAP_session_per_day   = 2; //TH-1471: Số lần hiện tối đa popup trong ngày
    public int    Economy_dia_sale_IAP_limit_lifetime    = 5; //TH-1471: limitted show time in lifetime

    public bool Economy_buttonShopInHome_Enable = true;
    public bool Economy_shop_freegem_Enable     = true; // Bật/tắt config freegem
    public int  Economy_shop_freegem_1stValue   = 20; // lần đầu được nhận 1/5 song gems.
    public int  Economy_shop_freegem_nextValue  = 50; // Những lầ sau được nhận 1/2 song gems.

    // Thời gian hồi của freegem tính theo phút. (Nếu muốn nhận sớm thì phải xem RW)
    public int Economy_shop_freegem_intervalTime = 180;

    public string Economy_IAP_data             = string.Empty;
    public string Economy_SpecialOffer         = string.Empty;
    public int    Economy_SpecialOffer_Version = 1;
    public string Economy_AppliedCountries     = string.Empty;

    public TextAsset Economy_LocalEconomySettings => Configuration.instance.Economy_LocalEconomySettings;

    #endregion

    #region Optimized Scrollview Adapter

    public float Scroller_DeclearationRate   = 0.9f;
    public bool  ScrollerNavigation_IsEnable = true;

    #endregion

    #region Open/Ending VFX

    public bool VFX_Opening_IsEnable     = false;
    public bool VFX_Dead_IsEnable        = false;
    public bool VFX_Dead_KeepTileVisible = false;

    #endregion

    #region EDM

    public string EDM_Songspath = "";

    #endregion

    #region Hiphop

    public string Hiphop_Songspath = "";

    #endregion

    [Header("ACM song card")] // ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    public bool ACM_IsEnableSongCover = false;

    public bool ACM_IsEnableSquareArtistCover = false;
    public bool ACM_IsEnableSquareSongCard    = false;

    public bool   IsMIDI_FullControl   = true; // transfer from TH-1490 -> TH-2483
    public bool   SongStructure_Enable = false;
    public string Midi_FullControl_Force;

    //TH-1341: Giữ lại vị trí các scroll view ở home khi play song
    public bool FixedScrollviewPositionInHome_IsEnable = false;
    //public bool FixedScrollviewPositionInHome_Center   = false;

    //SongList customization by Female & 25-34 Age
    // public int SongListCustomizationType = 0;
    public string SlotId_Midi;
    public string SlotId_Mp3;
    public string SlotId_Preview;

    #region Communication System

    [Header("Communication System")] // 
    public bool CommunicationSystem_IsOn = false;

    public bool CommunicationSystem_EnableSquidAnim = false;

    public bool   CommunicationSystem_IsTesting = false;
    public string INGAME_POPUP;
    public string INGAME_POPUP_Test;
    public string LOCAL_NOTI;
    public string LOCAL_NOTI_Test;

    #endregion

    public bool VFX_TagAlbum_Enable = true;
    public bool Secure_Data_Enable  = false;

    #region RatePopup

    public int RateAtPlayCount = 5;
    public int RateAtScore     = 200;

    public int    RatePopup_StyleIndex = 0;
    public string RatePopup_Config     = String.Empty;

    #endregion

    #region FPS Tracker

    public bool FPSTracker_Enable = false;

    #endregion

    #region LiveEvent

    [Header("Live Event")] // 
    public bool LiveEvent_Enable = false;

    public string LiveEvent_Data                        = "";
    public bool   LiveEvent_OnlyShowSongWhenActiveEvent = true;
    public bool   LiveEvent_ReplaceLockedSongAndBall    = false;
    public bool   LiveEvent_UserDayByDayDiff            = false;

#if UNITY_EDITOR
    [SerializeField] private TextAsset LiveEvent_Data_test;
#endif

    public bool GameCompleteUI_ButtonHome_Enable = true; // bật tắt button home ở game complete

    #endregion

    public string SongListTargetSegment; //

    #region Limited Download Song Time ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

    public bool  DownloadSong_TimeLimit_Enable     = false;
    public float DownloadSong_TimeLimit_Value      = 2f;
    public bool  DownloadSong_Replace_TutorialSong = false;

    #endregion

    public int    onboarding_demographic_improve; //0 old; 1 new
    public int    onboarding_tutorial_improve; //0 old; 1 new
    public string onboarding_tutorial_gameplay;

    #region Knob Playtime Reward

    public string knob_enable_condition;
    public string knob_data;

    #endregion

    #region MysteryBox

    public bool   mysterybox_enable                   = false;
    public bool   mysterybox_daily                    = false;
    public bool   mysterybox_replace_freegift         = false;
    public string mysterybox_config                   = string.Empty;
    public bool   mysterybox_RewardBall_Pool_IsEnable = false;
    public bool   mysterybox_RewardBall_Random        = false;
    public int[]  mysterybox_RewardBall_Pool;

    #endregion

    #region New SFX

    public bool PlayNewSFX_Enable    = false;
    public bool PlayNewSFX_UP_Enable = false;

    #endregion

    public bool SubscriptionUI_v4_IsEnable = false;

    public bool DifficultyTag_IsEnable      = false;
    public bool DifficultyTag_HideNone      = false;
    public bool DifficultyTag_HideEasy      = false;
    public bool DifficultyTag_HideMedium    = false;
    public bool DifficultyTag_HideHard      = false;
    public bool DifficultyTag_HideExtreme   = false;
    public bool DifficultyTag_HideInsane    = false;

    public int ScoreConfig_Great     = 1;
    public int ScoreConfig_Perfect   = 1;
    public int ScoreConfig_MaxStreak = 20;

    public bool  ProgressByScore_IsEnable;
    public int[] ProgressByScore_Star = { 20, 60, 80 }; // 1 star, 2 star, 3 star

    public bool   FakeTile_Follow_Pitch;
    public string FakeTile_Follow_Pitch_Include;
    public string FakeTile_Follow_Pitch_Exclude;

    public int ProgressBar_StyleIndex;

    #region DownloadSong v2

    public bool  DownloadSong_v2_IsEnable = false;
    public int[] DownloadSong_v2_Tips     = { };
    public float DownloadSong_v2_MaxTime  = 20;

    #endregion

    #region UserPermission Config
    public bool   UserPermissions_CMP_IsEnable        = true;
    public string UserPermissions_ATTOrder           = "1a";
    public string UserPermissions_CMPOrder           = "2a";
    public string UserPermissions_NotificationsOrder = "1b";

    #endregion

    public string paywall_weekly_plan_optimization;

    #region Notification Box

    public bool   NotificationBox_IsEnable = false;
    public string NotificationBox_UrlData;
    public bool   NotificationBox_RewardBall_Pool_IsEnable = false;
    public bool   NotificationBox_RewardBall_Random        = false;
    public int[]  NotificationBox_RewardBall_Pool          = new int[] { };

    #endregion
    public bool Category_NewDiscovery;
    public bool Category_UseFixedTextSize = true;

    public float ReviveScreen_ProgressBarDuration           = 1f;
    public float ReviveScreen_ContentFadeDuration           = 1f;
    public bool  ReviveScreen_UsingGems                     = false;
    public int   ReviveScreen_GemsPrice                     = 100;
    public int[] ReviveScreen_ProgressionReward             = new int[] { 1, 2, 3 };
    public bool  ReviveScreen_ShowSubscription_RemainRevive = false;
    public int   ReviveScreen_StyleIndex = 1;
    public int   ReviveScreen_GemsPrice_Booster = 200;

	#region New Elements

	public bool NewElements_Enable                      = false;
    public bool NewElements_Enable_HC_CS = false;
    public bool NewElements_Enable_HC_HS = false;
    
    public bool NewElements_Onboarding_Enable           = false;
    public int  NewElements_AfterSongStart              = 0;
    public bool NewElements_OverLapOtherRule            = false;
    public bool NewElements_OverLapMidiManualElement    = false;
    public bool NewElements_MidiAutoGenElement_Enable   = false;
    public bool NewElements_NormalAutoGenElement_Enable = true;
    public int  NewElements_AutoGenElement_Max          = 2;

    public int[] NewElements_Tile_index       = Array.Empty<int>();
    public int[] NewElements_MoodChange_Index = Array.Empty<int>();
    public int[] NewElements_Section_Index    = Array.Empty<int>();

    public float[] NewElements_FadeOut_FadeValue       = new[] { 1f, 0.1f, 0.25f, 0.6f, 0.8f, 1f };
    public float   NewElements_Conveyor_Speed          = 5f;
    public int     NewElements_Teleport_Count          = 5;
    public bool    NewElements_Teleport_UsePlinth      = false;
    public float   NewElements_MovingCircle_Speed      = 15f; //unit/seconds
    public bool    NewElements_MovingCircle_UseMidiPos = false;
    public int     NewElements_TransformTrap_Index     = 1; // should be range [1, 5]
    public float   NewElements_TransformTrap_Time      = 0.2f; // should be range [0.1f, 1f]
    
    public string NewElements_AutoGen_List                 = "";
    public int    NewElements_Brick_Appear                 = 4; //appear after x song_start
    public int    NewElements_Brick_MoodChange        = 2;  // appear after x mood change
    public int    NewElements_Conveyor_Appear              = 4; //appear after x song_start
    public int    NewElements_Conveyor_MoodChange          = 2; // appear after x mood change
    public int    NewElements_Conveyor_Amount              = 1; // appear 1 per mood change
    public int    NewElements_Teleport_Appear              = 4; //appear after x song_start
    public int    NewElements_Teleport_MoodChange          = 2; // appear after x mood change
    public int    NewElements_Teleport_Amount              = 1; // appear 1 per mood change
    public int    NewElements_FadeOut_Appear               = 4; //appear after x song_start
    public int    NewElements_FadeOut_MoodChange           = 2; // appear after x mood change
    public int    NewElements_FadeOut_Amount               = 1; // appear 1 per mood change
    public int    NewElements_MovingCircle_Appear          = 4; //appear after x song_start
    public int    NewElements_MovingCircle_MoodChange      = 3; // appear after x mood change
    public int    NewElements_MovingCircle_Amount          = 1; // appear 1 per mood change
    public int    NewElements_HyperBoost_Appear            = 4; //appear after x song_start
    public int    NewElements_HyperBoost_MoodChange        = 3; // appear after x mood change
    public int[]  NewElements_HyperBoost_Repeat            = new int[] {3, 5}; // repeat after song_start
    public int    NewElements_Mirror_Appear                = 4; //appear after x song_start
    public int    NewElements_Mirror_MoodChange            = 3; // appear after x mood change
    public int[]  NewElements_Mirror_Repeat                = new int [] {3,5}; // repeat after song_start
    public int    NewElements_UpSideDown_Appear            = 4; //appear after x song_start
    public int    NewElements_UpSideDown_MoodChange        = 3; // appear after x mood change
    public int[]  NewElements_UpSideDown_Repeat            = new int [] {3,5}; // repeat after song_start
    public int    NewElements_FakeTile_MoodChange          = 1;// appear after x mood change
    public int    NewElements_FakeTile_Amount              = 10; // appear x per mood change
    public int    NewElements_MovingTile_MoodChange        = 1;// appear after x mood change
    public int    NewElements_MovingTile_Amount            = 10; // appear x per mood change
    public float  NewElements_HyperBoost_FlyHeight         = 6;
    public string NewElements_HyperBoost_ObjectType        = "music";
    public int    NewElements_AutoGen_Group_Start     = 0;
    public bool   NewElements_RuleShow_Enable            = false;
    public string NewElements_RuleShow_Order             = string.Empty;
    public int[]  NewElements_RuleShow_SongCount         = Array.Empty<int>();
    public int    NewElements_RuleShow_NewElementPercent = 20;
    public int    NewElements_RuleShow_OldElementPercent = 10;

    #endregion

    public bool  PreviewMusic_Improve_IsEnable          = false;
    public bool  PreviewMusic_AutoFixScrollviewPosition = false;
    public int   PreviewMusic_Index                     = 0;
    public float PreviewMusic_TimeDelay                 = 0f;
    public bool  PreviewMusic_AutoReplayPreviousSong    = false;
    public bool  PreviewMusic_AutoResetWhenComeBackHome = false;
    public int   PreviewMusic_LoopCount                 = 0;
    public bool  PreviewMusic_AutoPlayBGM               = false;
    

    public bool   MidiMultipleVersion_IsEnable = false;
    public string MidiMultipleVersion_Config   = string.Empty;
    
    #region Hybryd
    [Space]
    [Header("Hybrid")]
    public bool   Hybrid_Achievement_IsEnable  = false;
    public string Hybrid_Achievement_ConfigUrl = string.Empty;
    
    public bool   Hybrid_Mission_IsEnable      = false;
    public string Hybrid_Mission_Daily = "order,id,missionType,count,reward_type_1,reward_count_1,reward_type_2,reward_count_2,reward_type_3,reward_count_3||0,1,start_x_songs,1,Diamond,10,,,,||1,2,replay_x_times,2,Diamond,50,,,,||2,3,get_free_gem_in_shop_x_times,1,Diamond,30,,,,||3,4,start_x_songs,2,Diamond,15,,,,||4,5,unlock_x_songs_with_ads,2,Diamond,20,,,,";
    public string Hybrid_Mission_Weekly = "order,id,missionType,count,reward_type_1,reward_count_1,reward_type_2,reward_count_2,reward_type_3,reward_count_3||0,1,reach_1_stars_x_times,1,Diamond,10,Song,1,,,||1,2,reach_2_stars_x_times,1,Diamond,50,Song,1,,,||2,3,unlock_x_songs_with_diamond,1,Diamond,30,Song,2,Ball,1||3,4,play_x_songs_in_endless_mode,1,Diamond,15,Ball,1,,,||4,5,play_x_songs_and_get_at_least_2_stars,1,Diamond,20,Ball,1,,,";
    public string Hybrid_Mission_Instant = "order,id,missionType,count,reward_type_1,reward_count_1,reward_type_2,reward_count_2,reward_type_3,reward_count_3||0,1,reach_2_stars_x_times,1,Diamond,10,,,||1,2,unlock_x_songs_with_ads,2,Diamond,50,,,||2,3,equip_other_ball_and_play_x_songs,1,Diamond,30,,,||3,4,finish_x_rounds_in_endless_mode,1,Ball,1,,,";
    public bool   Hybrid_Mission_RewardBall_Pool_IsEnable = false;
    public bool   Hybrid_Mission_RewardBall_Random        = false;
    public int[]  Hybrid_Mission_RewardBall_Pool;
    #endregion
    
    #region User Progression
    [Space]
    [Header("User Progression")]
    public bool   UserProgression_IsEnable                    = false;
    public string UserProgression_SongPack_ConfigUrl          = string.Empty;
    public int[]  UserProgression_ExpConfig                   = new int[] { };
    public int    UserProgression_SongPack_ReplaceGem         = 150;
    public bool   UserProgression_RandomPreviewMusic          = false;
    public string UserProgression_UnlockAll_Type              = "LOCK";
    public int    UserProgression_UnlockAll_Value             = 100;
    public bool   UserProgression_ShowLockedSong              = false;
    public bool   UserProgression_ShowDiscoverBeforeUnlockAll = false;
    public int    UserProgression_StarPercentRequired         = 100;
    public bool   UserProgression_DualUnlockSong              = false;
    public int    UserProgression_EndlessMode_LimitTile       = 0;
    
    /// <summary>
    /// Config location show popup gems tuto reward: result screen ngay sau khi complete tuto hoặc ở home screen
    /// </summary>
    public bool   UserProgression_TutoRewardLocatedAtResult = true;

    #endregion

    public bool   StarsJourney_IsEnable = false;
    public string StarsJourney_ConfigUrl;
    public int    StarsJourney_ReplaceGem           = 50;
    public bool   StarsJourney_IsLockTopMenu        = false;
    public bool   StarsJourney_StarsFlyFromSongDisk = true;
    public bool   BadgeNoti_EnableSyncAnimation     = false;

    #region Instant Mission

    [Header("Instant Mission")]
    public bool   InstantMission_Enable = false;
    public string InstantMission_Config = "id,mission,value,reward,reward_value,points,genre||1,start_x_songs,1,Gem,10,,||2,equip_other_ball_x_times,1,Gem,10,,||3,get_x_tile,100,Gem,10,,||4,get_x_perfect,20,Gem,10,,||5,get_at_least_x_point,1,Gem,10,1000,||6,get_x_stars,4,Gem,10,,||7,continue_x_times,1,Gem,10,,||8,play_x_songs_in_genre_y,2,Gem,10,,POP||9,adjust_sound,1,Gem,10,,||10,view_profile,1,Gem,10,,||11,claim_x_star_reward,1,Gem,10,,||12,play_x_songs_in_endless_mode,1,Gem,10,,||13,play_x_songs_in_genre_y,2,Gem,10,,ROCK||14,play_x_songs_in_genre_y,2,Gem,10,,EDM||15,play_x_songs_in_genre_y,2,Gem,10,,HIP-HOP";
    #endregion

    private DefaultEarningConfig _defaultEarningConfig;

    [Header("Old user gems: ")] public int OldUserGem_Milestone_A       = 100;
    public                             int OldUserGem_Milestone_B       = 200;
    public                             int OldUserGem_Milestone_C       = 300;
    public                             int OldUserGem_Reward_A          = 300;
    public                             int OldUserGem_Reward_B          = 200;
    public                             int OldUserGem_Reward_C          = 100;
    public                             int OldUserGem_Reward_C_MoreThan = 50;

    #region Discovery Challenge
    [Space]
    [Header("Dicovery Challenge")]
    public bool DiscoveryChallenge_Is_Enable = false;
    public string DiscoveryChallenge_Config             = string.Empty;
    public string DiscoveryChallenge_ChallengeList_Path = "";
    public string DiscoveryChallenge_HardcoreList_Path  = "";
    public string DiscoveryChallenge_LocalizedTexts;
    public int    DiscoveryChallenge_NoAdTimeFromSongStart         = 10;
    public int    DiscoveryChallenge_ConsecutiveNoAdSongStartLimit = 3;
    public int    DiscoveryChallenge_RwFsInterval                  = 25;
    public bool   DiscoveryChallenge_NewUI                          = false;
    #endregion

    [Header("Challenge For Old User")] public bool   ChallengeOldUser_IsEnable = false;
    public                                    string ChallengeOldUser_Config   = String.Empty;

    [Space] [Header("IAP Summer Party Pack")]
    public string SeasonalPack_PackName = string.Empty;

    public string SeasonalPack_Config;
    public string ListHiddenSongs;
    public string ListHiddenBalls;

    [Space]
    [Header("Shop Ball Improvement")] 
    public bool   ShopBall_EntryPointV2_IsEnable = false;
    public bool   ShopBall_UseCategoryLayout = false;
    public int    ShopBall_LayoutStyleIndex  = 2;
    public bool   ShopBall_SaleOff_IsEnable  = false;
    public string ShopBall_Category_Tags;
    public bool   ShopBall_Transition_IsEnable      = false;
    public bool   ShopBall_TooltipNewBalls_IsEnable = false;
    public bool   ShopBall_DarkLockedIcon           = false;

    public bool ShopBall_Rotate_IsEnable   = false;
    public int  ShopBall_Rotate_Sensitive  = 120;
    public bool ShopBall_Rotate_Onboarding = false;
    public bool ShopBall_VerticleScroll    = false;

    public bool Playgap_IsEnable = false;
    [Space]
    [Header("Pause and Quit in Action Phase")]
    public bool PauseQuitAP_DeviceBackButton_IsIgnore = false;

    public bool PauseQuitAP_PauseButton_IsEnable        = false;
    public bool PauseQuitAP_ExitButton_IsEnable         = false;
    public bool PauseQuitAP_EndButtonBottom             = false;
    public bool PauseQuitAP_UsingResumeButton           = false;
    public bool PauseQuitAP_ButtonSettingWithBackground = false;

    public string AssetBundle_SongCard_Artist =
        "A-ha;AddItUp;AintNoMountainHighEnough;ArigatoDooDahJP_white;BabyOneMoreTime;Backstreet_Boys;BadHabits;BeeBB_white;BeepBop_bpm128;BetteDavisEyes;BeWithYou;Beyonce;BIGBANG;BillieEilish;Billie_Eilish;BLACKPINK;bloodymary;Britney_Spears;Byrd;CallMe;CandyShop-Explicit-;Chandelier;CharliePuth2_white;CharliePuth3;Coldplay;Crazy_Frog;DaddyCool;deathbed;Dilemma;DojaCatSZA;DojoCat;DontBringMeDown_white;driverslicense;Em_Beihold;EndlessLove;EveryBreathYouTake;Extravaganza_bpm120;Fireflies;gheiudaucuaemoi;Giveon;GoldenSamurai;Greatestoflove;Haddaway;Heartbreak;howyoulikethat;IAmBetterOff;IfIWasYourGirlfriend_white;IfIWereaBoy;IllBeMissingYou-Explicit-;ILoveU;Imagine_Dragons;IWantItThatWay;JapaneseSouvenir_white;Karol_G;KobeSteak;L-O-V-E;LadyGaga;LaLa_bpm97;leviating;Lewis_Capaldiv;LightItUp_bpm112;LikeaVirgin;LosingMyCool;Lukas_Graham_white;MaskedWolf;MaterialGirl;Monody;OneRepublic;Paradise_white;Phao_-_KAIZ_Remix;PinkSweat_white;PlanetOfTheBass;PostMalone2;PushIt;Renegade_bpm105;Salt-N-Pepa;Sia;Slushii;Smooth;SomeoneYouLoved;standbyme;Stephen_Sanchez;STFU_bpm80;StickTogether_white;SunnyDay_Electrojam;SuperFreak;SymphonyHeartbeat_white;TakeonMe;TheCalling;TheFatRatHunger;thescientist;Thunder;Tom_Walker;TONES_AND_I;Toxic;TurnItUpCOERemix;TwentyOnePilot;Twenty_One_Pilots;UCantTouchThis;Un-BreakMyHeart;Unity;UntilIFoundYou;UptownGirl;Vicetone;VidaDeRico;vietkieu;VivaLaVida;WakeMeUp;YouSetMyWorldOnFire;Yummy;";

    [Space] 
    [Header("UI Home Decorations")]
    public bool HomeDecor_UserProfile = false;

    public bool  HomeDecor_SongCard         = false;
    public bool  HomeDecor_BottomMenuDecal  = false;
    public bool  HomeDecor_SpecialParticles = false;
    public bool  HomeDecor_SongDisk         = false;
    public float RemoteConfig_TimeCached    = 0; //Hours

    #region UI Transitions

    public bool UITransition_IsTransitionHomeAndTabs     = false;
    public bool UITransition_IsTransitionHomeAndGameplay = false;
    public bool UITransition_IsSongCardsAppearTransition = false;

    #endregion

    /// <summary>
    /// show tối đa 3 button ở left menu theo độ ưu tiên
    /// </summary>
    [Space] [Header("Home Icons UX")] public bool Home_LeftMenu_ButtonPriorities_IsEnable = false;

    /// <summary>
    /// Gộp các entry points vào một nhóm, expand/collapse bằng toggle button ở right menu
    /// Các gói bán chuyển sang left menu
    /// </summary>
    public bool Home_RightMenu_Toggle_IsEnable = false;

    /// <summary>
    /// Config các entry point nào sẽ được gom nhóm
    /// Các entry point không có trong config sẽ không bị ảnh hưởng
    /// </summary>
    public string Home_RightMenu_Toggle_Features = string.Empty;

    public bool Home_DiskFallAnimation_IsEnable = false;
    public bool Home_ButtonAppearAnimation_IsEnable = false;

    [Space] [Header("VFX Touch")] public bool VFX_Touch_IsEnable = false;

    [Header("ShopInHome Preview")]
    // Mặc định: sử dụng renderTexture để preview ball => nếu không thì sử dụng camera UI
    public bool BallPreviewInHome_RenderTexture_IsEnable = true;

    #region LiveEvent - Ball Spinner
    [Space]
    [Header("LiveEvent - Ball Spinner")]
    public bool    LiveEvent_BallSpinner_Enable        = false;
    public int     LiveEvent_BallSpinner_SongStart     = 3;
    public int     LiveEvent_BallSpinner_ID            = 0;
    public int     LiveEvent_BallSpinner_StartCoin     = 1000;
    public int     LiveEvent_BallSpinner_PriceBet      = 1;
    public float   LiveEvent_BallSpinner_RateBet       = 0.065f;
    public float[] LiveEvent_BallSpinner_RewardNormal  = new float[] { 0, 0, 3, 5, 10, 20 };
    public float[] LiveEvent_BallSpinner_RewardSpecial = new float[] { 0, 0, 5, 10, 30, 50 };
    public float[] LiveEvent_BallSpinner_RewardBingo   = new float[] { 0, 0, 0, 0, 0, 100 };
    public int[]   LiveEvent_BallSpinner_BallNormal    = new int[] { 0, 62, 6, 65 };
    public int[]   LiveEvent_BallSpinner_BallSpecial   = new int[] { 117, 123, 126, 127 };

    public float[] LiveEvent_BallSpinner_WinRate   = new float[10] { 85, 80, 70, 50, 20, 5, 1.2f, 0.6f, 0.1f, 0.02f };
    public float[] LiveEvent_BallSpinner_WinValue  = new float[10] { 120, 110, 105, 102, 100, 98, 95, 95, 92, 90 };
    public float[] LiveEvent_BallSpinner_LoseRate  = new float[10] { 0.02f, 0.06f, 0.3f, 1f, 5, 20, 50, 70, 80, 85 };
    public float[] LiveEvent_BallSpinner_LoseValue = new float[10] { 90, 92, 95, 95, 98, 100, 102, 105, 110, 120 };

    public int LiveEvent_BallSpinner_Gap1 = 20000;
    #endregion

    public bool   HomePopupFlow_IsEnable = false;
    public string HomePopupFlow_Ordering;
    
#if UNITY_IOS
        Enable_LocalSong = false;
#endif
    public float[] LiveEvent_BallSpinner_Gap1_WinRate =
        new float[10] { 50, 40, 30, 15, 10, 5, 1.2f, 0.6f, 0.1f, 0.02f };

    public float[] LiveEvent_BallSpinner_Gap1_WinValue  = new float[10] { 34, 30, 25, 22, 18, 16, 12, 10, 6, 1 };
    public float[] LiveEvent_BallSpinner_Gap1_LoseRate  = new float[10] { 15, 20, 25, 30, 40, 45, 50, 55, 70, 80 };
    public float[] LiveEvent_BallSpinner_Gap1_LoseValue = new float[10] { 120, 112, 102, 96, 90, 74, 75, 66, 50, 38 };

    public int LiveEvent_BallSpinner_Gap2 = 40000;

    public float[] LiveEvent_BallSpinner_Gap2_WinRate =
        new float[10] { 50, 40, 30, 15, 10, 5, 1.2f, 0.6f, 0.1f, 0.02f };

    public float[] LiveEvent_BallSpinner_Gap2_WinValue  = new float[10] { 34, 30, 25, 22, 18, 16, 12, 10, 6, 1 };
    public float[] LiveEvent_BallSpinner_Gap2_LoseRate  = new float[10] { 15, 20, 25, 30, 40, 45, 50, 55, 70, 80 };
    public float[] LiveEvent_BallSpinner_Gap2_LoseValue = new float[10] { 120, 112, 102, 96, 90, 74, 75, 66, 50, 38 };

    public string LiveEvent_BallSpinner_Mission     = string.Empty;
    public string LiveEvent_BallSpinner_Store       = string.Empty;
    public string LiveEvent_BallSpinner_Achievement = string.Empty;

    #endregion

    #region Fever Mode
    public bool  FeverMode_IsEnable = false;
    public int   FeverMode_Perfect  = 20;
    public float FeverMode_Multiply = 1.15f;
    public float FeverMode_Time     = 15;
    public float FeverMode_Cooldown = 5f;
    #endregion
    #region Level Play
    [Space]
    [Header("Level Play")]
    public string Android_LevelPlay_BannerIDs       = "0dr8wyf1n6b86vlu"; // "0dr8wyf1n6b86vlu;iyq50z3mhksuo0xo";
    public string Android_LevelPlay_InterstitialIDs = "up0rgv7m9guscthh"; // "up0rgv7m9guscthh;fnw2hyp4paqjrkx7";
    public string Android_LevelPlay_RewardIDs       = "6cqhrmk12kgoq8fk"; // "6cqhrmk12kgoq8fk;ekx7z1hig5dnye11";

    public string IOS_LevelPlay_BannerIDs       = "bpem0r5rgom4sbyc";
    public string IOS_LevelPlay_InterstitialIDs = "mkkjq6b416y8nwoe";
    public string IOS_LevelPlay_RewardIDs       = "qab3gwby8hjuqehl";
    #region Tile Appearances
    
    public bool   TileAppearance_IsRandomEachSong = false;
    public string TileAppearance_Type;
    public string TileAppearance_Pool;

    #endregion

    public float LevelPlay_TimeDelayLoadAd = 1f;
    public bool  LevelPlay_RenewAd;

    //public int   ItemRevive_Start          = 0;
    public string Ignore_LogEvents = "AssetBundle_Load;AssetBundle_LoadDone;AssetBundle_LoadFail";
    public string Segments = "[\"VeryLowECPM\",\"0.004\",\"LowECPM\",\"0.01\",\"MediumECPM\",\"0.02\",\"HighECPM\",\"0.05\",\"VeryHighECPM\"]";

    #endregion

    #region GALAXY QUEST

    [Space][Header("Galaxy Quest")]
    public bool GalaxyQuest_IsEnable = false;
    public string GalaxyQuest_Config = "";

    #endregion
    
    #region Boosters
    [Space]
    [Header("Boosters")]
    public bool  Booster_IsEnable = false;
    public int   Booster_Shield_StarUnlock      = 6;
    public int   Booster_Shield_SongStartUnlock = 2;
    public int   Booster_Shield_Onboard_Gift    = 2;
    public float Booster_Shield_InvincibleTime  = 4f;
    public int   Booster_Shield_PriceExchange  = 100;
    
    public int   Booster_Magnet_StarUnlock      = 9;
    public int   Booster_Magnet_SongStartUnlock = 3;
    public int   Booster_Magnet_Onboard_Gift    = 2;
    public float Booster_Magnet_Range           = 6f;
    public float Booster_Magnet_Strong          = 40;
    
    public int   Booster_GemRain_StarUnlock      = 12;
    public int   Booster_GemRain_SongStartUnlock = 4;
    public int   Booster_GemRain_Onboard_Gift    = 2;
    public int   Booster_GemRain_MoodChange      = 2;
    public float Booster_GemRain_Duration        = 15f;
    public int   Booster_GemRain_AmountGem       = 30;
    public int   Booster_GemRain_PriceExchange   = 50;
    public int   Booster_GemRain_ValueGem        = 5;
    
    public int   Booster_TidyTile_StarUnlock      = 15;
    public int   Booster_TidyTile_SongStartUnlock = 5;
    public int   Booster_TidyTile_Onboard_Gift    = 2;
    public float Booster_TidyTile_Ratio           = 0.6f;
    public int   Booster_TidyTile_PriceExchange     = 100;

    public int Booster_HyperBoost_MoodChange = 2;
    public int Booster_HyperBoost_Amount     = 20;
    public int Booster_HyperBoost_ValueGem      = 5;

    #endregion
    #region Power Cube
    [Space]
    [Header("Power Cube")]
    public bool   PowerCube_IsEnable         = false;
    public int    PowerCube_Unlock_SongStart = 2;
    public int    PowerCube_Unlock_Star      = 6;
    public float  PowerCube_ActiveTime_Minute    = 15f; 
    public string PowerCube_Streak1_Reward   = "Magnet";
    public string PowerCube_Streak2_Reward   = "Magnet;Shield";
    public string PowerCube_Streak3_Reward   = "Magnet;Shield;TileTidy";
    #endregion

    public string Background_urlHome;
    public string Background_urlSubs;
}