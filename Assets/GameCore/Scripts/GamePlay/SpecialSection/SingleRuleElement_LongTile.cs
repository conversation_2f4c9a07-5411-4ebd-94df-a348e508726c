using UnityEngine;

namespace TileHop.GamePlay.NewElement {
    public class SingleRuleElement_LongTile : SingleRuleElement {
        public override NoteElementType elementType => NoteElementType.LongTile;

        public SingleRuleElement_LongTile() {
            curRatio = ratio;
        }

        public override void CheckExist(int songStart) {
            isExist = true;
        }

        public override NoteElementType Process(byte mood, NoteElementType defaultElementType) {
            if (!isExist) {
                return defaultElementType;
            }

            if (Random.Range(0, 9999) % 100 > curRatio) {
                curRatio++;
                return defaultElementType;
            } else {
                curRatio = ratio;
            }

            return elementType;
        }
    }
}