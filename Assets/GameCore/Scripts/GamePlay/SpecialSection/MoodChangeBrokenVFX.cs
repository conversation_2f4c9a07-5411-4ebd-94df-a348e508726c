using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;

public class MoodChangeBrokenVFX : MonoBehaviour {
    [SerializeField] private Canvas         mainCanvas;
    [SerializeField] private Image[]        crashes;
    [SerializeField] private ParticleSystem vfxBroken;
    [SerializeField] private float          fadeTime = 0.4f;

    private byte _vfxLength;

    private void Awake() {
        mainCanvas.worldCamera = Camera.main;
        _vfxLength = (byte) crashes.Length;
    }

    public void ShowCrash(byte offset) {
        if (_vfxLength == 0) {
            _vfxLength = (byte) crashes.Length;
        }

        if (offset >= _vfxLength)
            return;

        mainCanvas.enabled = true;
        switch (offset) {
            case 2:
                if (crashes[offset] == null)
                    return;
                crashes[offset].gameObject.SetActive(true);
                crashes[offset].color = Color.clear;
                crashes[offset].DOColor(Color.white, fadeTime);
                break;

            case 1:
                if (crashes[offset] == null)
                    return;
                crashes[offset].gameObject.SetActive(true);
                crashes[offset].color = Color.clear;
                crashes[offset].DOColor(Color.white, fadeTime);
                break;

            case 0:
                if (crashes[offset] == null)
                    return;
                crashes[offset].gameObject.SetActive(true);
                
                crashes[offset].color = Color.clear;
                crashes[offset].DOColor(Color.white, fadeTime/2).OnComplete(() => {
                    crashes[offset].DOColor(Color.clear, fadeTime/2).OnComplete(() => {
                        crashes[offset].gameObject.SetActive(false);
                    });
                });
                
                crashes[1].DOColor(Color.clear, fadeTime).OnComplete(() => {
                    crashes[1].gameObject.SetActive(false);
                });
                crashes[2].DOColor(Color.clear, fadeTime).OnComplete(() => {
                    crashes[2].gameObject.SetActive(false);
                });
                if (vfxBroken) {
                    vfxBroken.Play();
                }

                break;

            default:
                crashes[0].gameObject.SetActive(false);
                crashes[1].gameObject.SetActive(false);
                crashes[2].gameObject.SetActive(false);
                break;
        }
    }

    public void Hide() {
        mainCanvas.enabled = false;
        crashes[0].gameObject.SetActive(false);
        crashes[1].gameObject.SetActive(false);
        crashes[2].gameObject.SetActive(false);
    }
}