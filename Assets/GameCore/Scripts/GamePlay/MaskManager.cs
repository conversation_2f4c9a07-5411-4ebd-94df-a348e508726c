using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Spine.Unity;

public class MaskManager : MonoBehaviour {
    public static MaskManager instance;

    [SerializeField] private List<SkeletonAnimation> maskAnim;
    [SerializeField] private List<ParticleSystem> maskEffect;

    private float _timeScale;

    private void Awake() {
        instance = this;
    }

    public void PlayMaskEffect(bool isStrongNote = false, float nextNoteTime = 1f) {
        if (isStrongNote) {
            ParticleSystem system = maskEffect[Spawner.s.currentIndexSkin];
            if (system.gameObject.activeSelf) {
                system.Play();
            }
        }

        StopAllCoroutines();
        SkeletonAnimation skeletonAnimation = maskAnim[Spawner.s.currentIndexSkin];
        if (skeletonAnimation.gameObject.activeSelf) {
            skeletonAnimation.AnimationState.SetAnimation(0, "Bump", false);
            _timeScale = (1f / nextNoteTime) > 1 ? 1f / nextNoteTime : 1f;
            skeletonAnimation.AnimationState.TimeScale = _timeScale;
            if (gameObject.activeSelf) {
                StartCoroutine(BackToIdle(0.5f / _timeScale));
            }
        }
    }

    private IEnumerator BackToIdle(float duration = 0.5f) {
        yield return new WaitForSeconds(duration);

        maskAnim[Spawner.s.currentIndexSkin].AnimationState.TimeScale = 1;
        maskAnim[Spawner.s.currentIndexSkin].AnimationState.SetAnimation(0, "Idle", true);
    }
}