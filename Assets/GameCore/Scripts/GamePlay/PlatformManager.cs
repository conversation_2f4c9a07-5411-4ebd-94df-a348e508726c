using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using DG.Tweening;
using GamePlay.Levels;
using UnityEngine;
using UnityEngine.Pool;
using Random = UnityEngine.Random;

public class PlatformManager : MonoBehaviour {
    #region Fields

    //~~~~~~~~~~~~~~~~~ static ~~~~~~~~~~~~~~~~~
    private const  string           ResourcePath = "Platforms/{0}";
    private const  string           FakeTileOrig = "FakeTileOrig";

    //~~~~~~~~~~~~~~~~~ public ~~~~~~~~~~~~~~~~~
    [SerializeField]  private float platformLargeRatio = 0.146f;
    [HideInInspector] public  int   cloneTotal         = 0;
    [HideInInspector] public  int   blockTotal         = 0;

    [SerializeField] private GameObject defaultNormalPlatform;
    [SerializeField] private Platform   defaultFullPlatform;

    private Platform _platformFullTile;
    private Platform _platformSmallMoodColor;
    private Platform _platformLongTile;
    private Platform _platformRhythmic;
    private Platform _platformVocal;
    private Platform _platformMelodic;
    private Platform _platformEndHuman; //TH-395

    [SerializeField] private Platform platformLongTile;
    [SerializeField] private Platform platformStraightLongTile;

    private Platform platformLongTileS;

    [SerializeField] private Platform platformHitchHike;
    [SerializeField] private Platform platformPipeTile;
    [SerializeField] private Platform platformBreakableLongTile;
    [SerializeField] private Platform platformCombinableLongTile;

    public                   Diamond    prefDiamond;
    [SerializeField] private RhythmNote prefRhythmNote;
    [SerializeField] private RhythmNote prefRhythmNoteV2;

    [Space] [SerializeField] MaterialPlatform materialPlatform;
    public                   UIScoreIngame    prefabScoreIngame;

    //~~~~~~~~~~~~~~~~~ private ~~~~~~~~~~~~~~~~~
    private List<Platform> _blockList;
    private List<Platform> _cloneList;

    private Platform                              _prefDefaultPlatform;
    private Dictionary<NoteData.Timbre, Platform> _prefFakeTile = new Dictionary<NoteData.Timbre, Platform>();

    private RemoteConfig remoteConfig => RemoteConfigBase.instance;
    private float _deltaZ    = 80;
    private float _deltaTime = 0.05f;
    private float _moveTime  = 0.3f;

    private List<GameObject> origFakeTiles = ListPool<GameObject>.Get();

    private bool IsUsingTimbre => remoteConfig.Musicalization_Timbre_IsEnable;

    private bool IsUsingFullMood =>
        remoteConfig.GetMusicalization_MoodChange_ColorTileId() == (int) PlatformType.MOOD_FULL;

    #endregion

    public RhythmNote PrefRhythmNote => remoteConfig.LongNote_v2_IsEnable ? prefRhythmNoteV2 : prefRhythmNote;

    private static bool                     _isInitAppearanceConfig;
    private static int                      _numberOfAppearType;
    private static int                      _songAppearTypeMoodChangeIndex;
    private static List<TileAppearanceType> _appearanceTypePool;
    private static TileAppearanceType       _tileAppearanceType = TileAppearanceType.BASIC;

    public static TileAppearanceType tileAppearanceType => _tileAppearanceType;

    private void Start() {
        prefDiamond.CreatePool(5);
        Configuration.instance.InitTimeCurveMoving();
    }

    public static void ProcessTilesAppearType() {
        // init tile moving appeareance types
        if (!_isInitAppearanceConfig) {
            if (string.IsNullOrEmpty(RemoteConfigBase.instance.TileAppearance_Pool)) {
                _numberOfAppearType = Enum.GetValues(typeof(TileAppearanceType)).Length;
            } else {
                var appearanceStringPool = Util.StringToList(RemoteConfigBase.instance.TileAppearance_Pool);
                _appearanceTypePool = new List<TileAppearanceType>();
                foreach (var typeString in appearanceStringPool) {
                    if (Enum.TryParse(typeString, out TileAppearanceType type)) {
                        _appearanceTypePool.Add(type);
                    }
                }

                _numberOfAppearType = _appearanceTypePool.Count;
            }

            _isInitAppearanceConfig = true;
        }

        // random appear type at start of every song
        if (RemoteConfigBase.instance.TileAppearance_IsRandomEachSong) {
            RandomAppearanceType();
        } else {
            _songAppearTypeMoodChangeIndex = -1;
            if (!Enum.TryParse(RemoteConfigBase.instance.TileAppearance_Type, out _tileAppearanceType)) {
                _tileAppearanceType = TileAppearanceType.BASIC;
            }
        }
    }

    private void OnDestroy() {
        ListPool<GameObject>.Release(origFakeTiles);
        ListPool<Platform>.Release(_blockList);
        ListPool<Platform>.Release(_cloneList);
    }
    private static void RandomAppearanceType() {
        if (!RemoteConfigBase.instance.TileAppearance_IsRandomEachSong) {
            return;
        }

        // set current Appear Type index
        int nextType;
        int curType;
        bool haveConfigPool = _appearanceTypePool != null;

        if (haveConfigPool) {
            curType = _appearanceTypePool.IndexOf(_tileAppearanceType);
        } else {
            curType = (int) _tileAppearanceType;
        }

        // try to get new different type index
        int attempt = 0;
        do {
            attempt++;
            nextType = Util.GetBetterRandomInt(0, _numberOfAppearType);
        } while (nextType == curType && attempt < 100);

        // set the type by its index, based on whether the Type pool is used.
        if (haveConfigPool) {
            _tileAppearanceType = _appearanceTypePool[nextType];
        } else {
            _tileAppearanceType = (TileAppearanceType) nextType;
        }
    }

    public static void SwitchTileAppearanceOnMoodChange() {
        if (RemoteConfigBase.instance.TileAppearance_IsRandomEachSong || _appearanceTypePool.IsNullOrEmpty()) {
            return;
        }

        _songAppearTypeMoodChangeIndex = (_songAppearTypeMoodChangeIndex + 1) % _appearanceTypePool.Count;
        _tileAppearanceType = _appearanceTypePool[_songAppearTypeMoodChangeIndex];
    }

    private bool IsUseNewTile() {
        // Newtile là tile kiểu trắng -> đổ màu lên tile
        return ThemeManager.IsUseNewTile();
    }

    public Platform GetDefaultPlatform() {
        if (_prefDefaultPlatform != null) {
            return _prefDefaultPlatform;
        }

        string pathPlatform;

        if (ThemeManager.IsXmas2021()) {
            pathPlatform = string.Format(ResourcePath, "xmas");
        } else if (IsUseNewTile()) {
            pathPlatform = string.Format(ResourcePath, "NewTile");
        } else {
            pathPlatform = string.Format(ResourcePath, remoteConfig.forcePlatformId);
        }

        GameObject platform;
        if (defaultNormalPlatform == null) {
            platform = Resources.Load<GameObject>(pathPlatform);
        } else {
            platform = defaultNormalPlatform;
        }

        if (platform == null) {
            platform = Resources.Load<GameObject>(string.Format(ResourcePath, 0));
        }

        if (platform == null || !platform.TryGetComponent(out _prefDefaultPlatform)) {
            _prefDefaultPlatform = null;
            CustomException.Fire("[GetDefaultPlatform]", "Can't load default platform");
        }

        return _prefDefaultPlatform;
    }

    public void GeneratePlatform(bool isHasClone, Song song) {
        if (remoteConfig.ImproveSensitive_IsEnable && song.isTutorialSong && !song.IsPlayed()) {
            blockTotal = 4;
        } else if (GameController.instance.isUpdateSensitive || Configuration.instance.isOpenSensitiveFromHome) {
            blockTotal = 4;
        } else {
            blockTotal = remoteConfig.GetTileTotalFront() + 1;
        }

        cloneTotal = isHasClone ? blockTotal * 2 : 0;

        _blockList = ListPool<Platform>.Get();
        _cloneList = ListPool<Platform>.Get();
        _prefFakeTile.Clear();
        _ratio = GetRatio();

        _prefDefaultPlatform = GetDefaultPlatform();

        _prefDefaultPlatform.CreatePool(blockTotal + 3);
        MakeFakeTile(NoteData.Timbre.None, _prefDefaultPlatform);

        if (IsUsingFullMood) {
            InitFullMoodTile();
            _platformFullTile.CreatePool(2);
        } else {
            InitSmallMoodTile();
            _platformSmallMoodColor.CreatePool(2 * blockTotal);
        }

        if (remoteConfig.ImproveSensitive_IsEnable) {
            InitFullMoodTile();
            _platformFullTile.CreatePool(2);
        }

        if (IsUsingTimbre) {
            InitTimbreTile();
            CreatePoolTimbreTile();
        }

        if (platformStraightLongTile) {
            platformStraightLongTile.CreatePool(2);
        }

        InitEndHumanTile();
        _platformEndHuman.CreatePool(1);

        InitLongType();
        _platformLongTile.CreatePool(3);
    }

    private void InitTimbreTile() {
        _platformRhythmic = Resources.Load<Platform>(string.Format(ResourcePath, "Drum"));
        _platformVocal = Resources.Load<Platform>(string.Format(ResourcePath, "Vocal"));
        _platformMelodic = Resources.Load<Platform>(string.Format(ResourcePath, "Melodic"));
    }

    private void InitEndHumanTile() {
        _platformEndHuman = Resources.Load<Platform>(string.Format(ResourcePath, "EndHuman"));
    }

    private void InitFullMoodTile() {
        if (_platformFullTile != null) {
            return;
        }

        if (defaultFullPlatform == null) {
            string pathPlatform = "0_FullTile";
            _platformFullTile = Resources.Load<Platform>(string.Format(ResourcePath, pathPlatform));
        } else {
            _platformFullTile = defaultFullPlatform;
        }
    }

    private void InitLongType() {
        if (_platformLongTile != null) {
            return;
        }

        if (NotesManager.instance.isMidiGenerateTile || NotesManager.instance.useHaptic) {
            _platformLongTile = Resources.Load<Platform>(string.Format(ResourcePath, "LongTile_Haptic"));
            return;
        }

        if (platformLongTile != null) {
            _platformLongTile = platformLongTile;
            return;
        }

        //get default
        _platformLongTile = Resources.Load<Platform>(string.Format(ResourcePath, "LongTile_0"));
    }

    private void InitSmallMoodTile() {
        if (_platformSmallMoodColor != null) {
            return;
        }

        string pathPlatform = "0_Color";
        // if (ThemeManager.IsXmas2023()) {
        //     pathPlatform = string.Format(ResourcePath, "Xmas_FullTile");
        // } else if (ThemeManager.IsThemeHalloween2023()) {
        //     pathPlatform = string.Format(ResourcePath, "HLW_FullTile");
        // }
        _platformSmallMoodColor = Resources.Load<Platform>(string.Format(ResourcePath, pathPlatform));
    }

    private void CreatePoolTimbreTile() {
        _platformRhythmic.CreatePool(blockTotal);
        MakeFakeTile(NoteData.Timbre.Rhythmic, _platformRhythmic);

        _platformVocal.CreatePool(blockTotal);
        MakeFakeTile(NoteData.Timbre.Vocal, _platformVocal);

        _platformMelodic.CreatePool(blockTotal);
        MakeFakeTile(NoteData.Timbre.Melodic, _platformMelodic);
    }

    private void MakeFakeTile(NoteData.Timbre timbre, Platform platform) {
        GameObject origFakeTile = Instantiate(platform.gameObject, ObjectPool.instance.CachedTransform);
#if UNITY_EDITOR
        origFakeTile.name = FakeTileOrig + "_" + timbre.ToString();
#endif
        origFakeTile.SetActive(false);
        origFakeTiles.Add(origFakeTile);

        Platform prefFakeTileDefault = origFakeTile.GetComponent<Platform>();
        prefFakeTileDefault.CreatePool(cloneTotal);

        _prefFakeTile.Add(timbre, prefFakeTileDefault);
    }

    private float _ratio;

    private float GetRatio() {
        if (_ratio <= 0) {
            if (Spawner.s.skinSet[0].blockSprite == null && !ThemeManager.IsXmas2021()) {
                _ratio = platformLargeRatio;
            } else {
                _ratio = 1f;
            }
        }

        return _ratio;
    }

    private Vector3 _cachedBlockScale;

    public void SpawnFirstPlatforms(Dictionary<int, Vector3> cachedPositions = null) {
        StopHideAllPlatform();
        _cachedBlockScale = Spawner.s.GetBlockScale();

        while (_blockList.Count > 0) {
            Platform platform = _blockList[^1];
            RecycleTile(platform);
        }

        Spawner.s.isPauseSpawnTile = false;
        for (int i = 0; i < blockTotal; i++) {
            Platform platform = Spawner.s.SpawnTile();
            if (platform) {
                if (platform.elementType.IsNeedResetAfterRevive()) {
                    platform.SetElementType(NoteElementType.None, false);
                }

                if (i == 0) {
                    platform.HidePerfectAnim(true);
                    GameController.instance.timePlay = platform.platformData.currNoteObj.timeAppear; //TH-335
                } else {
                    platform.ShowPerfectAnim(false);
                }

                if (cachedPositions != null && cachedPositions.Count > i) {
                    if (cachedPositions.ContainsKey(platform.noteID)) {
                        platform.platformData.positionPlatform = cachedPositions[platform.noteID];
                        platform.SetPosition(cachedPositions[platform.noteID]);
                        MapManager.instance.SetPlatformData(platform.noteID, platform.platformData);
                        Spawner.s.SetPrevPosX(cachedPositions[platform.noteID].x);
                    }

                    platform.ForceUpdateFirstFrame();
                }
            } else {
                break;
            }
        }
    }

    public int GetNumberBlock() {
        return _blockList.Count;
    }

    public void AwakeBlock() {
        foreach (Platform b in _blockList) {
            b.ActivePerfectAnim(false);
            b.gameObject.SetActive(true);
            //if (remoteConfig.CenterAnimation_Enable && !b.platformData.isStrongNote) {
            //    b.ShowPerfectAnim();
            //} else b.perfectAnim.gameObject.SetActive(false);
        }
    }

    #region BackgroundTransition

    public void FinishedSwitchColor(int indexSkin) {
        foreach (Platform t in _blockList) {
            t.FinishedSwitchColor(indexSkin);
        }

        foreach (Platform t in _cloneList) {
            t.FinishedSwitchColor(indexSkin);
        }

        foreach (RhythmNote t in Spawner.s._listRhythmNote) {
            t.FinishedSwitchColor(indexSkin);
        }
    }

    public void SwitchColor(float pctTime, int currentIndex, SkinSet currentSkinSet, int nextIndex,
                            SkinSet nextSkinSet) {
        foreach (Platform t1 in _blockList) {
            t1.SwitchColor(pctTime, currentIndex, currentSkinSet, nextIndex, nextSkinSet);
        }

        foreach (Platform t1 in _cloneList) {
            t1.SwitchColor(pctTime, currentIndex, currentSkinSet, nextIndex, nextSkinSet);
        }

        foreach (RhythmNote t1 in Spawner.s._listRhythmNote) {
            t1.SwitchColor(pctTime, currentSkinSet, nextSkinSet);
        }
    }

    public void SetCloneSkin(int index, bool isSwitchColor = false) {
        foreach (Platform platform in _cloneList) {
            platform.SetSkin(index, isSwitchColor);
        }
    }

    public void SetBlockSkin(int index, bool isSwitchColor = false) {
        foreach (Platform block in _blockList) {
            block.SetSkin(index, isSwitchColor);
        }
    }

    public void SetEffectTexture(int indexSkin) {
        Platform.updatedTextures = false;
        foreach (Platform block in _blockList) {
            block.SetEffectTexture(indexSkin);
        }
    }

    #endregion

    public void UpdateTileWhenContinue() {
        for (int index = 0; index < _blockList.Count; index++) {
            Platform platform = _blockList[index];
            if (platform.platformType == PlatformType.FAKE && platform.platformData.stage != Stage.FAKE_TILE) {
                //bỏ hết fake color đi nếu stage != Stage.FAKE_TILE
                //còn trường hợp stage = Stage.FAKE_TILE thì chỉ cần gọi RecycleFakeTiles là đủ
                RecycleTile(platform);
                index--;
            } else {
                platform.gameObject.SetActive(true);
                if (platform.platformType == PlatformType.COLOR_CHOOSING) {
                    //giữ yên vị trí 2 tile này
                } else {
                    platform.transCache.SetPositionX(0); //set main color vào giữa đường
                }

                platform.Moving(false);
                if (platform.elementType != NoteElementType.FakeConveyor) {
                    platform.RecycleFakeTiles();
                }

                switch (platform.platformData.stage) {
                    case Stage.LATE_FAKE:
                        //ẩn hiệu ứng focus của stage LATE_FAKE
                        platform.SetActiveEfxFocus(false);
                        break;

                    case Stage.SKEW_STRAIGHT:
                    case Stage.SKEW_OPPOSITE:
                        platform.transCache.rotation = Quaternion.identity; // reset góc quay
                        break;

                    case Stage.SKEW_SPIRAL:
                        platform.transCache.rotation = Quaternion.identity; // reset góc quay
                        platform.transCache.SetPositionY(0); //reset độ cao Y

                        platform.platformData.stage = Stage.NORMAL; // đưa tile về normal để tránh auto move
                        platform.platformData.platformType = PlatformType.NORMAL;
                        if (platform.platformData.isStartStage) {
                            MapManager.instance.CancelSkewSpiral(platform.platformData.currentIndexNote);
                        }

                        break;
                }
            }
        }

        RecycleAllCloneTile();
    }

    private Coroutine _startCoroutine;

    public void GameOver() {
        float timeWaitEnd = remoteConfig.VFX_Dead_IsEnable && remoteConfig.VFX_Dead_KeepTileVisible ? 1f : 0f;
        _startCoroutine = StartCoroutine(IEWaitAnimationEnd(timeWaitEnd));
    }

    private IEnumerator IEWaitAnimationEnd(float timeWaitEnd) {
        yield return new WaitForSeconds(timeWaitEnd);

            foreach (Platform b in _blockList) {
                b.gameObject.SetActive(false);
            }

            RecycleAllCloneTile();
    }

    private void RecycleAllCloneTile() {
        for (int i = _cloneList.Count - 1; i >= 0; i--) {
            RecycleFakeTile(_cloneList[i]);
        }
    }

    public void StopHideAllPlatform() {
        if (_startCoroutine != null) {
            StopCoroutine(_startCoroutine);
        }
    }

    public void GameRewind() {
        for (int i = 0; i < _blockList.Count; i++) {
            if (i < blockTotal - 2) {
                _blockList[i].gameObject.SetActive(true);
            }
        }
    }

    public Platform GetFirstPlatform() {
        if (_blockList == null || _blockList.Count <= 0) {
            Song currentSong = NotesManager.instance.song;
            Logger.LogError("[GetFirstPlatform] _blockList == null || _blockList.Count <= 0 " + currentSong.name + " " +
                            currentSong.acm_id_v3);
            return null;
        }

        return _blockList.First();
    }

    private void Sort() {
        _blockList.Sort((x, y) => {
            Transform xTransCache = x.transCache ? x.transCache : x.transform;
            Transform yTransCache = y.transCache ? y.transCache : y.transform;
            return xTransCache.position.z.CompareTo(yTransCache.position.z);
        });
    }

    public Platform GetLastPlatform() {
        if (_blockList == null || _blockList.Count <= 0) {
            return null;
        }

        for (int index = 0; index < _blockList.Count; index++) { //try fix PlatformManager.Sort NULL
            Platform platform = _blockList[index];
            if (platform == null) {
                _blockList.RemoveAt(index);
                index--;
                CustomException.Fire("[GetLastPlatform]", "platform == null");
            }
        }

        Sort();
        foreach (Platform p in _blockList) {
            if (!p.hitted) {
                return p;
            }
        }

        return null;
    }

    public void CheckSpawner() {
        //add new logic
        int tileTotalFront = blockTotal - 1;
        if (tileTotalFront > 0) {
            int totalTileFront = GetTotalTileFront() - 1;
            //-1 là do hiện tại tile này hitted = false và chuyển bị = true
            int totalMissTileFront = tileTotalFront - totalTileFront;
            while (totalMissTileFront > 0) {
                Platform spawnTile = Spawner.s.SpawnTile();
                if (spawnTile == null) {
                    break;
                } else {
                    totalMissTileFront--;
                }
            }
        }
    }

    public int GetTotalTileFront() {
        int total = 0;
        for (int index = _blockList.Count - 1; index >= 0; index--) {
            if (!_blockList[index].hitted) {
                if (!_blockList[index].isMirrorTile) {
                    total++;
                }
            } else {
                break;
            }
        }

        return total;
    }

    /// <summary>
    /// GetFirstBlockHit
    /// </summary>
    /// <returns></returns>
    private Platform GetFirstBlockHit() {
        foreach (Platform platform in _blockList) {
            if (platform.hitted) {
                return platform;
            }
        }

        return null;
    }

    public void ClearClone(List<Platform> fakeTiles, Vector3 direction = default) {
        if (fakeTiles == null || fakeTiles.Count == 0) {
            return;
        }

        foreach (Platform b in fakeTiles) {
            if (b.gameObject.activeSelf) {
                StartCoroutine(b.Hide(() => {
                    bool recycleFakeTile = RecycleFakeTile(b);
                    if (!recycleFakeTile) {
                        RecycleTile(b);
                    }
                }, direction));
            } else {
                bool recycleFakeTile = RecycleFakeTile(b);
                if (!recycleFakeTile) {
                    RecycleTile(b);
                }
            }
        }

        fakeTiles.Clear();
    }

    public void CenterPerfectEffect() {
        foreach (Platform b in _blockList) {
            if (b == null) {
                continue;
            }

            if (remoteConfig.CenterAnimation_Enable) {
                b.FocusPerfectStatus(false);
            }

            if (remoteConfig.Tile_PerfectIcon_Enable && !b.hitted) {
                b.ShowPerfectAnim();
            }
        }
    }

    public void DisableCenterEffect() {
        if (!remoteConfig.CenterAnimation_Enable)
            return;
        if (!remoteConfig.Tile_PerfectIcon_Enable)
            return;

        foreach (var b in _blockList.Where(b => !b.hitted && !b.platformData.isStrongNote)) {
            b.ShowPerfectAnim();
            b.FocusPerfectStatus(true);
        }
    }

    public void ChangeBlockSize(int perfectCount, Vector3 blockScaleCurrent, float tileMinSize, float tileMaxSize) {
        float tileDeltaSize = remoteConfig.Tile_DeltaSize;
        Vector3 currentScale = _blockList[0].GetBlockSize();

        if ((perfectCount < 0 && (currentScale.x - tileDeltaSize > tileMinSize)) ||
            (perfectCount > 0 && (currentScale.x + tileDeltaSize < tileMaxSize))) {
            Vector3 increaseAmount = tileDeltaSize * (new Vector3(1, 0, blockScaleCurrent.z / blockScaleCurrent.x));

            if (perfectCount > 0) {
                currentScale += increaseAmount;
            } else {
                currentScale -= increaseAmount;
            }

            foreach (Platform b in _blockList) {
                b.SetBlockSize(currentScale);
            }

            foreach (Platform b in _cloneList) {
                b.SetBlockSize(currentScale);
            }

            _cachedBlockScale = currentScale;
        }
    }

    #region Normal Tile

    public bool CanTrySkin(ref PlatformData platformData) {
        if (!platformData.canTrySkin || Ball.b.endlessModeCount > 0) {
            return false;
        }

        if (GameController.instance.isUpdateSensitive) {
            return false;
        }

        if (!TrySkinFeature.IsShowTrySkin()) {
            return false;
        }

        return true;
    }

    public Platform SpawnTile(PlatformType platformType, ref PlatformData platformData) {
        Platform platform = null;

        switch (platformType) {
            case PlatformType.MOOD_FULL:
                if (_platformFullTile != null) {
                    platform = _platformFullTile.Spawn(transform);
                } else {
                    Logger.EditorLogError("Platform", "Don't exist platform full tile!!!!");
                    platform = _prefDefaultPlatform.Spawn(transform);
                }

                break;

            case PlatformType.MOOD_SHORT:
                platform = _platformSmallMoodColor.Spawn(transform);
                break;

            case PlatformType.COLORING:
                //platform = platform0Color.Spawn(transform);
                break;

            case PlatformType.COLOR_CHOOSING:
                //platform = platform0Color.Spawn(transform);
                break;

            case PlatformType.LONG_TILE:
                platform = _platformLongTile.Spawn(transform);
                break;

            case PlatformType.STRAIGHT_LONG_TILE:
                platform = platformStraightLongTile.Spawn(transform);
                break;

            case PlatformType.PIPE_TILE:
                if (platformPipeTile == null) {
                    platformPipeTile = Resources.Load<Platform>(string.Format(ResourcePath, "PipeTile"));
                    platformPipeTile.CreatePool(3);
                }

                platform = platformPipeTile.Spawn(transform);
                break;

            case PlatformType.HITCH_HIKE_TILE:
                if (platformHitchHike == null) {
                    platformHitchHike = Resources.Load<Platform>(string.Format(ResourcePath, "HitchhikeTile"));
                    platformHitchHike.CreatePool(2);
                }

                platform = platformHitchHike.Spawn(transform);
                break;

            case PlatformType.BREAKABLE_LONG_TILE:
                if (platformBreakableLongTile == null) {
                    platformBreakableLongTile =
                        Resources.Load<Platform>(string.Format(ResourcePath, "LongTile_Breakable"));
                }

                platform = platformBreakableLongTile.Spawn(transform);
                break;

            case PlatformType.COMBINABLE_LONG_TILE:
            case PlatformType.S_LONG_TILE:
                if (platformCombinableLongTile == null) {
                    platformCombinableLongTile =
                        Resources.Load<Platform>(string.Format(ResourcePath, "LongTile_CombinableHaptic"));
                }

                platform = platformCombinableLongTile.Spawn(transform);
                break;

            default:
                if (platformData.currNoteObj.nodeID == NoteData.NoteIDLastNoteForHuman) { //TH-395
                    if (ThemeManager.IsXmas2021()) {
                        platform = _prefDefaultPlatform.Spawn(transform);
                    } else {
                        platform = _platformEndHuman.Spawn(transform);
                    }
                } else if (IsUsingTimbre && Spawner.s.isMusicalizationType) {
                    NoteData.Timbre timbre = platformData.currNoteObj.timbre;
                    switch (timbre) {
                        case NoteData.Timbre.Rhythmic:
                            platform = _platformRhythmic.Spawn(transform);
                            break;

                        case NoteData.Timbre.Vocal:
                            platform = _platformVocal.Spawn(transform);
                            break;

                        case NoteData.Timbre.Melodic:
                            platform = _platformMelodic.Spawn(transform);
                            break;

                        default:
                            platform = _prefDefaultPlatform.Spawn(transform);
                            break;
                    }
                } else {
                    platform = _prefDefaultPlatform.Spawn(transform);
                }

                break;
        }

        if (platform == null) {
            return null;
        }

        platform.isTileTrySkin = CanTrySkin(ref platformData);
        if (platform.isTileTrySkin) {
            StartCoroutine(ProcessTrySkin());
        }

        if (!platform.isMade) {
            platform.UpdateSize(GetRatio());
            platform.Make();
        }

        platform.SetBlockSize(_cachedBlockScale);
        platform.Reset();

        _blockList.Add(platform);
        return platform;
    }

    private IEnumerator ProcessTrySkin() {
        yield return null;

        int tryBallId = GameItems.instance.tryBallId;
        Dictionary<string, object> param = new Dictionary<string, object> {
            { "item_id", tryBallId },
            { "skin_type", BallManager.itemsHuman.Contains(tryBallId) ? "character" : "ball" },
            { "time_impression", GameController.instance.timePlay },
            { TRACK_NAME.song_name, NotesManager.instance.song.name },
            { "accumulate_song_start", UserProperties.GetPropertyInt(UserProperties.song_start) },
        };
        AnalyticHelper.FireEvent(FIRE_EVENT.skin_gameplay_impression, param);
    }

    public void RecycleTile(Platform bl) {
        if (_blockList.Contains(bl)) {
            _blockList.Remove(bl);
            bl.RecycleFakeTiles();
            bl.Recycle();
        }
    }

    #endregion

    #region Fake Tile

    private Platform SpawnFakeTile(FakeTileStyle style, NoteData.Timbre timbre, int noteID) {
        Platform platform = _prefFakeTile.ContainsKey(timbre)
            ? _prefFakeTile[timbre].Spawn(transform)
            : _prefFakeTile[NoteData.Timbre.None].Spawn(transform);

        platform.SetBlockSize(_cachedBlockScale);
        platform.noteID = noteID;
#if UNITY_EDITOR
            platform.name = $"[Fake-{noteID}] {style}";
#endif
        if (!platform.isMade) {
            platform.UpdateSize(_ratio);
            platform.Make(style);
        }

        if (platform.GetFakeStyleType() != style) {
            platform.MakeCloneTile(style);
        }

        _cloneList.Add(platform);
        return platform;
    }

    public bool RecycleFakeTile(Platform bl) {
        if (_cloneList.Contains(bl)) {
            _cloneList.Remove(bl);
            bl.Recycle();
            return true;
        }

        return false;
    }

    public void AttachCloneGrid2(Platform bl, float[] mapPositions, float flyTime) {
        Vector3 newPosition = bl.transCache.position;
        if (NotesManager.instance.FakeTile_Follow_Pitch) { // support all lines (1,2,3,4,5)
            //Logger.EditorLogError(newPosition.ToString());
            if (newPosition.x < 0) {
                newPosition.x -= mapPositions[0];
            } else {
                newPosition.x += mapPositions[0];
            }
        } else { // support main lines (1,3,5)
            int indexPositionOfPlatform = 0;
            for (int indexPosition = 0; indexPosition < mapPositions.Length; indexPosition++) {
                if (Mathf.Approximately(newPosition.x, mapPositions[indexPosition])) {
                    indexPositionOfPlatform = indexPosition;
                    break;
                }
            }

            if (indexPositionOfPlatform == 0 || indexPositionOfPlatform == 2) {
                newPosition.x = mapPositions[1];
            } else {
                newPosition.x = Random.Range(0, 1) == 0 ? mapPositions[0] : mapPositions[2];
            }
        }

        AddFakeTile(FakeTileStyle.Fake_Normal, bl, newPosition.x, flyTime);
    }

    public Platform AddFakeTile(FakeTileStyle style, Platform bl, float positionX, float flyTime) {
        Vector3 newPosition = bl.transCache.position;
        newPosition.x = positionX;

        Platform fakeTile = SpawnFakeTile(style, bl.timbre, bl.noteID);
        if (fakeTile != null) {
            fakeTile.transCache.position = newPosition;
            fakeTile.gameObject.SetActive(true);
            fakeTile.SetFadeValue(1f);
            fakeTile.FlyEffect(flyTime);

            //set skin
            if (!Spawner.s.onTransition) {
                //nếu không phải đang đổi màu thì áp dụng skin mới luôn (cần viết gọn lại 3 hàm dưới)
                fakeTile.SetSkin(Spawner.s.currentIndexSkin, true); //set sr[1]
                fakeTile.FinishedSwitchColor(Spawner.s.currentIndexSkin); //set sr[0] = sr[1]
                fakeTile.SetSkin(Spawner.s.currentIndexSkin); //set sr[0] if no onTransition
            } else { //nếu đang đổi màu thì set skin cũ trước
                //set sr[0] = skin cũ
                fakeTile.SetSkin(Spawner.s.beforeIndexSkin, false, true);

                //set sr[1] = skin mới rồi đợi quá trình chuyển skin hoàn tất
                fakeTile.SetSkin(Spawner.s.currentIndexSkin, true);
            }

            bl.AddFakeTiles(fakeTile);
        }

        return fakeTile;
    }

    #endregion

    /// <summary>
    /// IsActiveDiamond
    /// </summary>
    /// <returns></returns>
    public bool IsActiveDiamond() {
        foreach (Platform platform in _blockList) {
            if (platform.IsActiveDiamond()) {
                return true;
            }
        }

        return false;
    }

    #region Tile Color

    public Platform AttachColorTileGrid2(Platform platform, float[] mapPositions, PlatformData platformData,
                                         int indexSkinSet, PlatformType platformType) {
        Platform colorTile = SpawnTile(PlatformType.COLORING, ref platformData);
#if UNITY_EDITOR
        colorTile.name = $"{platform.noteID}_{platformType}_2";
#endif
        colorTile.platformType = platformType;
        colorTile.platformData = platformData /*.Clone()*/;

        if (platformType != PlatformType.COLOR_CHOOSING) {
            Vector3 newPosition = GetNearPositionGrid(platform, mapPositions);
            colorTile.transCache.position = newPosition;
        } else {
            Vector3 newPosition = platform.transCache.position;
            newPosition.x = Spawner.s.GetSlot02ColorChoosing();
            colorTile.transCache.position = newPosition; //đã cố gắng set position ở bên ngoài nhưng ko thành công :(
        }

        colorTile.FlyEffect(platformData.flyTime);
        colorTile.SetSkinImmediately(indexSkinSet);

        return colorTile;
    }

    #endregion

    ///  <summary>
    /// Đổi màu toàn bộ tile trong stage color choosing
    ///  </summary>
    ///  <param name="indexSkin"></param>
    ///  <param name="indexNote"></param>
    public void ChangeColorForStageColorChoosing(int indexSkin, int indexNote) {
        for (int index = 0; index < _blockList.Count; index++) {
            Platform platform = _blockList[index];

            //không thay màu của những tile trước đấy, đề phòng trường hợp stage trước cũng là Color Choosing
            if (platform.platformData.currentIndexNote < indexNote) {
                continue;
            }

            PlatformData platformData = platform.platformData;
            if (platformData.stage == Stage.COLOR_CHOOSING && platform.platformType == PlatformType.COLORING) {
                platform.SetSkinImmediately(indexSkin);

                //nếu là tile cuối của stage thì break đề phòng trường hợp có 2 stage Color Choosing liên tiếp
                if (platformData.isEndStage) {
                    break;
                }
            }
        }
    }

    public static Vector3 GetNearPositionGrid(Platform platform, float[] mapPositions) {
        Vector3 newPosition = platform.transCache.position;
        int indexPositionOfPlatform = 0;
        for (int indexPosition = 0; indexPosition < mapPositions.Length; indexPosition++) {
            if (Mathf.Approximately(newPosition.x, mapPositions[indexPosition])) {
                indexPositionOfPlatform = indexPosition;
                break;
            }
        }

        if (indexPositionOfPlatform == 0 || indexPositionOfPlatform == 2) {
            newPosition.x = mapPositions[1];
        } else {
            newPosition.x = Random.Range(0, 1) == 0 ? mapPositions[0] : mapPositions[2];
        }

        return newPosition;
    }

    public Platform GetBlockByNoteID(int noteID) {
        foreach (Platform platform in _blockList) {
            if (platform.noteID == noteID) {
                return platform;
            }
        }

        return null;
    }

    public bool IsFirstTile() {
        if (_blockList == null) {
            return false;
        }

        return _blockList.Count == 1;
    }

    public IEnumerator RecycleAllTile(bool isBack = true) {
        yield return null;

        float deltaZ = isBack ? _deltaZ : -_deltaZ;
        if (_blockList != null) {
            for (int index = 1; index < _blockList.Count; index++) {
                Platform platform = _blockList[index];
                if (platform != null) {
                    platform.Hide((_blockList.Count - 1 - index) * _deltaTime, _moveTime, deltaZ);
                }
            }
        }

        if (_cloneList != null) {
            for (int index = 0; index < _cloneList.Count; index++) {
                Platform platform = _cloneList[index];
                if (platform != null) {
                    platform.Hide(index * _deltaTime, _moveTime, deltaZ);
                }
            }
        }

        int totalPlatformMove = Mathf.Max(_blockList?.Count ?? 0, _cloneList?.Count ?? 0);
        yield return new WaitForSecondsRealtime(_moveTime + _deltaTime * totalPlatformMove);

        _prefDefaultPlatform.RecycleAll();
        _prefDefaultPlatform.RemovePooled();

        if (prefDiamond) {
            prefDiamond.RecycleAll();
            prefDiamond.RemovePooled();
        }

        if (_platformFullTile != null) {
            _platformFullTile.RecycleAll();
            _platformFullTile.RemovePooled();
        }

        if (_platformSmallMoodColor) {
            _platformSmallMoodColor.RecycleAll();
            _platformSmallMoodColor.RemovePooled();
        }

        if (_platformLongTile) {
            _platformLongTile.RecycleAll();
            _platformLongTile.RemovePooled();
        }

        if (IsUsingTimbre) {
            _platformRhythmic.RecycleAll();
            _platformRhythmic.RemovePooled();

            _platformVocal.RecycleAll();
            _platformVocal.RemovePooled();

            _platformMelodic.RecycleAll();
            _platformMelodic.RemovePooled();
        }

        _platformEndHuman.RecycleAll();
        _platformEndHuman.RemovePooled();

        List<NoteData.Timbre> timbres = _prefFakeTile.Keys.ToList();
        foreach (NoteData.Timbre variable in timbres) {
            if (_prefFakeTile.TryGetValue(variable, out Platform timbresa)) {
                timbresa.RecycleAll();
                timbresa.RemovePooled();
            }
        }

        foreach (GameObject origFakeTile in origFakeTiles) {
            origFakeTile.Recycle();
        }

        origFakeTiles.Clear();
    }

    public Platform GetPlatform(NoteData noteData) {
        if (_blockList == null || noteData == null) {
            return null;
        }

        foreach (Platform platform in _blockList) {
            NoteData currNoteObj = platform.platformData.currNoteObj;
            if (Math.Abs(currNoteObj.timeAppear - noteData.timeAppear) < 0.02) {
                return platform;
            }
        }

        return null;
    }

    public Platform GetPlatform(int idNode) {
        if (_blockList == null) {
            return null;
        }

        foreach (Platform platform in _blockList) {
            if (platform.noteID == idNode) {
                return platform;
            }
        }

        return null;
    }

    public bool GetPlatform(int idNode, out Platform result) {
        if (_blockList == null) {
            result = null;
            return false;
        }

        foreach (Platform platform in _blockList) {
            if (platform.noteID == idNode) {
                result = platform;
                return true;
            }
        }

        result = null;
        return false;
    }

    public bool CheckConsecutiveSingleTiles(int idHitNote) {
        if (_blockList == null) {
            return false;
        }

        foreach (Platform platform in _blockList) {
            if (platform.noteID > idHitNote && !platform.IsSingleNote) {
                return false;
            }
        }

        return true;
    }

    private SingleTileBrokenEffect _loadedSingleTileBrokenEffect;

    public SingleTileBrokenEffect GetPlatformBrokenEffectAsset() {
        if (_loadedSingleTileBrokenEffect == null) {
            _loadedSingleTileBrokenEffect = Resources.Load<SingleTileBrokenEffect>("VFX/single_tile_broken_effect");
        }

        return _loadedSingleTileBrokenEffect;
    }

    public void MoveFirstPlatform() {
        for (int index = 1; index < _blockList.Count; index++) {
            Platform platform = _blockList[index];
            if (platform != null) {
                platform.SetActive(false);
                platform.Show((index - 1) * _deltaTime, _moveTime, _deltaZ);
            }
        }
    }

    #region Material Platform

    public void UpdatePlatformMaterial(float pctTime, int currentSkinIndex, int nextSkinIndex) {
        if (materialPlatform != null) {
            materialPlatform.UpdatePlatformMaterial(pctTime, currentSkinIndex, nextSkinIndex);
        }
    }

    public void FinishUpdatePlatformMaterial(int currentSkinIndex) {
        if (materialPlatform != null) {
            materialPlatform.FinishUpdatePlatformMaterial(currentSkinIndex);
        }
    }

    public Material GetPlatformMaterial() {
        return materialPlatform != null ? materialPlatform.GetPlatformMaterial() : null;
    }

    public Material GetFakePlatformMaterial() {
        return materialPlatform != null ? materialPlatform.GetFakePlatformMaterial() : null;
    }

    #endregion

    private float _rangeSpawnTile = 1f;

    public Platform CheckSpawnInTile(float zPosition) {
        foreach (var platform in _blockList) {
            if (platform.IsActiveDiamond())
                continue;
            if (!platform.CanSpawnDiamondOnTile())
                continue;

            if (platform.isSlideTile) {
                //logic riêng
                if (platform.GetLandingPosition().z - _rangeSpawnTile < zPosition &&
                    platform.GetTakeOffPosition().z - _rangeSpawnTile > zPosition) {
                    return platform;
                } else {
                    // Logger.EditorLogError(platform.GetLandingPosition().z.ToString());
                    // Logger.EditorLogError(platform.GetTakeOffPosition().z.ToString());
                    // Logger.EditorLogError(zPosition.ToString());
                }
            } else {
                float offset = platform.platformData.positionZ - zPosition;
                if (offset > -_rangeSpawnTile && offset < _rangeSpawnTile) {
                    return platform;
                }
            }
        }

        return null;
    }

    public Vector3 GetDiamondRoadAt(float positionZ) {
        Platform prev = null;
        float lastPrevZ = int.MinValue;
        Platform next = null;
        float lastNextZ = int.MaxValue;
        foreach (var platform in _blockList) {
            if (platform.platformData.positionZ < positionZ && platform.platformData.positionZ > lastPrevZ) {
                lastPrevZ = platform.platformData.positionZ;
                prev = platform;
            } else if (platform.platformData.positionZ > positionZ && platform.platformData.positionZ < lastNextZ) {
                lastNextZ = platform.platformData.positionZ;
                next = platform;
            }
        }

        if (prev != null) {
            float height = Ball.b.GetJumpHeight(prev.platformData.nextNoteObj.distance);
            if (next != null) {
                return Util.GetMiddlePosition(prev.platformData.positionPlatform, next.platformData.positionPlatform,
                    height, positionZ);
            } else {
                var nextPlatformData = MapManager.instance.GetPlatformData(prev.noteID + 1);
                if (nextPlatformData.currNoteObj == null) {
                    return Vector3.zero;
                } else {
                    nextPlatformData.positionZ = prev.platformData.positionZ +
                                                 nextPlatformData.currNoteObj.distance * Ball.b.GetBalLSpeed();
                    if (nextPlatformData.positionZ > positionZ) {
                        return Util.GetMiddlePosition(prev.platformData.positionPlatform,
                            prev.platformData.positionPlatform +
                            (prev.platformData.nextNoteObj.timeAppear - prev.platformData.currNoteObj.timeAppear) *
                            Ball.b.GetBalLSpeed() * Vector3.forward, height, positionZ);
                    } else {
                        //too far!!!!
                        Logger.EditorLog("Diamond", "too far to calculate");
                        return Vector3.zero;
                    }
                }
            }
        } else {
            if (next != null) {
                Vector3 lastPos = MapManager.instance.GetPlatformData(next.noteID - 1).positionPlatform;
                float height = Ball.b.GetJumpHeight(next.platformData.currNoteObj.distance);
                return Util.GetMiddlePosition(lastPos, next.platformData.positionPlatform, height, positionZ);
            } else {
                return Vector3.zero;
            }
        }
    }
}