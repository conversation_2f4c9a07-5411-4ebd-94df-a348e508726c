using UnityEngine;

public class BallTrailer : MonoBehaviour {
    public           Transform        trailTransCache;
    [SerializeField] ParticleSystem   smokeParticleSystem;
    public           ParticleSystem   trailParticleSystem;
    [SerializeField] SpriteRenderer   halo;

    private ParticleSystem.ColorOverLifetimeModule _colorModule;
    private ParticleSystem.EmissionModule          _emiModule;
    private ParticleSystem.ShapeModule             _shapeModule;
    private ParticleSystem.MainModule              _smokeMainModule;
    private Gradient                               _trailGradient;
    private GradientAlphaKey[]                     _rootGradientAlphaKey;
    private ParticleSystem.MinMaxGradient          _mmGradient;
    private GradientColorKey[]                     _colorKeys;
    private GradientAlphaKey[]                     _alphaKeys;

    [SerializeField] ParticleSystem[] trailParticleSystems;
    private void Awake() {
        Spawner.onBgChange += OnBgChange;
    }

    private void Start() {
        if (RemoteConfigBase.instance.MapSize > 1) {
            halo.transform.localScale *= Ball.b.GetBallSize() / (RemoteConfigBase.instance.MapSize * 1.5f);
            trailParticleSystem.transform.localScale *= RemoteConfigBase.instance.MapSize;
        } else {
            halo.transform.localScale *= Ball.b.GetBallSize() / 1.5f;
        }
    }

    private void OnEnable() {
        GetTrailGradient();
        SetHaloColor(_trailGradient.colorKeys[0].color);

        //UpdateTrailFollowBall(Configuration.GetSelectedBall());
        _currentStartLifeTime = ThemeManager.IsThemePrideMonth() ? 0.15f : 0.3f;
    }

    public void UpdateTrailFollowBall(int ballId) {
        trailParticleSystem.gameObject.SetActive(!Ball.b.isHumanPlayer 
            && !BallManager.instance.IsHalloweenCharacter(ballId) && !BallManager.instance.IsHalloweenBall(ballId));
    }

    public void Bind() {
        _shapeModule = smokeParticleSystem.shape;
        _smokeMainModule = smokeParticleSystem.main;
        _colorModule = trailParticleSystem.colorOverLifetime;
        _emiModule = trailParticleSystem.emission;
        _mmGradient = _colorModule.color;
        _colorKeys = GetTrailGradient().colorKeys;
        _alphaKeys = GetTrailGradient().alphaKeys;
    }

    private void SetHaloColor(Color c) {
        Color haloColor = Color.Lerp(ColorHelper.white, c, 0.25f);
        haloColor.a = 0.5f;
        halo.color = haloColor;
    }

    public void Run() {
        trailTransCache.gameObject.SetActive(true);
        trailTransCache.position = Ball.b.transCache.position;
        trailParticleSystem.Clear();
        trailParticleSystem.Play();
        trailParticleSystems[0].Play();
    }

    public void Stop() {
        trailTransCache.position = Ball.b.transCache.position;
        trailTransCache.gameObject.SetActive(false);
    }

    /// <summary>
    /// Update Trailer Strength
    /// </summary>
    /// <param name="t">Strength of Trailer 0-1 (1 is largest)</param>
    public void UpTrailerStrength(float t) {
            if (gameObject == null) {
                return;
            }

            if (Mathf.Approximately(t, 0)) {
                _emiModule.rateOverDistance = 0;
            }

            _emiModule.rateOverTime = Mathf.Lerp(30, 50, t);

            float temp = 0.1f + t * 0.9f;
            for (byte i = 0; i < _alphaKeys.Length; i++) {
                _alphaKeys[i].alpha = Mathf.Lerp(0, _rootGradientAlphaKey[i].alpha, temp);
            }

            //_mmGradient.gradient.colorKeys = _colorKeys;
            //_mmGradient.gradient.alphaKeys = _alphaKeys;
            _mmGradient.gradient.SetKeys(_colorKeys, _alphaKeys);
            _colorModule.color = _mmGradient;
            _shapeModule.radius = Mathf.Lerp(0.1f, 0.3f, t);
            _smokeMainModule.startLifetime = Mathf.Lerp(0.15f, 0.3f, t);
    }

    public void GameCompletedEffect() {
        _emiModule.rateOverTime = 100;
        for (byte i = 0; i < _alphaKeys.Length; i++) {
            _alphaKeys[i].alpha = 1;
        }

        _mmGradient.gradient.alphaKeys = _alphaKeys;
        _colorModule.color = _mmGradient;
        _shapeModule.radius = 1;
        _smokeMainModule.startLifetime = 1;
    }


    private int   _currentPerfectStep   = 0;
    private float _currentStartLifeTime = 0.3f;
    private bool  _onPlayTrailEffect    = false;

    public void UpdateTrailEffect(int idBall, int perfectCount) {
        if (!RemoteConfig.instance.BallTrailPerfect_Enable || _currentPerfectStep > perfectCount || ThemeManager.IsThemePrideMonth()
            || BallManager.instance.IsHalloweenBall(idBall) || BallManager.instance.IsHalloweenCharacter(idBall)) return;

        trailParticleSystem.Stop();
        trailParticleSystem.Clear();

        if (!_onPlayTrailEffect) {
            trailParticleSystems[0].gameObject.SetActive(true);
            trailParticleSystems[0].Play();
            _onPlayTrailEffect = true;
        }

        for (byte i = 0; i < trailParticleSystems.Length; i++) {
            var module = trailParticleSystems[i].main;
            module.startLifetime = _currentStartLifeTime;
        }

        _currentPerfectStep += RemoteConfig.instance.BallTrailPerfect_PerfectStep;

        _currentStartLifeTime += RemoteConfig.instance.BallTrailPerfect_LifeTimeStep;
        if (_currentStartLifeTime > RemoteConfig.instance.BallTrailPerfect_MaxLifeTime)
            _currentStartLifeTime = RemoteConfig.instance.BallTrailPerfect_MaxLifeTime;
    }
    public void StopTrailEffect() {
        if (!RemoteConfig.instance.BallTrailPerfect_Enable) {
            return;
        }

        _currentStartLifeTime = ThemeManager.IsThemePrideMonth() ? 0.15f : 0.3f;
        _currentPerfectStep = 0;
        _onPlayTrailEffect = false;
        trailParticleSystems[0].gameObject.SetActive(false);
        trailParticleSystems[0].Stop();
        trailParticleSystem.Play();
    }

    private void OnBgChange(byte current, byte next, float duration) {
        UpdateTrailColor();
    }

    private void UpdateTrailColor() {
        if (RemoteConfig.instance.BallTrailPerfect_Enable) {
            Color color = Spawner.s.GetCurrentSkin().tileColorDarker;
            for (byte i = 0; i < trailParticleSystems.Length; i++) {
                var colorModule = trailParticleSystems[i].main;
                color.a = colorModule.startColor.color.a;
                colorModule.startColor = color;
            }
        }
    }

    private Gradient GetTrailGradient() {
        if (_trailGradient == null) {
            int indexGradient = Ball.b.BallId + 1;
            int maxBallGradientLength = CoreData.instance.ballGradient.Length - 1;
            if (indexGradient > maxBallGradientLength) {
#if UNITY_EDITOR
                Debug.LogWarning($"[GetTrailGradient] out of index {indexGradient}/{maxBallGradientLength}");
#endif
                indexGradient = 0;
            }

            _trailGradient = CoreData.instance.ballGradient[indexGradient];
            _rootGradientAlphaKey = _trailGradient.alphaKeys;
        }

        return _trailGradient;
    }
}