using UnityEngine;
using UnityEngine.UI;

public class XNumber : MonoBehaviour {
    [SerializeField] private Sprite[]  numberImages;
    [SerializeField] private Image[]   numeralRow;
    [SerializeField] private Animation effect;

    private bool  _isShowing;
    private float _showTime;

    public void SetNumber(int number) {
        if (number <= 0) {
            gameObject.SetActive(false);
            numeralRow[1].gameObject.SetActive(false);
            numeralRow[2].gameObject.SetActive(false);
        } else {
            _isShowing = true;
            _showTime = 0.7f;
            if (number < 10) {
                numeralRow[0].sprite = numberImages[number];
            } else if (number < 100) {
                numeralRow[0].sprite = numberImages[number / 10];
                numeralRow[1].sprite = numberImages[number % 10];
                if (number == 10) {
                    numeralRow[1].gameObject.SetActive(true);
                }
            } else if (number < 1000) {
                int hundred = number / 100;
                int dozens = (number % 100) / 10;
                numeralRow[0].sprite = numberImages[hundred];
                numeralRow[1].sprite = numberImages[dozens];
                numeralRow[2].sprite = numberImages[number % 10];
                if (number == 100) {
                    numeralRow[2].gameObject.SetActive(true);
                }
            } else { // over range 1000 ->> max 999
                numeralRow[0].sprite = numberImages[9];
                numeralRow[1].sprite = numberImages[9];
                numeralRow[2].sprite = numberImages[9];
            }

            if (!gameObject.activeSelf) {
                gameObject.SetActive(true);
            }

            effect.Rewind();
            effect.Play();
        }
    }

    private void LateUpdate() {
        if (_isShowing) {
            _showTime -= Time.deltaTime;
            if (_showTime < 0) {
                _isShowing = false;
                gameObject.SetActive(false);
            }
        }
    }
}