using System;
using System.Collections;
using System.Collections.Generic;
using Newtonsoft.Json;
using UnityEngine;
using Random = System.Random;

public class TrySkinFeature {
    public static bool IsActive = false;

    private static bool _isInit              = false;
    private static bool _isFirstSessionOfDay = false;
    private static bool _isFirstShow         = true;

    private static byte      _countSongToTrySkin;
    private static byte      _countTrySkinInday;
    private static List<int> _listTriedSkins;


    private static RemoteConfig _remoteConfig;

    private static bool IsEnableFeature() {
        if (_remoteConfig == null || !_remoteConfig.TrySkin_Enable) {
            //Logger.Log("TrySkin", "CheckLogicTrySkin => TrySkin_Enable false");
            return false;
        }

        //bỏ tính năng này với vip
        if (SubscriptionController.IsSubscriptionVip()) {
            return false;
        }

        //khi show quá nhiều lần mà k lần nào unlock
        int maxTimeOffer = _remoteConfig.TrySkin_MaxTimeOffer;
        if (maxTimeOffer > 0 && PlayerPrefs.GetInt(CONFIG_STRING.Key_CountTrySkinAppear, 0) >= maxTimeOffer) {
            return false;
        }

        return true;
    }

    public static void InitData() {
        if (_isInit) return;
        _isInit = true;
        _remoteConfig = RemoteConfig.instance;
        IsActive = IsEnableFeature();
        Logger.EditorLog("TrySkin", $"Init isActive: {IsActive}");
        if (IsActive) {
            DateTime lastDate = Util.GetDateTimeByString(PlayerPrefs.GetString(CONFIG_STRING.Key_TrySkin_Date),
                DateTime.MinValue);
            int dayDiff = (int)(DateTime.Now - lastDate.Date).TotalDays;
            if (dayDiff >= 1) {
                PlayerPrefs.SetString(CONFIG_STRING.Key_TrySkin_Date,
                    DateTime.Now.ToString(CONFIG_STRING.DateTimeFormat));
                SetTrySkinInday(0);
                SetCountSongToTrySkin(0);
                _isFirstSessionOfDay = true;
            } else {
                _isFirstSessionOfDay = false;
                _countSongToTrySkin = (byte)PlayerPrefs.GetInt(CONFIG_STRING.Key_TrySkin_CountSongToTrySkin, 0);
                _countTrySkinInday = (byte)PlayerPrefs.GetInt(CONFIG_STRING.Key_TrySkin_CountTrySkinInDay, 0);
            }

            if (PlayerPrefs.HasKey(CONFIG_STRING.Key_TrySkin_TriedSkins)) {
                try {
                    _listTriedSkins =
                        JsonConvert.DeserializeObject<List<int>>(
                            PlayerPrefs.GetString(CONFIG_STRING.Key_TrySkin_TriedSkins));
                }
                catch (Exception e) {
                    _listTriedSkins = new List<int>();
                }
            } else {
                _listTriedSkins = new List<int>();
            }
        }
    }

    public static void SetCountSongToTrySkin(byte value) {
        _countSongToTrySkin = value;
        PlayerPrefs.SetInt(CONFIG_STRING.Key_TrySkin_CountSongToTrySkin, _countTrySkinInday);
    }

    public static void IncreaseCountSongToTrySkin(byte amount = 1) {
        _countSongToTrySkin += amount;
        PlayerPrefs.SetInt(CONFIG_STRING.Key_TrySkin_CountSongToTrySkin, _countSongToTrySkin);
        Logger.EditorLog("TrySkin", $"Increase song count to try skin: {_countSongToTrySkin}");
    }

    public static void IncreaseTrySkinInDay(byte amount = 1) {
        _isFirstShow = false;
        _countTrySkinInday += amount;
        PlayerPrefs.SetInt(CONFIG_STRING.Key_TrySkin_CountTrySkinInDay, _countTrySkinInday);
    }

    private static void SetTrySkinInday(byte value) {
        _countTrySkinInday = value;
        PlayerPrefs.SetInt(CONFIG_STRING.Key_TrySkin_CountTrySkinInDay, _countTrySkinInday);
    }

    public static bool IsShowTrySkin() {
        if (!IsActive) {
            return false;
        }

        //check song_start
        int countCurrent = (GameController.instance.game == GameStatus.LIVE ? 0 : 1);

        if (_isFirstSessionOfDay) {
            if (_isFirstShow) {
                if (_countSongToTrySkin + countCurrent <= _remoteConfig.TrySkin_FirstSession_FirstShow) {
                    return false;
                }
            } else {
                if (_countSongToTrySkin + countCurrent <= _remoteConfig.TrySkin_FirstSession_NextShow) {
                    return false;
                }
            }
        } else {
            if (_isFirstShow) {
                if (_countSongToTrySkin + countCurrent <= _remoteConfig.TrySkin_NextSession_FirstShow) {
                    return false;
                }
            } else {
                if (_countSongToTrySkin + countCurrent <= _remoteConfig.TrySkin_NextSession_NextShow) {
                    return false;
                }
            }
        }

        //check limit skin tried in a day
        if (_remoteConfig.TrySkin_LimitInDay > 0 && _countTrySkinInday >= _remoteConfig.TrySkin_LimitInDay) {
            //Logger.Log("TrySkin", "CheckLogicTrySkin => TrySkin_LimitInDay false");
            return false;
        }

        if (GetTryBalls().Count <= 0) {
            //Logger.Log("TrySkin", "CheckLogicTrySkin => GetTryBalls().Count <= 0");
            return false;
        }

        return true;
    }

    public static void AddTrySkin(int ballId) {
        Logger.Log("TrySkin", $"Tried skins: {ballId}");
        if (!_remoteConfig.TrySkin_UniqueShow) {
            //dont care if the config is turn off
            return;
        }

        if (_listTriedSkins == null) {
            _listTriedSkins = new List<int>();
        }

        if (_listTriedSkins.Contains(ballId)) return;
        _listTriedSkins.Add(ballId);
        var json = JsonConvert.SerializeObject(_listTriedSkins);
        PlayerPrefs.SetString(CONFIG_STRING.Key_TrySkin_TriedSkins, json);
        Logger.Log("TrySkin", $"Save tried skins: {json}");
    }

    public static List<int> GetTryBalls() {
        List<int> tryBalls = new List<int>();

        string trySkinTypeBall = _remoteConfig.TrySkin_TypeBall;
        if (string.IsNullOrEmpty(trySkinTypeBall)) {
            return tryBalls;
        }

        if (_remoteConfig.TrySkin_SkinIdList != null && _remoteConfig.TrySkin_SkinIdList.Length > 0) {
            foreach (var ballId in _remoteConfig.TrySkin_SkinIdList) {
                var ballConfig = BallManager.instance.GetBallConfig(ballId);
                if (ballConfig != null && !Configuration.IsOpenBall(ballId)) {
                    tryBalls.Add(ballConfig.id);
                }
            }
        } else {
            string[] strings = trySkinTypeBall.Split(',', ';');
            List<UnlockType> unlockTypes = EnumUtil.GetListEnum<UnlockType>(strings);
            List<BallConfig> listBallConfig = BallManager.instance.GetBallConfig(unlockTypes);

            foreach (BallConfig ballConfig in listBallConfig) {
                if (!Configuration.IsOpenBall(ballConfig.id)) {
                    tryBalls.Add(ballConfig.id);
                }
            }
        }

        if (_remoteConfig.TrySkin_UniqueShow) {
            if (_listTriedSkins != null) {
                foreach (var triedBall in _listTriedSkins) {
                    tryBalls.Remove(triedBall);
                }
            }
        }

        return tryBalls;
    }

    public static void CheckOverOfferTrySkin() {
        //Nếu xuất hiện nhiều lần TrySkinPopup mà user không unlock skin thì tắt tính năng này đi
        if (_remoteConfig.TrySkin_MaxTimeOffer > 0 && PlayerPrefs.GetInt(CONFIG_STRING.Key_CountTrySkinAppear, 0) >=
            _remoteConfig.TrySkin_MaxTimeOffer) {
            IsActive = false;
        }
    }

    public static int GetIdTryBall() {
        List<int> idBallToTry = GetTryBalls();
        if (idBallToTry.Count == 0) return -1;
        if (idBallToTry.Count == 1) return idBallToTry[0];
        int idLastTry = PlayerPrefs.GetInt(PlayerPrefsKey.trySkinID, -1);
        if (_remoteConfig.TrySkin_IsRandomSkin) {
            idBallToTry.Remove(idLastTry); // tránh trùng
            return idBallToTry[UnityEngine.Random.Range(0, 9999) % idBallToTry.Count];
        } else {
            return GetNextTrySkin(idLastTry, idBallToTry);
        }
    }

    private static int GetNextTrySkin(int idLastTry, List<int> listID) {
        int totalID = listID.Count;

        //get next skin
        for (int index = 0; index < totalID; index++) {
            if (listID[index] == idLastTry) {
                if (index == totalID - 1) { //last ID
                    return listID[0];
                } else {
                    return listID[index + 1];
                }
            }
        }

        return listID[0];
    }
}