using System;
using UnityEngine;

[Serializable]
public class ThemeColor {
    public int id;
    public TileColor[] tileColors;

    public ThemeColor Convert() {
        for (int i = 0; i < tileColors.Length; i++) {
            tileColors[i].ConvertColor();
        }

        return this;
    }
}

[Serializable]
public struct TileColor {
    public string mainHex;
    public string subHex;
    public string fireHex;
    public string colorName;

    [NonSerialized] public Color main;
    [NonSerialized] public Color sub;
    [NonSerialized] public Color fire;
    [NonSerialized] public ColorType colorType;

    public void ConvertColor() {
        ColorUtility.TryParseHtmlString("#" + mainHex, out main);
        ColorUtility.TryParseHtmlString("#" + subHex, out sub);
        ColorUtility.TryParseHtmlString("#" + fireHex, out fire);

        if (!Enum.TryParse(colorName, out colorType)) {
            colorType = ColorType.Yellow; //default value
        }
    }
}

[Serializable]
public enum ColorType {
    Purple = 0,
    Blue = 1,
    Yellow = 2,
    Green = 3,
    Pink = 4,
}