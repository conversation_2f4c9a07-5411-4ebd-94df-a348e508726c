using UnityEngine;
using Music.ACM;

public class DeathLine : MonoBehaviour {
    [SerializeField, ReadOnly] private bool isDeath = false;

    private void OnEnable() {
        isDeath = false;
    }

    private void OnTriggerEnter(Collider other) {
        if (!isDeath && other.CompareTag(GAMETAG.Ball)) {
            if (Ball.b.isInvincible) {
                Ball.b.TriggerInvincible(isShowHitProtector: true);
            } else {
                Ball.b.FallToDeath();
                isDeath = true;
            }
        }
    }
}