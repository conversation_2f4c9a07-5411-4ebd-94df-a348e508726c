using System.Collections;
using System.Collections.Generic;
using Com.TheFallenGames.OSA.Core.CustomScroller.StandardPopup;
using TileHop.Cores.Pooling;
using UnityEngine;
using UnityEngine.UI;

public class FreeVideo : PopupUI {
    public static FreeVideo     instance;
    protected       LOCATION_NAME location = LOCATION_NAME.FREE_GIFT;

    [Header("Video Bonus")] [SerializeField]
    private StandardPopupScrollerAdapter scrollerAdapter;

    [SerializeField] private OptimizedCellView prefHeader;
    [SerializeField] private OptimizedCellView prefItem;

    [SerializeField] private Sprite ballSprite;
    [SerializeField] private Sprite diamondSprite;
    [SerializeField] private Sprite musicSprite;
    public                   int    currentIndex = -1;

    [SerializeField] private Button           btnClose;
    [SerializeField] private DiamondFlyEffect diamondFlyEffect;
    
    private List<IData> _datas;
    private Transform   _cachedDiamondTargetPosition;

    #region Unity Methods

    private void Awake() {
        instance = this;
        scrollerAdapter.Init(prefHeader.transform as RectTransform, prefItem.transform as RectTransform);
    }

    private void Start() {
        BuildList();
        Configuration.instance.StartCoroutine(SetData());
        btnClose.onClick.AddListener(Close);
    }

    protected override void OnEnable() {
        scrollerAdapter.OnItemVisible += OnScroll_OnItemVisible;
        onFinishOpen += OnFinishOpen;
        base.OnEnable();
        AnalyticHelper.instance.SetCurrentLocation(location);
        SoundManager.PlaySFX_PopupOpen();
    }

    protected override void OnDisable() {
        scrollerAdapter.OnItemVisible -= OnScroll_OnItemVisible;
        onFinishOpen -= OnFinishOpen;
        base.OnDisable();
    }

    #endregion

    #region ScrollerAdapter

    private IEnumerator SetData() {
        while (!scrollerAdapter.IsInitialized) {
            yield return YieldPool.GetWaitForEndOfFrame();
        }

        scrollerAdapter.SetItems(_datas);
    }

    private void OnScroll_OnItemVisible(BaseItemViewsHolder item) {
        if (_datas.Count <= item.ItemIndex)
            return;

        if (item.cellView is FreeVideoItem view && _datas[item.ItemIndex] is FreeVideoItemData itemData) {
            view.SetData(item.ItemIndex, itemData,location.ToString());
        }
    }

    #endregion

    public override bool HandleEventBack() {
        if (isAnimating) return false;
        return base.HandleEventBack();
    }
    private void OnFinishOpen() {
        if (HomeManager.instance)
            HomeManager.instance.HideHome();
    }

    private void BuildList() {
        _datas = new List<IData> { new HeaderData("VIDEO_BONUS") };

        int ordering = 0;

        // Economy System: TH-1442 : Bỏ thưởng gem mỗi 1h tại FreeVideo
        if (!RemoteConfig.instance.Economy_IsEnable) {
            _datas.Add(new FreeVideoItemData() {
                type = FREEVIDEO_TYPE.HOURLY,
                sprite = diamondSprite,
                song = null,
                id = 0
            });
            ordering++;
        }

        if (!RemoteConfig.instance.VideoDiamondAtHome_Enable) {
            _datas.Add(new FreeVideoItemData() {
                type = FREEVIDEO_TYPE.DIAMOND,
                sprite = diamondSprite,
                song = null,
                id = 0
            });
            ordering++;
        }

        //add ball
        List<int> freeActiveBall = BallManager.instance.GetActiveVideoLockedBalls();
        if (freeActiveBall != null) {
            foreach (var item in freeActiveBall) {
                _datas.Add(new FreeVideoItemData() {
                    type = FREEVIDEO_TYPE.BALL,
                    sprite = ballSprite,
                    song = null,
                    id = item
                });
            }

            ordering += freeActiveBall.Count;
        }

        //add song
        int total = 0;
        foreach (Song song in SongManager.instance.songs.Values) {
            if (total > 25) {
                break;
            }

            if (song.savedType == SONGTYPE.VIDEO && song.ads < 2) {
                ordering++;
                total++;
                _datas.Add(new FreeVideoItemData() {
                    type = FREEVIDEO_TYPE.SONG,
                    sprite = musicSprite,
                    song = song,
                    id = 0
                });
                song.ordering = ordering;
            }
        }
    }

    public static void ShowPopUp() {
        if (instance == null) {
            Util.ShowPopUp(PopupName.FreeVideo);
        }
    }

    public override void Close() {
        if (SongList.instance != null) {
            SongList.instance.BuildList();
        }

        if (HomeManager.instance)
            HomeManager.instance.ShowHome();
        base.Close();
        SoundManager.PlaySFX_PopupClose();
    }

    /// <summary>
    /// Kiểm tra có item nào trong FreeVideo hay không -> để show button FreeVideo trong home
    /// </summary>
    /// <returns></returns>
    public static bool HasItem() {
        // Economy System: TH-1442 : Bỏ thưởng gem mỗi 1h tại FreeVideo
        if (!RemoteConfig.instance.Economy_IsEnable) {
            return true;
        }

        if (!RemoteConfig.instance.VideoDiamondAtHome_Enable) {
            return true;
        }

        // Các ball unlock bằng Video
        List<int> ballConfigs = BallManager.instance.GetActiveVideoLockedBalls();
        if (ballConfigs != null && ballConfigs.Count != 0) {
            return true;
        }

        if (!SongManager.instance || SongManager.instance.songs == null) {
            return false;
        }
        
        // Các bài hát unlock bằng video
        foreach (Song song in SongManager.instance.songs.Values) {
            if (song.savedType == SONGTYPE.VIDEO) {
                return true; // at least 1 -> break
            }
        }

        return false;
    }
    
    public void ShowDiamondVfx(Transform startPosition, int amount) {
        if (_cachedDiamondTargetPosition == null) {
            var topBarHome = HomeManager.instance != null ? HomeManager.instance.topBarHome : null;
            if (topBarHome) {
                _cachedDiamondTargetPosition = topBarHome.TxtDiamond.rectTransform;
            }
        }
        
        diamondFlyEffect.AddDiamonds(startPosition, amount, _cachedDiamondTargetPosition);
    }
}