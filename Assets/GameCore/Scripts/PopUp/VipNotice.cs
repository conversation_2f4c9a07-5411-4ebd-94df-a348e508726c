using System;

public class VipNotice : PopupUI {
    public static bool isShowing { get; private set; }
    
    public override bool HandleEventBack() { // K xử lý event back vì nếu thoát trước sẽ k nhận được bonus
        return false;
    }

    protected override void OnEnable() {
        isShowing = true;
        base.OnEnable();
    }

    protected override void OnDisable() {
        isShowing = false;
        base.OnDisable();
    }
    
    public override void Close() {
		base.Close();
        
        //với Popup flow, đã có config thứ tự hiển thị của popup
        //nên không cần tự động show khi close VIP notice
        if (!RemoteConfigBase.instance.HomePopupFlow_IsEnable) {
            SubscriptionController.CheckAndShowBonus();
        }
    }
}
