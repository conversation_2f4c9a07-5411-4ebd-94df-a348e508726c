using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using CielaSpike;
using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;
using Firebase.Extensions;
using InFramework.DevInfo;
using TileHop.Cores.UserProgression;

public partial class DevInfo : PopupUI {
    public const string IS_RESET_KEY = "IsReset";
    
    //~~~~~~~~~~~~~~~~~~ static ~~~~~~~~~~~~~~~~~~
    public static DevInfo instance;
    static        int     _versionClick = 0;

    //~~~~~~~~~~~~~~~~~~ public ~~~~~~~~~~~~~~~~~~
    public Dropdown themeSelection;

    [Header("Components")] [SerializeField]
    private RemoteConfigDev remoteConfigDev;

    [SerializeField] private GameObject passwordForm;
    [SerializeField] private InputField passwordInput;
    [SerializeField] private Button     passwordEnterBtn;
    [SerializeField] private Button     passwordFormCloseBtn;
    [SerializeField] private Text       passwordFormMsgTxt;

    [SerializeField] private Button btnCropSongsTool;
    [SerializeField] private Button btnLogViewerTool;
    [SerializeField] private Text   txtMessage;

    [SerializeField] private Toggle     tgShowFps;
    [SerializeField] private Transform  tfInformation;
    [SerializeField] private Toggle     tgShowPauseGame;
    [SerializeField] private InputField ifForceSkinSet;

    [SerializeField] private GameObject objQueryEvent;
    [SerializeField] private Button     btnQueryEvent;

    [SerializeField] private Button btnLongNotePrototype;
    [SerializeField] private Button btnSpecialTileControl;
    [SerializeField] private Button btnSpecialTileV2;

    [SerializeField] private Button     btnScanInforSong;
    [SerializeField] private GameObject objScanInforSong;

    [SerializeField] private Button btnShowRate;
    [SerializeField] private Button btnShowGemPopup;
    [SerializeField] private Button btnPlayLockedSong;
    [SerializeField] private Button btnUpgradeLevel;

    [SerializeField] private Toggle tgLaunchTestSuite;
    [SerializeField] private Toggle tgSanboxAdverty;
    [SerializeField] private Toggle tgSanboxAnzu;

    [SerializeField] private Button                 btnShowAdId;
    [SerializeField] private Button                 btnShowCustomerInfo;
    [SerializeField] private Button                 btnShowPurchasesData;
    [SerializeField] private Button                 btnShowDevABLab;
    [SerializeField] private RemoteConfigJSONEditor remoteConfigJsonEditor;

    //~~~~~~~~~~~~~~~~~~ private ~~~~~~~~~~~~~~~~~~
    private Transform    _container;
    private List<string> themeList;

    private bool enableContentTool {
        get => Configuration.instance.enableContentTool;
        set => Configuration.instance.enableContentTool = value;
    }

    public static bool isShowTestSuite => PlayerPrefs.GetInt(PlayerPrefsKey.Admin_LaunchTestSuite, 0) == 1;

    public static bool isShowSandboxAdverty => PlayerPrefs.GetInt(PlayerPrefsKey.Admin_SandboxAdverty, 0) == 1;

    public static bool isShowSandboxAnzu => PlayerPrefs.GetInt(PlayerPrefsKey.Admin_SandboxAnzu, 0) == 1;

    #region Unity method

    private void Awake() {
        instance = this;

        AdsManager.instance.HideBanner();
        remoteConfigDev.gameObject.SetActive(false);
        btnLogViewerTool.onClick.AddListener(btnLogViewerToolOnClick);

        passwordEnterBtn.onClick.AddListener(OnPasswordEnterClick);
        passwordFormCloseBtn.onClick.AddListener(OnPasswordFormCloseBtnClick);

        btnLongNotePrototype.onClick.AddListener(OnLongNotePrototypeBtnClick);
        btnSpecialTileControl.onClick.AddListener(OnSpecialTileControlBtnClick);
        btnSpecialTileV2.onClick.AddListener(OnSpecialTileV2BtnClick);

        _versionClick = 0;
        if (Configuration.isAdmin || Application.isEditor) {
            ActiveAdmin();
        } else {
            passwordForm.SetActive(true);
        }

#if UNITY_EDITOR
        if (Application.CanStreamedLevelBeLoaded("CropSongsTool")) {
            btnCropSongsTool.gameObject.SetActive(true);
            btnCropSongsTool.onClick.AddListener(btnCropSongsToolOnClick);

            if (Application.isEditor) {
                Application.runInBackground = true;
            }
        } else {
            btnCropSongsTool.gameObject.SetActive(false);
        }
#else
        btnCropSongsTool.gameObject.SetActive(false);
#endif
        themeList = ThemeManager.supportThemeList.Values.ToList();

        tgShowFps.onValueChanged.AddListener(TgShowFpsOnValueChanged);
        tgShowPauseGame.onValueChanged.AddListener(tgShowPauseGameOnValueChanged);
        ifForceSkinSet.onValueChanged.AddListener(IfForceSkinSetOnValueChanged);

        btnQueryEvent.onClick.AddListener(BtnQueryEventOnClick);
        btnQueryEvent.gameObject.SetActive(RemoteConfigBase.instance.CommunicationSystem_IsOn);
        btnScanInforSong.onClick.AddListener(btnScanInformationSongOnClick);

        btnShowRate.onClick.AddListener(btnShowRateOnClick);
        btnShowGemPopup.onClick.AddListener(btnShowGemPopupOnClick);
        btnPlayLockedSong.onClick.AddListener(btnPlayLockedSongOnClick);
        btnUpgradeLevel.onClick.AddListener(btnUpgradeLevelOnClick);
        tgLaunchTestSuite.onValueChanged.AddListener(TgLaunchTestSuiteOnValueChanged);
        tgSanboxAdverty.onValueChanged.AddListener(TgSanBoxAdvertyOnValueChanged);
        tgSanboxAnzu.onValueChanged.AddListener(TgSanBoxAnzuOnValueChanged);

        btnShowAdId.onClick.AddListener(btnShowAdIdOnClick);
        btnShowCustomerInfo.onClick.AddListener(ShowCustomerInfo);
        btnShowPurchasesData.onClick.AddListener(ShowPurchaseSavedData);
        btnShowDevABLab.onClick.AddListener(btnShowDevABLabOnClick);
    }

    private void Start() {
        _container = transform.Find("Window/Scroll View/Viewport/Content");

        UpdateDebugState(Configuration.instance.isDebug);
        UpdateAutoPlayState(Configuration.instance.isAutoPlay);
        UpdateApprovalProcessState(Configuration.instance.isApprovalProcess);
        UpdateSelectMIDIState(Configuration.instance.isSelectMIDI);
        UpdateLongNotePrototypeState(Configuration.instance.isLongNotePrototype);
        UpdateSpecialTileControlState(Configuration.instance.isSpecialTileControl);
        UpdateSpecialTileV2State(Configuration.instance.isSpecialTileV2);
        enableContentTool = Configuration.instance.enableContentTool;
        UpdateUIContentTool(enableContentTool);
        UpdateLogViewer(Configuration.instance.isEnableLogViewer);
        if (!Configuration.instance.isPartnerBuild) {
            for (int i = 0; i < tfInformation.childCount; i++) {
                GameObject item = tfInformation.GetChild(i).gameObject;
                Text value = item.transform.Find("Value") != null
                    ? item.transform.Find("Value").GetComponent<Text>()
                    : null;

                if (item.transform.Find("Button") != null) {
                    CopyButton(item.transform.Find("Button").GetComponent<Button>(), value);
                }

                if (value != null) {
                    switch (item.name) {
                        case "Firebase":
                            UpdateTokenFirebase(value);

                            break;

                        case "Facebook":
                            value.text = Facebook.Unity.Settings.FacebookSettings.AppId;
                            break;

                        case "Ironsource":
#if UNITY_ANDROID
                            value.text = (AdsManager.instance.adn as AdIronSource)?.androidAppKey;
#elif UNITY_IOS
                            // ReSharper disable once PossibleNullReferenceException
                            value.text = (AdsManager.instance.adn as AdIronSource).iosAppKey;
#endif
                            break;

                        case "Appsflyer":
                            value.text = AppsFlyerInit.instanceAFI.appsFlyerKey;
                            break;

                        case "PushNotification":
                            value.text = PlayerPrefs.GetString("Firebase_Push");
                            break;
                    }
                }
            }
        }

        themeSelection.ClearOptions();
        themeSelection.AddOptions(ThemeManager.supportThemeList.Values.ToList());

        int themeID = RemoteConfigBase.instance.Theme_ForceID;
        if (ThemeManager.supportThemeList.TryGetValue(themeID, out string themeName)) {
            themeSelection.value = themeList.IndexOf(themeName);
        } else {
            Logger.LogError("[DevInfo] Cannot find theme name from theme id: " + themeID);
        }
    }

    protected override void OnEnable() {
        base.OnEnable();

        tgShowFps.isOn = FPSTracker.Instance.IsShowGUI();
        tgShowPauseGame.isOn = Configuration.instance.Admin_IsShowedPause;
        ifForceSkinSet.text = Configuration.instance.Admin_ForceSkinSet.ToString();
        passwordEnterBtn.gameObject.SetActive(true);
        objScanInforSong.SetActive(false);
        tgLaunchTestSuite.isOn = isShowTestSuite;
        tgSanboxAdverty.isOn = isShowSandboxAdverty;
        tgSanboxAnzu.isOn = isShowSandboxAnzu;
    }

    #endregion

    public override bool HandleEventBack() {
        if (objScanInforSong.activeSelf) {
            return false;
        }

        return base.HandleEventBack();
    }

    private void btnShowRateOnClick() {
        this.Close();
        RateUs.ShowReviewCustom();
    }

    private void btnShowGemPopupOnClick() {
        this.Close();
        Util.ShowPopUpCanvasHeight(PopupName.OldUserGemPopup);
    }

    private void btnPlayLockedSongOnClick() {
        var song = SongManager.instance.GetRewardSong();
        song.oneTimePlay = true;
        Util.GoToGamePlay(song, location: LOCATION_NAME.CommunicationSystem.ToString(), isSongClick: true);
    }

    private void btnUpgradeLevelOnClick() {
        if (!UserProgressionController.EnableFeature)
            return;

        UserProgressionController.instanceSafe.ForceLevelUp(1);
    }

    private void btnScanInformationSongOnClick() {
        objScanInforSong.SetActive(true);
        if (Application.isEditor) {
            Application.runInBackground = true;
        }
    }

    private void OnLongNotePrototypeBtnClick() {
        bool isLongNotePrototype = !Configuration.instance.isLongNotePrototype;
        Configuration.instance.SetLongNotePrototype(isLongNotePrototype);
        SoundManager.PlayGameButton();
        UpdateLongNotePrototypeState(isLongNotePrototype);
    }

    private void OnSpecialTileControlBtnClick() {
        bool enable = !Configuration.instance.isSpecialTileControl;
        Configuration.instance.SetSpecialTileControl(enable);
        SoundManager.PlayGameButton();
        UpdateSpecialTileControlState(enable);
    }
    private void OnSpecialTileV2BtnClick() {
        bool enable = !Configuration.instance.isSpecialTileV2;
        Configuration.instance.SetSpecialTileV2(enable);
        SoundManager.PlayGameButton();
        UpdateSpecialTileV2State(enable);
    }

    private void BtnQueryEventOnClick() {
        if (objQueryEvent != null) {
            objQueryEvent.SetActive(true);
        }
    }

    private void OnPasswordEnterClick() {
        string adminPassword = RemoteConfig.instance.Admin_Password.Trim();
        string userText = passwordInput.text.Trim();
        if (Compare(userText, adminPassword)) {
            PlayerPrefs.SetString(CONFIG_STRING.PREF_ADMIN_PASSWORD_KEY, userText);
            ActiveAdmin();
        } else {
            passwordEnterBtn.gameObject.SetActive(false); // tắt btn Enter đi -> tránh spam
            passwordFormMsgTxt.gameObject.SetActive(true); // show error
            _versionClick = 0; //reset counting
            DOVirtual.DelayedCall(1f, () => { this.Close(); }); // auto close
        }
    }

    private void ActiveAdmin() {
        AnalyticHelper.FireString(CONFIG_STRING.IsAdmin);
        Configuration.instance.EnableAdmin(true);
        passwordForm.SetActive(false);
    }

    private static bool Compare(string userText, string md5Hash) {
        if (string.IsNullOrWhiteSpace(userText))
            return false;
        if (string.IsNullOrWhiteSpace(md5Hash))
            return false;

        string md5Str = Util.MD5Hash(userText);
        return md5Str.Equals(md5Hash);
    }

    public static bool CheckVersionClick() {
        if (_versionClick > 20 || Configuration.isAdmin || Application.isEditor) {
            if (instance == null) {
                GameObject popup = Util.ShowPopUp(PopupName.DevInfo);
                instance = popup.GetComponent<DevInfo>();
                return true;
            }
        } else {
            _versionClick++;
        }

        return false;
    }

    private void OnPasswordFormCloseBtnClick() {
        base.Close();
    }

    public void ThemeSelection_Change(int index) {
        string themeName = themeList[index];
        int themeId = ThemeManager.supportThemeList.FirstOrDefault(x => x.Value == themeName).Key;
        Configuration.instance.SetForceTheme(themeId);
    }

    private void CopyButton(Button button, Text value) {
        button.onClick.AddListener(() => {
            SoundManager.PlayGameButton();
            GUIUtility.systemCopyBuffer = value.text;
        });
    }

    public void ResetGame() {
        SoundManager.PlayGameButton();

        //Delete PlayerPrefs except for firebase id
        string firebaseInstanceId = PlayerPrefs.GetString(PlayerPrefsKey.FirebaseInstallationsToken);
        string conditionDataUrlId = PlayerPrefs.GetString(CONDITIONS_DATA_FILE_URL_ID, string.Empty);
        string appliedConditions = RemoteConfigBase.instance.enableRemoteSync
            ? string.Empty
            : PlayerPrefs.GetString(APPLIED_CONDITIONS_KEY, string.Empty);
            
        PlayerPrefs.DeleteAll();
        
        ResetPersistentData();
        DeleteCache();

        PlayerPrefs.SetInt(IS_RESET_KEY, 1);
        PlayerPrefs.SetInt(PlayerPrefsKey.IsGetNewConfig, 1);
        PlayerPrefs.SetString(PlayerPrefsKey.FirebaseInstallationsToken, firebaseInstanceId);
        
        if (!string.IsNullOrEmpty(conditionDataUrlId)) {
            PlayerPrefs.SetString(CONDITIONS_DATA_FILE_URL_ID, conditionDataUrlId);
        }

        if (!string.IsNullOrEmpty(appliedConditions)) {
            PlayerPrefs.SetString(APPLIED_CONDITIONS_KEY, appliedConditions);
        }

        if (Application.isEditor) {
#if UNITY_EDITOR
            UnityEditor.EditorApplication.isPlaying = false;
#endif
        } else {
            Application.Quit();
        }
    }

    private void DeleteCache() {
        string path = Application.temporaryCachePath;

        DirectoryInfo di = new DirectoryInfo(path);

        foreach (FileInfo file in di.GetFiles()) {
            file.Delete();
        }

        foreach (DirectoryInfo dir in di.GetDirectories()) {
            SetAttributesNormal(dir);
            dir.Delete(true);
        }

        AssetBundleManager.ClearCache();
    }

    private void SetAttributesNormal(DirectoryInfo dir) {
        foreach (var subDir in dir.GetDirectories()) {
            SetAttributesNormal(subDir);
            subDir.Attributes = FileAttributes.Normal;
        }

        foreach (var file in dir.GetFiles()) {
            file.Attributes = FileAttributes.Normal;
        }

        dir.Attributes = FileAttributes.Normal;
    }

    private void ResetPersistentData() {
        string pathApp = Application.persistentDataPath;
        DirectoryInfo d = new DirectoryInfo(pathApp);

        FileInfo[] fileInfos = d.GetFiles();
        DirectoryInfo[] directoryInfos = d.GetDirectories();

        if (fileInfos.Length == 0 && directoryInfos.Length == 0) {
        } else {
            foreach (DirectoryInfo t in directoryInfos) {
                try {
                    SetAttributesNormal(t);
                    t.Delete(true);
                } catch (Exception e) {
                    Debug.LogError(e.Message);
                }
            }

            foreach (FileInfo t in fileInfos) {
                if (t.Name != "LocalConfig.json") {
                    try {
                        t.Attributes = FileAttributes.Normal;
                        t.Delete();
                    } catch (Exception e) {
                        if (Application.isEditor) {
                            if (t.Name == "PI_DB") {
                                Debug.LogWarning(e.Message);
                            } else {
                                Debug.LogError(e.Message);
                            }
                        }
                    }
                }
            }

            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ 003 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            string pathCachedRemoteConfigData = Path.Combine(Directory.GetCurrentDirectory(), "remote_config_data");
            if (File.Exists(pathCachedRemoteConfigData)) {
                File.SetAttributes(pathCachedRemoteConfigData, FileAttributes.Normal);
                File.Delete(pathCachedRemoteConfigData);
            }

            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ 004 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            //delete folder DownloadedData (ACm use this folder)
            //DownloadedData
            string pathDownloadedData = Path.Combine(Directory.GetCurrentDirectory(), "DownloadedData");
            if (Directory.Exists(pathDownloadedData)) {
                var dir = new DirectoryInfo(pathDownloadedData);
                SetAttributesNormal(dir);
                dir.Delete(true);
            }

            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ 005 EditorCache ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            //delete folder EditorCache (ACMv3 use this folder)
            string path = Path.Combine(Directory.GetCurrentDirectory(), "EditorCache");
            if (Directory.Exists(path)) {
                var dir = new DirectoryInfo(path);
                SetAttributesNormal(dir);
                dir.Delete(true);
            }
        }
    }

    public void DebugMode_Click() {
        bool isDebug = !Configuration.instance.isDebug;
        Configuration.instance.SetDebugMode(isDebug);
        SoundManager.PlayGameButton();
        UpdateDebugState(isDebug);

        if (Configuration.instance.isDebug) {
            Configuration.UpdateDiamond(1000 - Configuration.instance.GetDiamonds(), "dev_info");
        }
    }

    public void AutoPlay_Click() {
        bool isAutoPlay = !Configuration.instance.isAutoPlay;
        Configuration.instance.SetAutoPlay(isAutoPlay);
        SoundManager.PlayGameButton();
        UpdateAutoPlayState(isAutoPlay);
    }

    public void ApprovalProcess_Click() {
        bool isApprovalProcess = !Configuration.instance.isApprovalProcess;
        Configuration.instance.SetApprovalProcess(isApprovalProcess);
        SoundManager.PlayGameButton();
        UpdateApprovalProcessState(isApprovalProcess);
    }

    public void SelectMIDI_Click() {
        bool isSelectMIDI = !Configuration.instance.isSelectMIDI;
        Configuration.instance.SetSelectMIDI(isSelectMIDI);
        SoundManager.PlayGameButton();
        UpdateSelectMIDIState(isSelectMIDI);
    }

    public void ContentTool_Click() {
        StartCoroutine(IEContentTool_Click());
    }

    private IEnumerator IEContentTool_Click() {
        enableContentTool = !enableContentTool;
        Configuration.instance.SetContentTool(enableContentTool);
        if (enableContentTool) {
            SoundManager.PlayGameButton();
            Configuration.instance.EnableAdmin(true);
            DownloadManager.instanceSafe.LoadSongListWithContentTool();
        } else {
            yield return this.StartCoroutine(SongManager.instance.IEProcessSongList());

            if (SongList.instance != null) {
                SongList.instance.BuildList(true);
            }
        }

        UpdateUIContentTool(enableContentTool);

        yield return null;
    }

    void UpdateDebugState(bool enable) {
        _container.Find("Menu/DebugMode/Disable").gameObject.SetActive(enable);
        _container.Find("Menu/DebugMode/Enable").gameObject.SetActive(!enable);
    }

    void UpdateUIContentTool(bool enable) {
        _container.Find("Menu/ContentTool/Disable").gameObject.SetActive(enable);
        _container.Find("Menu/ContentTool/Enable").gameObject.SetActive(!enable);
    }

    void UpdateAutoPlayState(bool enable) {
        _container.Find("Menu/AutoPlay/Disable").gameObject.SetActive(enable);
        _container.Find("Menu/AutoPlay/Enable").gameObject.SetActive(!enable);
    }

    void UpdateApprovalProcessState(bool enable) {
        _container.Find("Menu/Approval_Process/Disable").gameObject.SetActive(enable);
        _container.Find("Menu/Approval_Process/Enable").gameObject.SetActive(!enable);
    }

    void UpdateSelectMIDIState(bool enable) {
        _container.Find("Menu/SelectMIDIVersion/Disable").gameObject.SetActive(enable);
        _container.Find("Menu/SelectMIDIVersion/Enable").gameObject.SetActive(!enable);
    }

    void UpdateLongNotePrototypeState(bool enable) {
        btnLongNotePrototype.transform.Find("Disable").gameObject.SetActive(enable);
        btnLongNotePrototype.transform.Find("Enable").gameObject.SetActive(!enable);
    }

    void UpdateSpecialTileControlState(bool enable) {
        btnSpecialTileControl.transform.Find("Disable").gameObject.SetActive(enable);
        btnSpecialTileControl.transform.Find("Enable").gameObject.SetActive(!enable);
    }
    void UpdateSpecialTileV2State(bool enable) {
        btnSpecialTileV2.transform.Find("Disable").gameObject.SetActive(enable);
        btnSpecialTileV2.transform.Find("Enable").gameObject.SetActive(!enable);
    }

    public void ShowFS_Click() {
        AdsManager.instance.ShowInterstitial(LOCATION_NAME.devInfo.ToString(),
            () => { Debug.Log("Show ads completed"); });
    }

    public void ShowSetting_Click() {
        if (!Configuration.instance.isPartnerBuild) {
            SoundManager.PlayGameButton();
            remoteConfigDev.Show();
        }
    }

    public void CloseDetails() {
        SoundManager.PlayGameButton();
        transform.Find("Window/Details").gameObject.SetActive(false);
    }

    public override void Close() {
        if (Util.IsHomeScene()) {
            HomeManager.instance.ShowHome();
        }

        base.Close();
    }

    private void btnLogViewerToolOnClick() {
#if ENABLE_LOGS
        Configuration.instance.ChangeEnableLogViewer();
        UpdateLogViewer(true);
        Configuration.instance.ShowLogViewer(Configuration.instance.isEnableLogViewer);
#else
        txtMessage.gameObject.SetActive(true);
        txtMessage.text = "Please enable feature ENABLE_LOGS from Unity before use!";
        DOVirtual.DelayedCall(3, () => { txtMessage.gameObject.SetActive(false); });
#endif
    }

    private void UpdateLogViewer(bool enable) {
        btnLogViewerTool.transform.Find("Disable").gameObject.SetActive(enable);
        btnLogViewerTool.transform.Find("Enable").gameObject.SetActive(!enable);
    }

#if UNITY_EDITOR
    private void btnCropSongsToolOnClick() {
        SceneManager.LoadScene("CropSongsTool");
    }
#endif

    private void TgShowFpsOnValueChanged(bool value) {
        if (value) {
            FPSTracker.Instance.ShowTracker();
        } else {
            FPSTracker.Instance.HideTracker();
        }
    }

    private void tgShowPauseGameOnValueChanged(bool value) {
        Configuration.instance.Admin_IsShowedPause = value;
    }

    private void IfForceSkinSetOnValueChanged(string value) {
        if (int.TryParse(value, out int skinID)) {
            Configuration.instance.Admin_ForceSkinSet = skinID;
        }
    }

    private static void UpdateTokenFirebase(Text text) {
        string token = PlayerPrefs.GetString(PlayerPrefsKey.FirebaseInstallationsToken);
        if (string.IsNullOrEmpty(token)) {
            Firebase.Installations.FirebaseInstallations.DefaultInstance.GetTokenAsync(true).ContinueWithOnMainThread(
                task => {
                    if (!(task.IsCanceled || task.IsFaulted) && task.IsCompleted) {
                        PlayerPrefs.SetString(PlayerPrefsKey.FirebaseInstallationsToken, task.Result);
                        text.text = task.Result;
                        Logger.Log($"[FirebaseInstallationsToken] {task.Result}");
                    }
                });
        } else {
            text.text = token;
            Logger.Log($"[FirebaseInstallationsToken] {token}");
        }
    }

    private void TgLaunchTestSuiteOnValueChanged(bool arg0) {
        PlayerPrefs.SetInt(PlayerPrefsKey.Admin_LaunchTestSuite, arg0 ? 1 : 0);
        if (arg0) {
            IronSource.Agent.launchTestSuite();
        }
    }

    private void TgSanBoxAdvertyOnValueChanged(bool arg0) {
        PlayerPrefs.SetInt(PlayerPrefsKey.Admin_SandboxAdverty, arg0 ? 1 : 0);
    }

    private void TgSanBoxAnzuOnValueChanged(bool arg0) {
        PlayerPrefs.SetInt(PlayerPrefsKey.Admin_SandboxAnzu, arg0 ? 1 : 0);
    }

    private void btnShowAdIdOnClick() {
        string logAd = AdsManager.instance.GetLogAd();
        remoteConfigJsonEditor.gameObject.SetActive(true);
        remoteConfigJsonEditor.InitJsonEditor(logAd);
    }

    private bool _isProcessingCustomerInfo;

    private void ShowCustomerInfo() {
        remoteConfigJsonEditor.gameObject.SetActive(true);
        remoteConfigJsonEditor.InitJsonEditor("Processing ...");

        if (_isProcessingCustomerInfo) {
            return;
        }

        _isProcessingCustomerInfo = true;
        RevenueCatPurchases.instance.GetCustomerInfo((customerInfo, error) => {
            _isProcessingCustomerInfo = false;
            if (error == null && remoteConfigJsonEditor.gameObject.activeSelf) {
                var info = customerInfo.ToString();
                remoteConfigJsonEditor.InitJsonEditor(info);
            }
        });
    }

    private void ShowPurchaseSavedData() {
        var data = NonSubPendingPurchaseManager.Log();
        remoteConfigJsonEditor.gameObject.SetActive(true);
        remoteConfigJsonEditor.InitJsonEditor(data);
    }

    private void btnShowDevABLabOnClick() {
        //ABLabAdapter.instance.OnDevModeButtonClicked();
    }
}