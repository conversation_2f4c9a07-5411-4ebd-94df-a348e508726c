using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
public class TouchTip : MonoBehaviour {
	public Image overlay;
	public Text title;
	public Text text;
	// Use this for initialization
	//public static  TouchTip instance ;
	//void Awake(){
	//	instance = this;
	//}
	void OnEnable () {
		overlay.canvasRenderer.SetAlpha (0);
		title.canvasRenderer.SetAlpha (0);
		text.canvasRenderer.SetAlpha (0);

		overlay.CrossFadeAlpha (1,2,false);
		title.CrossFadeAlpha (1,2,false);
		text.CrossFadeAlpha (1,2,false);
		StartCoroutine (FadeCompleted ());
	}

	IEnumerator FadeCompleted()
	{
		yield return new WaitForSeconds(3f);
		//UIController.ui.gameover.Active();
		GameController.instance.ShowResultUi(isCheckTrySkin: true, isCheckSongOfDay: true);
		Destroy (gameObject);
	}
}
