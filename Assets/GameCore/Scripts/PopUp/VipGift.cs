using System;
using System.Collections;
using TileHop.EconomySystem;
using UnityEngine;
using UnityEngine.UI;

public class VipGift : PopupUI {
    [SerializeField] private Text diamondBonus;
    [SerializeField] private Text titleText;

    private int    _diamond;
    private string _title;
    private string _type;
    private Action _onCompleted;

    private void Start() {
        StartCoroutine(IEDiamondBonusEffect());
    }

    public override bool HandleEventBack() { // K xử lý event back vì nếu thoát trước sẽ k nhận được bonus
        return false;
    }

    public void SetValue(int diamond, string title, string type, Action onCompleted = null) {
        this._diamond = diamond;
        this._title = title;
        this._type = type;
        this._onCompleted = onCompleted;

        titleText.text = _title;
        diamondBonus.text = $"+{diamond}";
    }

    private IEnumerator IEDiamondBonusEffect() {
        yield return new WaitForSeconds(1f);

        diamondBonus.gameObject.SetActive(true);
        Configuration.UpdateDiamond(_diamond, _type);
        if (this._type == "Subscription") {
            // Bonus gem daily login after active VIP
            EconomyTrackingEvents.TrackMissionCollect(this._type, _diamond, "login");
        } else {
            // Bonus gem when complete survey
            EconomyTrackingEvents.TrackMissionCollect(this._type, _diamond, "other");
        }

        yield return new WaitForSeconds(2f);

        Close();
        _onCompleted?.Invoke();
    }
}