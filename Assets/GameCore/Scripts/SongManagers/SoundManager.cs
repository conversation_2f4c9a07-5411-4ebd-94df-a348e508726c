using System.Collections.Generic;
using UnityEngine;
using TileHop.Sound;

public class SoundManager : Singleton<SoundManager> {
    // Use this for initialization
    private Dictionary<SFXType, string> dictSFXSounds;
    private int                         channel    = 0;
    private int                         maxChannel = 4;

    private RemoteConfig remoteConfig => RemoteConfigBase.instance;

    private void Start() {
        dictSFXSounds = new Dictionary<SFXType, string>() {
            { SFXType.gameButton, "click" },
            { SFXType.gameCompleted, "gameCompleted" },
            { SFXType.star, "star" },
            { SFXType.die, "die" },
            { SFXType.select, "select" },
            { SFXType.coins, "coins" },
            { SFXType.impactExtend, "impactExtend" },
            { SFXType.buy, "buy" },
            { SFXType.checkPoint, "checkPoint" },
            { SFXType.diamond, "diamond" },

            //TH-4187
            { SFXType.gameplay_strong_note, "GamPlay_StrongNote"},
			{ SFXType.gameplay_moodchange, "GamePlay_MoodChange"},
			{ SFXType.scroll_tick, "Slide_tick" },
			{ SFXType.list_menu_open, "ListMenu_open" },
			{ SFXType.list_menu_close, "ListMenu_close" },
			{ SFXType.gem_purchased, "Shop_BuyDiamon_success"},
			{ SFXType.shop_ball_tap, "Shop_tap"},
			{ SFXType.shop_ball_purchased, "Shop_Button_BuyGem" },
			{ SFXType.free_gift_collect, "FreeGift_collect" },
			{ SFXType.shop_select_char, "Shop_Select_char" },
			{ SFXType.song_card_preview, "PlayPreview_stop" },

            //TH-4577
            { SFXType.booster_turnoff, "SFX_Boot_TurnOff" },
            { SFXType.booster_shield_base_active, "SFX_Boot_Shield_Base_Active"},
            { SFXType.booster_tiletidy_active, "SFX_Boot_TileTidy_Active"},
            { SFXType.booster_magnet_active, "SFX_Boot_Magnet_Active"}
		};

        Util.WaitRemoteConfigDone(UpdateAssets, true);

        AudioDeviceDetector.Instance.RegisterAudioDeviceEvents();
    }

    private void UpdateAssets() {
        if (remoteConfig.PlayNewSFX_Enable) {
            dictSFXSounds.Add(SFXType.start_song, "start_song");
            dictSFXSounds.Add(SFXType.ingame_star, "ingame_star");
            dictSFXSounds.Add(SFXType.revive_bgm, "revive_bgm");
            dictSFXSounds.Add(SFXType.revive_countdown, "revive_countdown");
            dictSFXSounds.Add(SFXType.revived, "revived");
            dictSFXSounds.Add(SFXType.result_star, "result_star");
            dictSFXSounds.Add(SFXType.result_gem, "result_gem");
            dictSFXSounds.Add(SFXType.result_bgm, "result_bgm");
            dictSFXSounds.Add(SFXType.popup_open, "popup_open");
            dictSFXSounds.Add(SFXType.popup_close, "popup_close");
            dictSFXSounds.Add(SFXType.button_tap, "button_tap");
            dictSFXSounds.Add(SFXType.gem_count, "gem_count");
            dictSFXSounds.Add(SFXType.open_shop, "open_shop");
            dictSFXSounds.Add(SFXType.character_select, "character_select");
        }

        if (remoteConfig.PlayNewSFX_UP_Enable) { //TH-3985
            dictSFXSounds.Add(SFXType.result_tile_collect, "anim_resultscrn_collect_2024SP");
            dictSFXSounds.Add(SFXType.result_reward_popup, "btn_resultscrn_rewardpopup_2024SP");
            dictSFXSounds.Add(SFXType.home_tile_collect, "anim_mainscrn_tilecollect_2024SP");
            dictSFXSounds.Add(SFXType.home_pack_unlock, "anim_mainscrn_songpackunlocked_2024SP");
            dictSFXSounds.Add(SFXType.home_onboarding, "anim_mainscrn_onboardingpopup_2024SP");
            dictSFXSounds.Add(SFXType.ingame_diamond_collect, "anim_actionphrase_diamondcollect_2024SP");
        }

        if (remoteConfig.StarsJourney_IsEnable) { //TH-4486
            dictSFXSounds.Add(SFXType.result_disk_popin, "Result_Disk_PopIn");
            dictSFXSounds.Add(SFXType.result_small_star_popin, "Result_SmallStar_PopIn");
            dictSFXSounds.Add(SFXType.home_sj_star_flying_to, "Home_SJ_StarFlyingTo");
            dictSFXSounds.Add(SFXType.home_sj_new_level, "Home_SJ_NewLevel");
            dictSFXSounds.Add(SFXType.sj_feature_progress_line_slide, "SJFeature_ProgressLine_Slide");
            dictSFXSounds.Add(SFXType.sj_feature_unlock_reward_extra, "SJFeature_UnlockRewadExtra");
            dictSFXSounds.Add(SFXType.home_small_star, "Home_SmallStar_Pop");
            dictSFXSounds.Add(SFXType.popup_slide, "Page_slide");
            dictSFXSounds.Add(SFXType.sj_gift_open, "GiftBox_InOpen");
            dictSFXSounds.Add(SFXType.sj_gift_item_pop, "GiftBox_ItemPop");
        }
    }

    /// <summary>
    /// Only support ios for silent mode
    /// </summary>
    public void ReloadAsset() {
        if (Application.platform == RuntimePlatform.IPhonePlayer) {
            AudioPlayer.ClearAudio();
        }
    }

    private void PlaySingleSound(string soundName, float soundVolume = 0.2f, bool loop = false) {
        if (Configuration.instance == null || !Configuration.instance.SoundIsOn()) {
            return;
        }

        StartCoroutine(AudioPlayer.Play(channel, soundName, soundVolume, loop));
        channel++;
        if (channel == maxChannel) {
            channel = 0;
        }
    }

    private static void StopSfx(SFXType key) {
        if (!isInstanced)
            return;

        if (instance.dictSFXSounds.ContainsKey(key)) {
            AudioPlayer.Stop(instance.dictSFXSounds[key]);
        } else {
            Logger.LogError("dictSFXSounds don't ContainsKey " + key);
        }
    }

    public static void PlayGameButton(float volume = 0.5f) {
        if (!isInstanced) {
            return;
        }

        var key = instance.remoteConfig.PlayNewSFX_Enable ? SFXType.button_tap : SFXType.gameButton;
        if (instance.dictSFXSounds.ContainsKey(key)) {
            instance.PlaySingleSound(instance.dictSFXSounds[key], volume);
        } else {
            Logger.LogError("dictSFXSounds don't ContainsKey " + key);
        }
    }

    public void PlaySelect() {
        PlaySingleSound(dictSFXSounds[SFXType.select]);
    }

    public void PlayGameCompleted() {
        PlaySingleSound(dictSFXSounds[SFXType.gameCompleted], 0.3f);
    }

    public void PlayDie() {
        PlaySingleSound(dictSFXSounds[SFXType.die]);
    }

    public void PlayStar(float volume = 0.5f) {
        var key = remoteConfig.PlayNewSFX_Enable ? SFXType.result_star : SFXType.star;
        if (dictSFXSounds.ContainsKey(key)) {
            PlaySingleSound(dictSFXSounds[key], volume);
        } else {
            Logger.LogError("dictSFXSounds don't ContainsKey " + key);
        }
    }

    public static void PlayCheckPoint(float volume = 0.05f) {
        if (!isInstanced)
            return;
        if (instance.remoteConfig == null)
            return;
        if (instance.remoteConfig.PlayNewSFX_Enable)
            return;

        instance.PlaySingleSound(instance.dictSFXSounds[SFXType.checkPoint], volume);
    }

    public void PlayCollect() {
        PlaySingleSound(dictSFXSounds[SFXType.coins]);
    }

    public static void PlayCoins(float volume = 0.35f) {
        if (!isInstanced) {
            return;
        }

        instance.PlaySingleSound(instance.dictSFXSounds[SFXType.coins], volume);
    }

    public void PlayImpactExtend() {
        PlaySingleSound(dictSFXSounds[SFXType.impactExtend], 0.02f);
    }

    public static void PlayBuy() {
        if (!isInstanced) {
            return;
        }

        instance.PlaySingleSound(instance.dictSFXSounds[SFXType.buy]);
    }

    public static void PlayDiamond() {
        if (!isInstanced)
            return;

        var key = SFXType.diamond;
        if (instance.dictSFXSounds.ContainsKey(key)) {
            instance.PlaySingleSound(instance.dictSFXSounds[key]);
        } else {
            Logger.LogError("dictSFXSounds don't ContainsKey " + key);
        }
    }

    private void OnApplicationPause(bool pauseStatus) {
        if (pauseStatus) {
            AudioPlayer.Pause();
        } else {
            AudioPlayer.Resume();
        }
    }

    //Play New SFX
    public static void PlayStartSong(float volume = 0.5f) {
        if (!isInstanced)
            return;
        if (instance.remoteConfig == null)
            return;
        if (!instance.remoteConfig.PlayNewSFX_Enable)
            return;

        var key = SFXType.start_song;
        if (instance.dictSFXSounds.ContainsKey(key)) {
            instance.PlaySingleSound(instance.dictSFXSounds[key], volume);
        } else {
            Logger.LogError("dictSFXSounds don't ContainsKey " + key);
        }
    }

    public void PlayIngame_Star(float volume = 1f) {
        if (!remoteConfig.PlayNewSFX_Enable)
            return;

        var key = SFXType.ingame_star;
        if (dictSFXSounds.ContainsKey(key)) {
            PlaySingleSound(dictSFXSounds[key], volume);
        } else {
            Logger.LogError("dictSFXSounds don't ContainsKey " + key);
        }
    }

    public static void PlayRevive_BG(float volume = 0.5f) {
        if (!isInstanced)
            return;
        if (instance.remoteConfig == null)
            return;
        if (!instance.remoteConfig.PlayNewSFX_Enable)
            return;

        var key = SFXType.revive_bgm;
        if (instance.dictSFXSounds.ContainsKey(key)) {
            instance.PlaySingleSound(instance.dictSFXSounds[key], volume);
            StopSfx(SFXType.start_song);
        } else {
            Logger.LogError("dictSFXSounds don't ContainsKey " + key);
        }
    }

    public static void StopRevive_BG() {
        if (!isInstanced)
            return;
        if (instance.remoteConfig == null)
            return;
        if (!instance.remoteConfig.PlayNewSFX_Enable)
            return;

        var key = SFXType.revive_bgm;
        StopSfx(key);
    }

    public void PlayRevive_CountDown(float volume = 0.5f) {
        if (!remoteConfig.PlayNewSFX_Enable)
            return;

        var key = SFXType.revive_countdown;
        if (dictSFXSounds.ContainsKey(key)) {
            PlaySingleSound(dictSFXSounds[key], volume);
        } else {
            Logger.LogError("dictSFXSounds don't ContainsKey " + key);
        }
    }

    public static void PlayRevived(float volume = 0.5f) {
        if (!isInstanced)
            return;
        if (instance.remoteConfig == null)
            return;
        if (!instance.remoteConfig.PlayNewSFX_Enable)
            return;

        var key = SFXType.revived;
        TryPlayVFX(key, volume);
    }

    public static void PlayResultBG(float volume = 0.7f) {
        if (!isInstanced)
            return;
        if (instance.remoteConfig == null)
            return;
        if (!instance.remoteConfig.PlayNewSFX_Enable)
            return;

        var key = SFXType.result_bgm;
        if (instance.dictSFXSounds.ContainsKey(key)) {
            instance.PlaySingleSound(instance.dictSFXSounds[key], volume);
        } else {
            Logger.LogError("dictSFXSounds don't ContainsKey " + key);
        }
    }

    public static void StopResultBG() {
        if (!isInstanced)
            return;
        if (instance.remoteConfig == null)
            return;
        if (!instance.remoteConfig.PlayNewSFX_Enable)
            return;

        var key = SFXType.result_bgm;
        if (instance.dictSFXSounds.ContainsKey(key)) {
            AudioPlayer.Stop(instance.dictSFXSounds[key]);
        } else {
            Logger.LogError("dictSFXSounds don't ContainsKey " + key);
        }
    }

    public void PlayResultGEM(float volume = 0.5f) {
        if (!remoteConfig.PlayNewSFX_Enable)
            return;

        var key = SFXType.result_gem;
        if (dictSFXSounds.ContainsKey(key)) {
            PlaySingleSound(dictSFXSounds[key], volume);
        } else {
            Logger.LogError("dictSFXSounds don't ContainsKey " + key);
        }
    }

    public static void PlaySFX_PopupOpen(float volume = 1f) {
        if (!isInstanced || !instance.remoteConfig.PlayNewSFX_Enable) {
            return;
        }

        SFXType key = SFXType.popup_open;
        if (instance.dictSFXSounds.ContainsKey(key)) {
            instance.PlaySingleSound(instance.dictSFXSounds[key], volume);
        } else {
            Logger.LogError("dictSFXSounds don't ContainsKey " + key);
        }
    }

    public static void PlaySFX_PopupClose(float volume = 1f) {
        if (!isInstanced || !instance.remoteConfig.PlayNewSFX_Enable) {
            return;
        }

        SFXType key = SFXType.popup_close;
        if (instance.dictSFXSounds.ContainsKey(key)) {
            instance.PlaySingleSound(instance.dictSFXSounds[key], volume);
        } else {
            Logger.LogError("dictSFXSounds don't ContainsKey " + key);
        }
    }

    public void PlaySFX_GemCount(float volume = 0.5f) {
        if (!remoteConfig.PlayNewSFX_Enable)
            return;

        var key = SFXType.gem_count;
        if (dictSFXSounds.ContainsKey(key)) {
            PlaySingleSound(dictSFXSounds[key], volume, true);
        } else {
            Logger.LogError("dictSFXSounds don't ContainsKey " + key);
        }
    }

    public void StopSFX_GemCount() {
        if (remoteConfig == null)
            return;
        if (!remoteConfig.PlayNewSFX_Enable)
            return;

        var key = SFXType.gem_count;
        if (dictSFXSounds.ContainsKey(key)) {
            AudioPlayer.Stop(dictSFXSounds[key]);
        } else {
            Logger.LogError("dictSFXSounds don't ContainsKey " + key);
        }
    }

    public void PlaySFX_OpenShop(float volume = 0.5f) {
        if (!remoteConfig.PlayNewSFX_Enable)
            return;

        var key = SFXType.open_shop;
        if (dictSFXSounds.ContainsKey(key)) {
            PlaySingleSound(dictSFXSounds[key], volume);
        } else {
            Logger.LogError("dictSFXSounds don't ContainsKey " + key);
        }
    }

    public static void PlaySFX_SelectCharacter(float volume = 0.5f) {
        if (!isInstanced)
            return;
        if (instance.remoteConfig == null || !instance.remoteConfig.PlayNewSFX_Enable)
            return;

        var key = SFXType.character_select;
        if (instance.dictSFXSounds.ContainsKey(key)) {
            instance.PlaySingleSound(instance.dictSFXSounds[key], volume);
        } else {
            Logger.LogError("dictSFXSounds don't ContainsKey " + key);
        }
    }

    public void Pause() {
        AudioPlayer.Pause();
    }

    public void StopAll() {
        for (int chanel = 0; chanel < maxChannel; chanel++) {
            if (AudioPlayer.IsPlaying(chanel)) {
                AudioPlayer.Stop(chanel);
            }
        }
    }

    private static void TryPlayVFX(SFXType key, float volume = 0.5f) {
        if (!isInstanced) {
            return;
        }

        if (instance.dictSFXSounds.ContainsKey(key)) {
            instance.PlaySingleSound(instance.dictSFXSounds[key], volume);
        }
    }

    public static void PlayResultDiamondCollect(float volume = 0.5f) {
        TryPlayVFX(SFXType.result_tile_collect, volume);
    }

    public static void PlayResultRewardPopup(float volume = 0.5f) {
        TryPlayVFX(SFXType.result_reward_popup, volume);
    }

    public static void PlayHomeTileCollect(float volume = 0.5f) {
        TryPlayVFX(SFXType.home_tile_collect, volume);
    }

    public static void PlayHomeSongPackUnlock(float volume = 0.5f) {
        TryPlayVFX(SFXType.home_pack_unlock, volume);
    }

    public static void PlayHomeOnboarding(float volume = 0.5f) {
        TryPlayVFX(SFXType.home_onboarding, volume);
    }

    public static void PlayEatSingleDiamond() {
        if (RemoteConfigBase.instance.PlayNewSFX_UP_Enable) {
            TryPlayVFX(SFXType.ingame_diamond_collect);
            return;
        }

        if (RemoteConfig.instance.PlayNewSFX_Enable) {
            return;
        }

        PlayCoins(0.02f);
    }

    #region SJ

    public static void PlayResultDiskPopin(float volume = 0.5f) {
        TryPlayVFX(SFXType.result_disk_popin, volume);
    }

    public static void PlayResultSmallStarPopin(float volume = 0.5f) {
        TryPlayVFX(SFXType.result_small_star_popin, volume);
    }

    public static void PlayHomeSJStarFlyingTo(float volume = 0.5f) {
        TryPlayVFX(SFXType.home_sj_star_flying_to, volume);
    }

    public static void PlayHomeSmallStar(float volume = 0.5f) {
        TryPlayVFX(SFXType.home_small_star, volume);
    }

    public static void PlayHomeSJNewLevel(float volume = 0.5f) {
        TryPlayVFX(SFXType.home_sj_new_level, volume);
    }

    public static void PlaySJFeatureProgressLineSlide(float volume = 0.5f) {
        TryPlayVFX(SFXType.sj_feature_progress_line_slide, volume);
    }

    public static void PlaySJFeatureUnlockRewardExtra(float volume = 0.5f) {
        TryPlayVFX(SFXType.sj_feature_unlock_reward_extra, volume);
    }

    public static void PlayPopupSlide(float volume = 1) {
        TryPlayVFX(SFXType.popup_slide, volume);
    }

    public static void PlayGiftOpen(float volume = 1) {
        TryPlayVFX(SFXType.sj_gift_open, volume);
    }

    public static void PlayGiftItemPop(float volume = 1) {
        TryPlayVFX(SFXType.sj_gift_item_pop, volume);
    }

    #endregion

    #region New update TH-4515

    public static void PlayGameplayStrongNote(float volume = 0.5f) {
        TryPlayVFX(SFXType.gameplay_strong_note, volume);
    }

    public static void PlayGameplayMoodChange(float volume = 0.5f) {
        TryPlayVFX(SFXType.gameplay_moodchange, volume);
    }

    public static void PlayScrollTick(float volume = 1) {
        TryPlayVFX(SFXType.scroll_tick, volume);
    }

    public static void PlayOpenMission(float volume = 1) {
        TryPlayVFX(SFXType.list_menu_open, volume);
    }

    public static void PlayCloseMission(float volume = 1) {
        TryPlayVFX(SFXType.list_menu_close, volume);
    }

    public static void PlayGemPurchased(float volume = 1) {
        TryPlayVFX(SFXType.gem_purchased, volume);
    }

    public static void PlayShopBallTap(float volume = 1) {
        TryPlayVFX(SFXType.shop_ball_tap, volume);
    }

    public static void PlayBallPurchased(float volume = 1) {
        TryPlayVFX(SFXType.shop_ball_purchased, volume);
    }

    public static void PlayFreeGiftCollect(float volume = 1) {
        TryPlayVFX(SFXType.free_gift_collect, volume);
    }

    public static void PlayShopSelectChar(float volume = 1) {
        TryPlayVFX(SFXType.shop_select_char, volume);
    }

    public static void PlaySongCardPreview(float volume = 1) {
        TryPlayVFX(SFXType.song_card_preview, volume);
    }

	#endregion

	#region Booster
	public static void PlayBoosterShieldBaseActive(float volume = 1f) {
		TryPlayVFX(SFXType.booster_shield_base_active, volume);
	}

	public static void PlayBoosterTurnOff(float volume = 1f) {
		TryPlayVFX(SFXType.booster_turnoff, volume);
	}

	public static void PlayBoosterTileTidyActive(float volume = 1f) {
		TryPlayVFX(SFXType.booster_tiletidy_active, volume);
	}

	public static void PlayBoosterMagnetActive(float volume = 1f) {
		TryPlayVFX(SFXType.booster_tiletidy_active, volume);
	}
	#endregion
}