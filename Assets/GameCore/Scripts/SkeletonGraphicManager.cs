using System;
using System.Collections.Generic;
using Spine.Unity;
using UnityEngine;

/// <summary>
/// trungvt
/// </summary>
public class SkeletonGraphicManager : MonoBehaviour {
    [Serializable]
    class AnimationName {
        public ColorType colorType;
        public string name;
    }

    #region Fields

    [SerializeField] private SkeletonAnimation skeletonGraphicLeft;
    [SerializeField] private SkeletonAnimation skeletonGraphicRight;

    [SerializeField] private List<AnimationName> animationCharacterLeft;
    [SerializeField] private List<AnimationName> animationCharacterRight;

    #endregion

    #region Unity Methods

    void Awake() {
        Spawner.onBgChange += OnBgChange;
    }

    void OnDestroy() {
        Spawner.onBgChange -= OnBgChange; 
    }

    #endregion

    #region Methods

    private void OnBgChange(byte current, byte next, float duration) {
        ColorType colorType = Spawner.s.GetSkinSet(next).colorType;

        string animationNameLeft = GetAnimationName(colorType, animationCharacterLeft);
        if (!string.IsNullOrEmpty(animationNameLeft)) {
            skeletonGraphicLeft.AnimationState.SetAnimation(0, animationNameLeft, true);
        }

        string animationNameRight = GetAnimationName(colorType, animationCharacterRight);
        if (!string.IsNullOrEmpty(animationNameRight)) {
            skeletonGraphicRight.AnimationState.SetAnimation(0, animationNameRight, true);
        }
    }

    private string GetAnimationName(ColorType colorType, List<AnimationName> animationNames) {
        foreach (AnimationName animationName in animationNames) {
            if (animationName.colorType == colorType) {
                return animationName.name;
            }
        }

        return null;
    }

    #endregion
}