using System.Collections.Generic;
using UnityEngine;

public static class TransformExtensions {
    /// <summary>
    /// Destroy or recycle all object child in transform
    /// </summary>
    /// <param name="transform">Transform need clear child</param>
    /// <returns>Transform after clear child</returns>
    public static Transform ClearChild(this Transform transform) {
        if (!Application.isPlaying && (Application.platform == RuntimePlatform.WindowsEditor ||
                                       Application.platform == RuntimePlatform.OSXEditor)) {
            int childs = transform.childCount;
            for (int i = childs - 1; i >= 0; i--) {
                Object.DestroyImmediate(transform.GetChild(i).gameObject);
            }

            return transform;
        } else {
            return transform.RecycleChild();
        }
    }

    /// <summary>
    /// Get All childs in transform
    /// </summary>
    /// <param name="transform">Transform need get childs</param>
    /// <returns>List Transform child</returns>
    public static List<Transform> GetChilds(this Transform transform) {
        List<Transform> tfChilds = new List<Transform>();
        for (int i = 0; i < transform.childCount; i++) {
            tfChilds.Add(transform.GetChild(i));
        }

        return tfChilds;
    }


    public static void SetLayerRecursively(this Transform transform, int newLayer) {
        transform.gameObject.layer = newLayer;
        foreach (Transform child in  transform) {
            SetLayerRecursively(child, newLayer);
		}
	}
    
    public static void SetLayerRecursivelyIgnoreTag(this Transform transform, int newLayer, string ignoreTag) {
        GameObject gameObject = transform.gameObject;
        if (gameObject.CompareTag(ignoreTag)) {
            return;
        }
        gameObject.layer = newLayer;
        foreach (Transform child in  transform) {
            SetLayerRecursivelyIgnoreTag(child, newLayer, ignoreTag);
        }
    }

    /// <summary>
    /// GetPosition of transform 
    /// </summary>
    /// <param name="tf">Transform need get position</param>
    /// <returns>position transform</returns>
    public static Vector3 GetPosition(this Transform tf) {
        return tf.position;
    }

    /// <summary>
    /// Get PositionX of transform 
    /// </summary>
    /// <param name="tf">Transform need get positionX</param>
    /// <returns>positionX transform</returns>
    public static float GetPositionX(this Transform tf) {
        return tf.position.x;
    }

    /// <summary>
    /// GetPositionY of transform 
    /// </summary>
    /// <param name="tf">Transform need get positionY</param>
    /// <returns>positionY transform</returns>
    public static float GetPositionY(this Transform tf) {
        return tf.position.y;
    }

    /// <summary>
    /// GetZ
    /// </summary>
    /// <param name="tf"></param>
    /// <returns></returns>
    public static float GetPositionZ(this Transform tf) {
        return tf.position.z;
    }

    /// <summary>
    /// Set new position transform
    /// </summary>
    /// <param name="tf">Transform need set</param>
    /// <param name="pos">New pos</param>
    /// <param name="space">Type Space</param>
    public static void SetPosition(this Transform tf, Vector3 pos, Space space = Space.World) {
        if (space == Space.World)
            tf.position = pos;
        else
            tf.localPosition = pos;
    }

    /// <summary>
    /// Set new position transform with x y z
    /// </summary>
    /// <param name="tf">Transform need set</param>
    /// <param name="x">New pos x</param>
    /// <param name="y">New pos y</param>
    /// <param name="z">New pos z</param>
    public static void SetPosition(this Transform tf, float x, float y, float z = 0.0f) {
        Vector3 position = tf.position;
        position.x = x;
        position.y = y;
        position.z = z;
        tf.position = position;
    }

    /// <summary>
    /// Set position X of transform
    /// </summary>
    /// <param name="tf">Transform need set posX</param>
    /// <param name="x">New PosX</param>
    public static void SetPositionX(this Transform tf, float x) {
        Vector3 position = tf.position;
        position.x = x;
        tf.position = position;
    }

    /// <summary>
    /// Set position Y of transform
    /// </summary>
    /// <param name="tf">Transform need set posY</param>
    /// <param name="y"></param>
    public static void SetPositionY(this Transform tf, float y) {
        Vector3 position = tf.position;
        position.y = y;
        tf.position = position;
    }

    /// <summary>
    /// SetZ
    /// </summary>
    /// <param name="tf"></param>
    /// <param name="z"></param>
    public static void SetPositionZ(this Transform tf, float z) {
        Vector3 position = tf.position;
        position.z = z;
        tf.position = position;
    }

    #region Local Position

    /// <summary>
    /// GetLocalX
    /// </summary>
    /// <param name="tf"></param>
    /// <returns></returns>
    public static float GetLocalX(this Transform tf) {
        Vector3 localPosition = tf.localPosition;
        return localPosition.x;
    }

    /// <summary>
    /// GetLocalY
    /// </summary>
    /// <param name="tf"></param>
    /// <returns></returns>
    public static float GetLocalY(this Transform tf) {
        Vector3 localPosition = tf.localPosition;
        return localPosition.y;
    }

    /// <summary>
    /// GetLocalZ
    /// </summary>
    /// <param name="tf"></param>
    /// <returns></returns>
    public static float GetLocalZ(this Transform tf) {
        Vector3 localPosition = tf.localPosition;
        return localPosition.z;
    }

    /// <summary>
    /// Set new local position transform
    /// </summary>
    /// <param name="tf">Transform need set</param>
    /// <param name="pos">New local position</param>
    public static void SetLocalPosition(this Transform tf, Vector3 pos) {
        tf.localPosition = pos;
    }

    /// <summary>
    /// Set new local positionX transform
    /// </summary>
    /// <param name="tf">Transform need set</param>
    /// <param name="x">New local positionX</param>
    public static void SetLocalX(this Transform tf, float x) {
        Vector3 localPosition = tf.localPosition;
        localPosition.x = x;
        tf.localPosition = localPosition;
    }

    /// <summary>
    /// Set new local positionY transform
    /// </summary>
    /// <param name="tf">Transform need set</param>
    /// <param name="y">New local positionY</param>
    public static void SetLocalY(this Transform tf, float y) {
        Vector3 localPosition = tf.localPosition;
        localPosition.y = y;
        tf.localPosition = localPosition;
    }

    /// <summary>
    /// Set new local positionZ transform
    /// </summary>
    /// <param name="tf">Transform need set</param>
    /// <param name="z">New local positionZ</param>
    public static void SetLocalZ(this Transform tf, float z) {
        Vector3 localPosition = tf.localPosition;
        localPosition.z = z;
        tf.localPosition = localPosition;
    }

    #endregion

    /// <summary>
    /// Get Scale of transform
    /// </summary>
    /// <param name="tf">Transform need set</param>
    /// <returns>local scale of transform</returns>
    public static Vector3 GetScale(this Transform tf) {
        return tf.localScale;
    }

    /// <summary>
    /// Get ScaleX of transform
    /// </summary>
    /// <param name="tf">Transform need set</param>
    /// <returns>local scaleX of transform</returns>
    public static float GetScaleX(this Transform tf) {
        return tf.localScale.x;
    }

    /// <summary>
    /// Get ScaleY of transform
    /// </summary>
    /// <param name="tf">Transform need set</param>
    /// <returns>local scaleY of transform</returns>
    public static float GetScaleY(this Transform tf) {
        return tf.localScale.y;
    }

    /// <summary>
    /// Set scale transform follow value
    /// </summary>
    /// <param name="tf">Transform need set</param>
    /// <param name="scale">Value scale</param>
    /// <returns>local scale of transform</returns>
    public static void SetScale(this Transform tf, float scale) {
        tf.localScale = Vector3.one * scale;
    }

    /// <summary>
    /// Set scale transform follow vector3
    /// </summary>
    /// <param name="tf">Transform need set</param>
    /// <param name="scale"> Vector3 Scale</param>
    public static void SetScale(this Transform tf, Vector3 scale) {
        tf.localScale = scale;
    }

    /// <summary>
    /// Set scale transform follow value X
    /// </summary>
    /// <param name="tf">Transform need set</param>
    /// <param name="x">Value scale by X</param>
    public static void SetScaleX(this Transform tf, float x) {
        Vector3 localScale = tf.localScale;
        localScale.x = x;
        tf.localScale = localScale;
    }

    /// <summary>
    /// Set scale transform follow value Y
    /// </summary>
    /// <param name="tf">Transform need set</param>
    /// <param name="y">Value scale by Y</param>
    public static void SetScaleY(this Transform tf, float y) {
        Vector3 localScale = tf.localScale;
        localScale.y = y;
        tf.localScale = localScale;
    }
    
    public static void SetScaleZ(this Transform tf, float z) {
        Vector3 localScale = tf.localScale;
        localScale.z = z;
        tf.localScale = localScale;
    }

    /// <summary>
    /// Set scale transform follow x y 
    /// </summary>
    /// <param name="tf">Transform need set</param>
    /// <param name="x">value scale by X</param>
    /// <param name="y">value scale by Y</param>
    public static void SetScale(this Transform tf, float x, float y) {
        tf.localScale = new Vector3(x, y, 1f);
    }

    public static Vector3 GetRotation(this Transform tf) {
        return tf.rotation.eulerAngles;
    }

    public static void SetRotation(this Transform tf, Vector3 rot, Space space = Space.World) {
        if (space == Space.Self)
            tf.localRotation = Quaternion.Euler(rot);
        else
            tf.rotation = Quaternion.Euler(rot);
    }

    public static float GetRotation2D(this Transform tf, Space space = Space.World) {
        float num = space != Space.World ? tf.localRotation.eulerAngles.z : tf.rotation.eulerAngles.z;
        if ((double) num >= 180.0)
            num -= 360f;
        return num;
    }

    public static void SetRotation2D(this Transform tf, float rot, Space space = Space.World) {
        if (space == Space.Self)
            tf.localRotation = Quaternion.Euler(Vector3.forward * rot);
        else
            tf.rotation = Quaternion.Euler(Vector3.forward * rot);
    }

    private static Transform RecycleChild(this Transform transform) {
        for (int i = transform.childCount - 1; i >= 0; i--) {
            transform.GetChild(i).Recycle();
        }

        return transform;
    }

    public static Vector3 RootLocalPos(this Transform tf) {
        Vector3 localPos = tf.localPosition;
        Transform tempTransform = tf;
        while (tempTransform.parent!=null) {
            if (tempTransform.parent.parent == null) break;
            var parent = tempTransform.parent;
            localPos += parent.localPosition;
            tempTransform = parent;
        }
        return localPos;
    }
}