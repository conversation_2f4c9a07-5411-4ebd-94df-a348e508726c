using System;
using Music.ACM;
using UnityEngine;
using UnityEngine.UI;
using Utils = Inwave.Utils;

[RequireComponent(typeof(RectTransform))]
[DisallowMultipleComponent]
public class SafeArea : MonoBehaviour {
    [SerializeField] private float bottomBannerShift = 0;
    [SerializeField] private float topBannerShift    = 0;

    private RectTransform _safeAreaRect;
    private Canvas        _canvas;
    private Vector2       _canvasSize;
    private Rect          _lastSafeArea;
    public event Action OnChangeSize;
    public float AnchorMaxY => _safeAreaRect.anchorMax.y;
    private bool _isSameHeight;

    #region Unity Method

    private void Awake() {
        _safeAreaRect = GetComponent<RectTransform>();
        if (_canvas == null) {
            _canvas = GetComponentInParent<Canvas>();
        }

        OnRectTransformDimensionsChange();
    }

    private void Start() {
        if (_canvas != null) {
            RectTransform canvasRect = (RectTransform) _canvas.transform;
            _canvasSize = canvasRect.sizeDelta;
            if (_canvasSize.x == 0 || _canvasSize.y == 0) {
                var rect = canvasRect.rect;
                _canvasSize = new Vector2(rect.width, rect.height);
            }
        }

        if (AdsManager.isInstanced) {
            AdsManager.instance.OnBannerChange += UpdateBanner;
        }

        _isSameHeight = Display.main.systemHeight == Display.main.renderingHeight;
        UpdateBanner();
    }

    private void OnDestroy() {
        if (AdsManager.isInstanced) {
            AdsManager.instance.OnBannerChange -= UpdateBanner;
        }
    }

    private void OnRectTransformDimensionsChange() {
        UpdateSizeToSafeArea();
    }

    #endregion

    private void UpdateSizeToSafeArea(Rect safeArea) {
        if (_safeAreaRect != null) {
            _safeAreaRect.anchoredPosition = Vector2.zero;
            _safeAreaRect.sizeDelta = Vector2.zero;
        }

        var anchorMin = safeArea.position;
        var anchorMax = safeArea.position + safeArea.size;
        anchorMin.x /= Utils.GetWidth();

        if (AdsManager.isInstanced && !AdsManager.instance.IsBottomBannerShown()) {
            anchorMin.y = 0; //Fix bottom home button ios
        } else {
            anchorMin.y /= Utils.GetHeight();
        }

        anchorMax.x /= Utils.GetWidth();
        anchorMax.y /= Utils.GetHeight();
        if (_safeAreaRect != null) {
            _safeAreaRect.anchorMin = anchorMin;
            _safeAreaRect.anchorMax = anchorMax;
        }
    }

    private void UpdateBanner() {
        QueueRunNextFrame.instance.Enqueue(() => {
            if (!this) {
                return;
            }

            UpdateSizeToSafeArea(true);
            if (_safeAreaRect != null) {
                if (AdsManager.isInstanced && AdsManager.instance.IsBottomBannerShown()) {
                    _safeAreaRect.offsetMin = new Vector2(_safeAreaRect.offsetMin.x,
                        ConvertDpToPixel(AdsManager.instance.GetBannerHeight(), true) + bottomBannerShift);
                } else if (AdsManager.isInstanced && AdsManager.instance.IsTopBannerShown()) {
                    _safeAreaRect.offsetMax = new Vector2(_safeAreaRect.offsetMax.x,
                        -ConvertDpToPixel(AdsManager.instance.GetBannerHeight(), false) + topBannerShift);
                } else {
                    _safeAreaRect.offsetMin = new Vector2(_safeAreaRect.offsetMin.x, 0);
                }
            }

            OnChangeSize?.Invoke();
        });
    }

    public static Rect GetSafeArea() {
        if (!Application.isMobilePlatform) { //Editor
            if (Configuration.instance != null && Configuration.instance.testNotchDevices) {
                return GetSafeAreaEditor();
            } else {
                return new Rect(0, 0, Utils.GetWidth(), Utils.GetHeight());
            }
        } else {
            return Screen.safeArea;
        }
    }

    public static float GetTopDistance(Transform source) {
        if (!Application.isMobilePlatform) {
            if (Configuration.instance.testNotchDevices) {
                float safeAreaYMax = (Utils.GetHeight() - GetSafeAreaEditor().yMax) *
                    source.GetComponentInParent<CanvasScaler>().referenceResolution.y / Utils.GetHeight();
                return safeAreaYMax;
            } else {
                return 0;
            }
        } else {
            return (Utils.GetHeight() - GetSafeArea().yMax) *
                source.GetComponentInParent<CanvasScaler>().referenceResolution.y / Utils.GetHeight();
        }
    }

    public static float GetBottomDistance(Transform source) {
        if (!Application.isMobilePlatform) {
            if (Configuration.instance.testNotchDevices) {
                float safeAreaYMin = (GetSafeAreaEditor().yMin) *
                    source.GetComponentInParent<CanvasScaler>().referenceResolution.y / Utils.GetHeight();
                return safeAreaYMin;
            } else {
                return 0;
            }
        } else {
            return (GetSafeArea().yMin) * source.GetComponentInParent<CanvasScaler>().referenceResolution.y /
                   Utils.GetHeight();
        }
    }

    private float GetMoreDistanceScreen(float moreDistanceUI, Transform source) {
        float distanceScreen = moreDistanceUI / source.GetComponentInParent<CanvasScaler>().referenceResolution.y *
                               Utils.GetHeight();
        return distanceScreen;
    }

    public static Rect GetSafeAreaEditor() {
        return new Rect(0, 0.05f * Utils.GetHeight(), Utils.GetWidth(), Utils.GetHeight() * 0.9f);
    }

    private void UpdateSizeToSafeArea(bool isForce = false) {
        Rect safeArea = GetSafeArea();
        if (safeArea != null && _canvas != null && (isForce || safeArea != _lastSafeArea)) {
            _lastSafeArea = safeArea;
            UpdateSizeToSafeArea(safeArea);
        }
    }

    private float ConvertDpToPixel(float dp, bool isBottom) {
        float bannerSizePixels = dp * Utils.GetDPI() / 160;

        if (_isSameHeight) {
            Rect safeArea = GetSafeArea();
            float offsetWithSafeArea = (isBottom ? safeArea.y : (Utils.GetHeight() - (safeArea.y + safeArea.height)));
            bannerSizePixels -= offsetWithSafeArea;
        }

        float bannerSizeCanvas = bannerSizePixels / Utils.GetHeight() * _canvasSize.y;
        return bannerSizeCanvas;
    }

    public void SetCanvas(Canvas canvas) {
        this._canvas = canvas;
    }
}