using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public interface IAudioDevice 
{
    void RegisterEvents();
    AudioDeviceInfo GetInfo();
    AudioDeviceInfo ParseStringInfo(string param);
}

public class AudioDeviceInfo
{
    public AudioDeviceType deviceType;
    public string deviceRawType;
    public string deviceName;

    public override string ToString()
    {
        if (string.IsNullOrEmpty(deviceRawType) || string.IsNullOrEmpty(deviceName)) return base.ToString();
        return $"type: {deviceType.ToEventString()} rawType:{deviceRawType} name:{deviceName}";
    }
}

public enum AudioDeviceType
{
    Unknown = 0,
    Internal,
    Wire,
    Wireless,
    Bluetooth,
}

public static class AudioDeviceTypeExtension
{
    public static string ToEventString(this AudioDeviceType deviceType)
    {
        return deviceType.ToString().ToLower();
    }
}