using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class DeerAnim : MonoBehaviour
{

	public Animator animator;
	public RectTransform rect;
	// Use this for initialization

	private int[] positions;
	private int nextPositions = 0;
	private float eatTime = 0;
	public Image sr;
	//private int direction = 1;

	void Start ()
	{
		positions = new int[]{ -50, 70, 292, 426, 536 };
		Walk (UnityEngine.Random.Range (1, positions.Length - 2));
	}

	// Update is called once per frame
	void Update ()
	{
		if (rect.anchoredPosition.x > positions [positions.Length - 1]) {
			Vector2 resetPosition = rect.anchoredPosition;
			resetPosition.x = positions [0];
			rect.anchoredPosition = resetPosition;
			Walk (UnityEngine.Random.Range (1, positions.Length - 2));
		} else {
			if (rect.anchoredPosition.x < positions [nextPositions]) {
				rect.anchoredPosition += Vector2.right * Time.deltaTime * 40;
			} else {
                if (Mathf.Approximately(eatTime,0)) {
					eatTime = Time.time + UnityEngine.Random.Range (6, 12);
					animator.Play ("Eat");
				} else {
					if (eatTime < Time.time) {
						eatTime = 0;
						Walk (positions.Length - 1);
						animator.StopPlayback ();
					}
				}
			}
		}
		
	}

	public void Walk (int i)
	{
		nextPositions = i;
		animator.Play ("Walk");
	}
}
