using System.Collections;
using UnityEngine;
using System;

public class ItweenShakeEffect : MonoBehaviour {
    public enum ShakeType {
        DEFAULT,
        GIFT,
        EVENT
    }

    [SerializeField] ShakeType type = ShakeType.DEFAULT;
    [SerializeField] private float shakeDecay = 0.001f;
    [SerializeField] private float shakeIntensity = .2f;
    
    private Vector3 _originPosition;
    private Quaternion _originRotation;
    private float _tempShakeIntensity = 0;
    bool _waitsharking = false;
    private static int GiftTime = 3600; //in seconds
    private static DateTime _giftReceivedDate;
    private Transform _transform;

    void Awake() {
        _transform = transform;
        _originPosition = _transform.localPosition;
        _originRotation = _transform.localRotation;

        int giftTime = RemoteConfig.instance.GetFreeGiftTime() ;

        if(giftTime < 0) {
            GiftTime = 3600;// default 1 hour
		} else {
            GiftTime = giftTime;
		}

    }

    private void OnEnable() {
        _giftReceivedDate = Util.GetDateTimeByString(Configuration.GetGiftTime(CONFIG_STRING.HourlyGiftTime),
            DateTime.Now.AddDays(-1));
        if (IsShake()) {
            _tempShakeIntensity = shakeIntensity;
            _waitsharking = false;
        }
    }

    void Update() {
        if ((type == ShakeType.DEFAULT || type == ShakeType.EVENT || IsShake())) {
            if (_tempShakeIntensity > 0) {
                _transform.localPosition =
                    _originPosition + UnityEngine.Random.insideUnitSphere * (_tempShakeIntensity * 20);
                _transform.localRotation = new Quaternion(
                    _originRotation.x + UnityEngine.Random.Range(-_tempShakeIntensity, _tempShakeIntensity) * .2f,
                    _originRotation.y + UnityEngine.Random.Range(-_tempShakeIntensity, _tempShakeIntensity) * .2f,
                    _originRotation.z + UnityEngine.Random.Range(-_tempShakeIntensity, _tempShakeIntensity) * .2f,
                    _originRotation.w + UnityEngine.Random.Range(-_tempShakeIntensity, _tempShakeIntensity) * .2f);
                _tempShakeIntensity -= shakeDecay;
            }
            else {
                if (!_waitsharking) {
                    _waitsharking = true;
                    StartCoroutine(Shake());
                }
            }
        }
        else {
            _transform.localPosition = _originPosition;
            _transform.localRotation = _originRotation;
        }
    }

    IEnumerator Shake() {
        yield return new WaitForSeconds(1f + UnityEngine.Random.Range(0, 100) / 100f);
        _tempShakeIntensity = shakeIntensity;
        _waitsharking = false;
    }

    private static bool IsShake() {
        // Economy System: TH-1442 : Bỏ thưởng gem mỗi 1h tại FreeVideo
        if (RemoteConfig.instance.Economy_IsEnable) {
            return false;
        }
        return GetGiftTimeRemain() < 0;
    }

    public static double GetGiftTimeRemain() {
        double secondsDiff = (DateTime.Now - _giftReceivedDate).TotalSeconds;
        return GiftTime - secondsDiff;
    }

    public void Stop() {
        enabled = false;
        _transform.localPosition = _originPosition;
        _transform.localRotation = _originRotation;
        StopAllCoroutines();
    }

    public void Resume() {
        enabled = true;
    }

    public static void ReceiveGift(int amount) {
        if (IsShake()) {
            Configuration.SetGiftTime(CONFIG_STRING.HourlyGiftTime,
                DateTime.Now.ToString(CONFIG_STRING.DateTimeFormat));
            _giftReceivedDate = DateTime.Now;
            Configuration.UpdateDiamond(amount, CurrencyEarnSource.HOURLY_GIFT.ToString());
        }
    }
}
