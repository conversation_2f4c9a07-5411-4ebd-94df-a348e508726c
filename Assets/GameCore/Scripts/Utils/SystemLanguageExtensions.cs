using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public static class SystemLanguageExtensions {
    private static readonly Dictionary<SystemLanguage, string> CountryCodes = new Dictionary<SystemLanguage, string> {
        { SystemLanguage.Afrikaans, "ZA" },
        { SystemLanguage.Arabic, "SA" },
        { SystemLanguage.Basque, "ES" },
        { SystemLanguage.Belarusian, "BY" },
        { SystemLanguage.Bulgarian, "BG" },
        { SystemLanguage.Catalan, "ES" },
        { SystemLanguage.Chinese, "CN" },
        { SystemLanguage.Czech, "CZ" },
        { SystemLanguage.Danish, "DK" },
        { SystemLanguage.Dutch, "NL" },
        { SystemLanguage.English, "US" },
        { SystemLanguage.Estonian, "EE" },
        { SystemLanguage.Faroese, "FO" },
        { SystemLanguage.Finnish, "FI" },
        { SystemLanguage.French, "FR" },
        { SystemLanguage.German, "DE" },
        { SystemLanguage.Greek, "GR" },
        { SystemLanguage.Hebrew, "IL" },
        { SystemLanguage.Hungarian, "HU" },
        { SystemLanguage.Icelandic, "IS" },
        { SystemLanguage.Indonesian, "ID" },
        { SystemLanguage.Italian, "IT" },
        { SystemLanguage.Japanese, "JP" },
        { SystemLanguage.Korean, "KR" },
        { SystemLanguage.Latvian, "LV" },
        { SystemLanguage.Lithuanian, "LT" },
        { SystemLanguage.Norwegian, "NO" },
        { SystemLanguage.Polish, "PL" },
        { SystemLanguage.Portuguese, "PT" },
        { SystemLanguage.Romanian, "RO" },
        { SystemLanguage.Russian, "RU" },
        { SystemLanguage.SerboCroatian, "RS" }, //HR for Croatia
        { SystemLanguage.Slovak, "SK" },
        { SystemLanguage.Slovenian, "SI" },
        { SystemLanguage.Spanish, "ES" },
        { SystemLanguage.Swedish, "SE" },
        { SystemLanguage.Thai, "TH" },
        { SystemLanguage.Turkish, "TR" },
        { SystemLanguage.Ukrainian, "UA" },
        { SystemLanguage.Vietnamese, "VN" },
        { SystemLanguage.Unknown, "US" }
    };

    /// <summary>
    /// Returns approximate country code of the language.
    /// </summary>
    /// <returns>Approximated country code.</returns>
    /// <param name="language">Language which should be converted to country code.</param>
    public static string ToCountryCode(this SystemLanguage language) {
        if (CountryCodes.TryGetValue(language, out string result)) {
            return result;
        } else {
            return CountryCodes[SystemLanguage.Unknown];
        }
    }
}