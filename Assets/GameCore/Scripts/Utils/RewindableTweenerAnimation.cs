using System;
using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using TileHop.Cores.Pooling;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

namespace TileHop.Cores.AnimationUI {
    public class RewindableTweenerAnimation : MonoBehaviour {
        public TweenerSetting[] tweenerSettings;

        [Space]
        public UnityEvent onCompletedAll;
        public UnityEvent onRewindedAll;

        private List<Tweener> _tweeners;
        private float         _totalAnimatedTime = 0;
        private Coroutine     _coroutine;

        private void Awake() {
            _tweeners = new List<Tweener>();
            _totalAnimatedTime = 0;
            foreach (var tweenerSetting in tweenerSettings) {
                _totalAnimatedTime = Mathf.Max(_totalAnimatedTime, tweenerSetting.duration + tweenerSetting.startDelay);
                if (SetupTweener(tweenerSetting, out Tweener tweener)) {
                    _tweeners.Add(tweener);
                }
            }
        }

        private void OnEnable() {
            PlayForward();
        }

        private void OnDestroy() {
            foreach (Tweener tweener in _tweeners) {
                tweener.Kill();
            }
        }

        [ContextMenu("Play forward")]
        public void PlayForward() {
            if (_coroutine != null) {
                StopCoroutine(_coroutine);
            }

            _coroutine = StartCoroutine(IEPlayForward());
        }

        [ContextMenu("Play backward")]
        public void PlayBackward() {
            if (_coroutine != null) {
                StopCoroutine(_coroutine);
            }

            _coroutine = StartCoroutine(IEPlayBackward());
        }

        private IEnumerator IEPlayForward() {
            foreach (Tweener tweener in _tweeners) {
                tweener.PlayForward();
            }

            yield return YieldPool.GetWaitForSeconds(_totalAnimatedTime);
            onCompletedAll?.Invoke();
        }

        private IEnumerator IEPlayBackward() {
            foreach (Tweener tweener in _tweeners) {
                tweener.PlayBackwards();
            }

            yield return YieldPool.GetWaitForSeconds(_totalAnimatedTime);
            onRewindedAll?.Invoke();
        }

        private bool SetupTweener(TweenerSetting setting, out Tweener tweener) {
            if (setting.target == null) {
                tweener = null;
                return false;
            }

            switch (setting.animatedType) {

                case AnimatedType.SCALE:
                    if (setting.target is Transform tfTarget) {
                        tweener = DOTween
                            .To(x => tfTarget.localScale = Vector3.one * x, setting.startValue, setting.endValue,
                                setting.duration).SetDelay(setting.startDelay).SetEase(setting.ease).SetAutoKill(false)
                            .Pause();
                        return true;
                    }

                    Debug.LogError(
                        $"[SetupTweener] can't convert {setting.animatedType.ToString()} target({setting.target.name}) to Transform");
                    tweener = null;
                    return false;

                case AnimatedType.FADE:
                    if (setting.target is CanvasGroup cgTarget) {
                        tweener = DOTween.To(x => cgTarget.alpha = x, setting.startValue, setting.endValue,
                                setting.duration).SetDelay(setting.startDelay).SetEase(setting.ease).SetAutoKill(false)
                            .Pause();
                        return true;
                    }

                    if (setting.target is Image imgTarget) {
                        tweener = DOTween.To(x => imgTarget.SetAlpha(x), setting.startValue, setting.endValue,
                                setting.duration).SetDelay(setting.startDelay).SetEase(setting.ease).SetAutoKill(false)
                            .Pause();
                        return true;
                    }

                    Debug.LogError(
                        $"[SetupTweener] can't convert {setting.animatedType.ToString()} target({setting.target.name}) to CanvasGroup or Image");
                    tweener = null;
                    return false;
            }

            tweener = null;
            return false;
        }
    }

    [Serializable]
    public class TweenerSetting {
        public Component    target;
        public float        duration;
        public float        startDelay;
        public AnimatedType animatedType;
        public Ease         ease = Ease.Linear;
        public float        startValue;
        public float        endValue;
    }

    public enum AnimatedType {
        SCALE,
        FADE,
    }
}