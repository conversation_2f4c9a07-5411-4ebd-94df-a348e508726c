using System;
using System.Collections;
using System.Collections.Generic;
using CielaSpike;
using UnityEngine;
using UnityEngine.UI;

public class LocalizationManager : MonoBehaviour {
    public delegate void LanguageChangeHandler();

    public event LanguageChangeHandler OnLanguageChange;
    public static LocalizationManager instance;
    public        TextAsset           localizedTextData;
    public        List<string>        supportedList;

    private Dictionary<string, string> _localizedText;
    private Font                       _vietnameseFont;

    public const  string DefaultLanguage      = "English";
    private const string DefaultKey           = "Key";
    private const string LanguageFileName     = "localization.csv";
    private const string LanguageVersionLocal = "2023.04.08";
    private const string LanguageVersion      = "language_version";
    private const string LanguageID           = "language_id";
    public const  string VietnameseID         = "Vietnamese";
    private const string VietnameseFontName   = "SairaCondensed-SemiBold";

    private void Awake() {
        if (instance == null) {
            instance = this;
            this.StartCoroutineAsync(IENGetDictionary());
        } else if (instance != null) {
            Destroy(gameObject);
        }
    }

    private void Start() {
        Util.WaitRemoteConfigDone(Init, true);
    }

    private void Init() {
        //Inwave.Utils.ShowTimeAction(GetType().Name, System.Reflection.MethodBase.GetCurrentMethod()?.Name);

        try {
            string remoteVersion = RemoteConfig.instance.LanguageRemoteVersion;
            if (!string.IsNullOrEmpty(remoteVersion)) {
                Version versionLocal = new Version(GetLanguageVersionLocal());
                Version versionRemote = new Version(remoteVersion);
                if (versionRemote > versionLocal) {
                    this.StartCoroutineAsync(IENDownloadCSVData());
                }
            }

        } catch (Exception e) {
            CustomException.Fire("LanguageManagerInit", e.ToString());
        }
    }

    private string GetLanguageData() {
        string text = null;

        Version versionCached = new Version(GetLanguageVersionLocal());
        Version versionInApk = new Version(LanguageVersionLocal);
        if (versionCached > versionInApk) {
            text = FileHelper.LoadFile(FileHelper.LocalPath(LanguageFileName));
        }

        if (string.IsNullOrEmpty(text)) { //get default localization in apk ~ LanguageVersionLocal
            text = localizedTextData.text;
        }

        return text;
    }

    private static IEnumerator IENDownloadCSVData() {
        yield return Ninja.JumpToUnity;

        WWW wwwTemp = new WWW(RemoteConfig.instance.LanguageRemotePath);
        yield return wwwTemp;

        if (wwwTemp.error == null && wwwTemp.isDone) {
            bool isChanged = false;
            string wwwData = wwwTemp.text;

            yield return instance.StartCoroutineAsync(instance.IENGetDictionary(wwwData, null, isChange => {
                //
                isChanged = isChange;
            }));

            if (isChanged) {
                string localPath = FileHelper.LocalPath(LanguageFileName);
                yield return Ninja.JumpBack;

                FileHelper.SaveFile(wwwData, localPath);

                yield return Ninja.JumpToUnity;

                SetLanguageVersionLocal(RemoteConfig.instance.LanguageRemoteVersion);
            }
        }
    }

    public bool ContainsKey(string key) {
        if (_localizedText != null && _localizedText.ContainsKey(key)) {
            return true;
        }

        return false;
    }

    public string GetLocalizedValue(string key) {
        string result = key;
        if (_localizedText != null) {
            result = _localizedText.TryGetValue(key, out string value) ? value : key.Replace("_", " ");
        }

        return result;
    }
    
    public bool TryGetLocalizedValue(string key, out string result) {
        if (_localizedText == null) {
            result = key;
            return false;
        }
        
        if (_localizedText.TryGetValue(key, out string value)) {
            result = value;
            return true;
        }
        
        result = key.Replace("_", " ");
        return false;
    }

    public string GetLocalizedUpperCase(string key) {
        string result = key;
        if (_localizedText != null) {
            var uppercaseKey = key.ToUpper();

            if (_localizedText.TryGetValue(uppercaseKey, out string value)) {
                result = value;
            } else {
                result = key.Replace("_", " ");
            }
        }

        return result;
    }

    public IEnumerator IENGetDictionary(string csvText = null, string langID = null, Action<bool> onDone = null) {
        yield return Ninja.JumpToUnity;

        if (string.IsNullOrEmpty(csvText)) {
            csvText = GetLanguageData();
        }
        if (string.IsNullOrEmpty(langID)) {
            langID = GetLanguageID();
        }
        if (string.IsNullOrEmpty(langID)) {
            langID = Application.systemLanguage == SystemLanguage.Chinese
                ? SystemLanguage.ChineseSimplified.ToString()
                : Application.systemLanguage.ToString();
        }
        yield return Ninja.JumpBack;

        string[,] grid = CSVReader.SplitCsvGrid(csvText, true);
        

        if (grid.Length > 0) {
            
            var key = DefaultKey;

            _localizedText = new Dictionary<string, string>();

            //int idCol = 0;
            int keyCol = 0;
            int valueCol = 0;
            int valueDefaultCol = 0;
            for (int x = 0; x < grid.GetUpperBound(0); x++) {
                string vl = grid[x, 0]?.Trim();
                if (!string.IsNullOrEmpty(vl)) {
                    if (vl == key) {
                        keyCol = x;
                    }

                    supportedList.Add(vl);
                }
            }

            valueCol = supportedList.IndexOf(langID);
            valueDefaultCol = supportedList.IndexOf(DefaultLanguage);

            if (valueCol < 0) {
                valueCol = supportedList.IndexOf(DefaultLanguage);
            }

            if (keyCol >= 0 && valueCol >= 0) {
                for (int y = 0; y < grid.GetUpperBound(1); y++) {
                    string keyLang = grid[keyCol, y];
                    if (!string.IsNullOrEmpty(keyLang)) {
                        if (_localizedText.ContainsKey(keyLang)) {
                            Logger.LogError($"An item with the same key has already been added. Key: {keyLang}");
                        } else {
                            string valueLang = grid[valueCol, y];
                            if (!string.IsNullOrEmpty(valueLang)) {
                                _localizedText.Add(keyLang, valueLang.Replace("\\n", "\n"));
                            } else {
                                string valueDefaultLang = grid[valueDefaultCol, y];
                                _localizedText.Add(keyLang, valueDefaultLang.Replace("\\n", "\n"));
                            }
                        }
                    }
                }
            }
            yield return Ninja.JumpToUnity;

            OnLanguageChange?.Invoke();

            onDone?.Invoke(true);
        } else {
            onDone?.Invoke(false);
        }

        yield return null;
    }
    
    /// <summary>
    /// Thêm localized strings
    /// </summary>
    /// <param name="csvText"></param>
    /// <param name="langID"></param>
    /// <param name="onDone">trả về trạng thái done (True: thành công) và dictionary(key cũ, key mới) các key bị trùng</param>
    /// <returns></returns>
    public IEnumerator IENAddDictionary(string csvText = null, string langID = null, Action<bool, Dictionary<string, string>> onDone = null) {
        if (string.IsNullOrEmpty(csvText)) {
            yield break;
        }
        yield return Ninja.JumpBack;

        string[,] grid = CSVReader.SplitCsvGrid(csvText, true);

        Dictionary<string, string> duplicatedKeys = new();
        if (grid != null && grid.Length > 0) {
            _localizedText ??= new Dictionary<string, string>();

            int keyCol = 0;
            int valueCol = 0;
            int valueDefaultCol = 0;
            
            keyCol = supportedList.IndexOf(DefaultKey);
            valueCol = supportedList.IndexOf(langID);
            valueDefaultCol = supportedList.IndexOf(DefaultLanguage);

            if (valueCol < 0) {
                valueCol = supportedList.IndexOf(DefaultLanguage);
            }

            if (keyCol >= 0 && valueCol >= 0) {
                for (int y = 0; y < grid.GetUpperBound(1); y++) {
                    string keyLang = grid[keyCol, y];
                    if (!string.IsNullOrEmpty(keyLang)) {
                        if (_localizedText.ContainsKey(keyLang)) {
                            // xử lý khi additional key trùng với key đã có
                            int postfix = 1;
                            while (_localizedText.ContainsKey(keyLang + postfix)) {
                                postfix++;
                            }
                            duplicatedKeys.Add(keyLang, keyLang + postfix);
                            Logger.EditorLogError($"[Add localization] Duplicate key: <{keyLang}, {keyLang}{postfix}>");
                        } else {
                            string valueLang = grid[valueCol, y];
                            if (!string.IsNullOrEmpty(valueLang)) {
                                _localizedText.Add(keyLang, valueLang.Replace("\\n", "\n"));
                            } else {
                                string valueDefaultLang = grid[valueDefaultCol, y];
                                _localizedText.Add(keyLang, valueDefaultLang.Replace("\\n", "\n"));
                            }
                        }
                    }
                }
            }
            yield return Ninja.JumpToUnity;

            // OnLanguageChange?.Invoke();
            onDone?.Invoke(true, duplicatedKeys);
        } 
        else {
            onDone?.Invoke(false, duplicatedKeys);
        }

        duplicatedKeys.Clear();
        yield return null;
    }


    public void AddLocalizedStrings(string csvText = null, string langID = null, Action<bool, Dictionary<string, string>> onDone = null) {
        this.StartCoroutineAsync(IENAddDictionary(csvText, langID, onDone));
    }
    
    private static string GetLanguageVersionLocal() {
        return PlayerPrefs.GetString(LanguageVersion, LanguageVersionLocal);
    }

    private static void SetLanguageVersionLocal(string version) {
        PlayerPrefs.SetString(LanguageVersion, version);
    }

    public static string GetLanguageID() {
        return PlayerPrefs.GetString(LanguageID, null);
    }

    public static void SetLanguageID(string id) {
        PlayerPrefs.SetString(LanguageID, id);
    }

    public bool IsLoaded() {
        return _localizedText != null && _localizedText.Count > 0;
    }

    public static string GetCurrentLanguageID() {
        string selectedLang = GetLanguageID();
        if (string.IsNullOrEmpty(selectedLang)) {
            selectedLang = Application.systemLanguage.ToString();
        }

        return selectedLang;
    }

    public void UpdateFont(Text text, Font originalFont = null) {
        if (!RemoteConfigBase.instance.EnableVietnameseFont || text == null) {
            return;
        }

        if (GetCurrentLanguageID() == VietnameseID || RemoteConfigBase.instance.EnableVietnameseFontForAll) {
            if (_vietnameseFont == null) {
                _vietnameseFont = Resources.Load<Font>(VietnameseFontName);
            }

            if (_vietnameseFont != null) {
                text.font = _vietnameseFont;
            }

        } else if (originalFont != null) {
            text.font = originalFont;
        }
    }

    public bool IsInited() {
        if (_localizedText != null && _localizedText.Count > 0) {
            return true;
        }

        return false;
    }
}