using System;
using System.Collections.Generic;
using TileHop.EconomySystem;
using UnityEngine;
using UnityEngine.Events;

public class SeasonalPackManager : FastSingleton<SeasonalPackManager> {
    #region CONST
    public const  string COUNT_DOWN_LOCALIZE_KEY              = "END_IN_COUNTDOWN";
    public const  string OFFER_ENDS_IN_COUNTDOWN_LOCALIZE_KEY = "OFFER_ENDS_IN_COUNTDOWN";
    public const  string DEFAULT_ENDS_IN_LOCALIZE_KEY         = "OFFER_ENDS_IN";
    public const  string REVIVE_TIME_FORMAT_LOCALIZE_KEY      = "IAP_SEASONAL_PACK_REVIVE_TIME";
    private const string REMAIN_COUNTDOWN_TIME_KEY_POSTFIX    = "_RemainTime";
    private const string ELAPSED_TIME_KEY_POSTFIX             = "_ElapsedTime";
    private const string SHOW_PHASE_COUNT_POSTFIX             = "_Phase";
    public const  string ADS_PURCHASE_COUNT_POSTFIX           = "_AdsPurchase";
    private const string PURCHASED_POSTFIX                    = "_Purchased";

    // các pack có format countdown khác với default
    private static readonly Dictionary<string, string> _specialCountdownLabelKeyMap = new() {
        { IAP_SummerPackConfig.PACK_NAME,    COUNT_DOWN_LOCALIZE_KEY },
        { IAP_HalloweenPackConfig.PACK_NAME, OFFER_ENDS_IN_COUNTDOWN_LOCALIZE_KEY },
        { IAP_ChristmasConfig.PACK_NAME,     OFFER_ENDS_IN_COUNTDOWN_LOCALIZE_KEY },
    };

    public enum PackShowPhase {
        PHASE1 = 0,
        PHASE2 = 1
    }
    
    public enum PackName {
        NONE,
        SUMMER,
        HALLOWEEN,
        CHRISTMAS,
        VALENTINE,
        EASTER,
    }
    #endregion
    
    #region FIELDS
    protected static string packName;
    protected static string packSavedKey;
    public static    string popupPath;
    
    public UnityEvent onEventDisable  = new();
    public UnityEvent onEventContinue = new();

    protected BaseSeasonalPackConfig config;
    #endregion
    
    #region PROPERTIES

    // ReSharper disable once InconsistentNaming
    public static BaseSeasonalPackConfig Config => instance.config;

    public bool isIAP =>
        config != null && !string.IsNullOrEmpty(config.PackId_Android_P2) && !string.IsNullOrEmpty(config.PackId_IOS_P2);
    
    public static int showPhase {
        get {
            return PlayerPrefs.GetInt(BuildKey(SHOW_PHASE_COUNT_POSTFIX), 0);
        }
        set {
            PlayerPrefs.SetInt(BuildKey(SHOW_PHASE_COUNT_POSTFIX), value);
        }
    }

    public static float durationInMinutes => showPhase == 0 ? Config.DurationInMinutes : Config.DurationInMinutes_P2;

    public static string packId {
        get {
            return Util.BuildString('_', packName, showPhase + 1);
        }
    }

    public static string GetCountdownLabel() {
        if (_specialCountdownLabelKeyMap == null || !_specialCountdownLabelKeyMap.ContainsKey(packName)) {
            return DEFAULT_ENDS_IN_LOCALIZE_KEY;
        }

        return _specialCountdownLabelKeyMap[packName];
    }

    public int GetAdsPurchasedMaxCount() {
        int maxCount = showPhase switch {
            0 => adsPricePhase1,
            1 => adsPricePhase2,
            _ => 0
        };
        return maxCount;
    }

    public static bool CheckShowIAPButton() {
        switch (showPhase) {
            case (int) PackShowPhase.PHASE1:
                return !string.IsNullOrEmpty(Config.PackPrice);
            case (int) PackShowPhase.PHASE2:
                return !string.IsNullOrEmpty(Config.PackPrice_P2);
            default:
                return false;
        }
    }

    public static (bool, bool) GetBuyButtonsState() {
        bool iapIsEnable = false;
        bool adsIsEnable = false;
        switch (showPhase) {
            case (int) PackShowPhase.PHASE1:
                iapIsEnable = !string.IsNullOrEmpty(Config.PackPrice);
                adsIsEnable = Config.PriceAdsPhase1 > 0;
                break;
            case (int) PackShowPhase.PHASE2:
                iapIsEnable = !string.IsNullOrEmpty(Config.PackPrice_P2);
                adsIsEnable = Config.PriceAdsPhase2 > 0;
                break;
        }

        return (iapIsEnable, adsIsEnable);
    }
    
    public static bool isEnable {
        get {
            return enableFeature
                   && !SubscriptionController.IsSubscriptionVip()
                   && !isPurchased;
        }
    }

    public static bool isEnableAndNotPurchased => enableFeature && !isPurchased;
    
    public static bool isEndEvent {
        get {
            if (!isInstanced) {
                return true;
            }
            if (Config.Phase2_IsEnable) {
                return showPhase > 1;
            }
            return showPhase > 0;
        }
    }

    public static string remainTimeKey => BuildKey(REMAIN_COUNTDOWN_TIME_KEY_POSTFIX);
    public static string elapsedTimeKey => BuildKey(ELAPSED_TIME_KEY_POSTFIX);
    public static string purchasedKey => BuildKey(PURCHASED_POSTFIX);
    public static bool isActive => PlayerPrefs.HasKey(remainTimeKey) && instance != null && instance.isQualified;
    public bool isQualified => config.CheckQualified();
    
    public bool isShowAdsButton => showPhase switch {
        (int) PackShowPhase.PHASE1 => adsPricePhase1 > 0,
        (int) PackShowPhase.PHASE2 => adsPricePhase2 > 0,
        _ => false
    };
    
    public int adsPricePhase1 => config.PriceAdsPhase1;
    public int adsPricePhase2 => config.PriceAdsPhase2;

    public int adsPrice {
        get {
            return showPhase switch {
                0 => adsPricePhase1,
                1 => adsPricePhase2,
                _ => 0
            };
        }
    }
    
    public static bool isPurchased => PlayerPrefs.GetInt(BuildKey(PURCHASED_POSTFIX), 0) == 1;

    public static int adsPurchasedCount {
        get {
            return PlayerPrefs.GetInt(BuildKey(ADS_PURCHASE_COUNT_POSTFIX));
        }
        set {
            PlayerPrefs.SetInt(BuildKey(ADS_PURCHASE_COUNT_POSTFIX), value);
        }
    }

    #endregion

    #region UNITY METHODS

    protected override void Awake() {
        base.Awake();
        if (instance == this) {
            DontDestroyOnLoad(gameObject);
        }
    }

    #endregion

    #region METHODS

    public static bool HandleTransactionAfterPending() {
        if (SubscriptionController.IsSubscriptionVip()) {
            return false;
        }
        
        string productIdP1 = IapBase.GetProductID(IAPDefinitionId.seasonal_pack_p1);
        string productIdP2 = IapBase.GetProductID(IAPDefinitionId.seasonal_pack_p2);
        
        if (NonSubPendingPurchaseManager.ContainsUnprocessed(productIdP1, productIdP2)) {
            SeasonalPackPopup.MakePurchasing();
            
            NonSubPendingPurchaseManager.RemoveUnprocessedPurchase(productIdP1, productIdP2);

            if (SeasonalPackPopup.isShowing) {
                SeasonalPackPopup.instancedPopup.Close();
            }
            
            // fire event để reload UI nếu user đang ở trong shop ball
            instance.onEventDisable?.Invoke();
            
            // event tracking
            int gems = Configuration.instance.GetDiamonds();
            EconomyIAPTracker.TrackIAP_PackPopupSuccess(
                packId,
                EconomyIAPTracker.TRACK_LOCATION.pending_purchase,
                string.Empty,
                0,
                gems, 
                gems,
                SeasonalPackPopup.timeElapsed);

            return true;
        }

        return false;
    }

    public static bool CheckUnprocessedPurchase() {
        string productIdP1 = IapBase.GetProductID(IAPDefinitionId.seasonal_pack_p1);
        string productIdP2 = IapBase.GetProductID(IAPDefinitionId.seasonal_pack_p2);
        return NonSubPendingPurchaseManager.ContainsUnprocessed(productIdP1, productIdP2);
    }

    public static void SetPackName(string name) {
        packName = name;
    }

    public static void SetPackKey(string key) {
        packSavedKey = key;
    }

    public void SetPopupPath(string popupName) {
        popupPath = Util.BuildString(null, popupName, config.ID);
    }

    public static void SetConfig(BaseSeasonalPackConfig config) {
        instanceSafe.config = config;
    }

    protected static string BuildKey(string key) {
        return Util.BuildString(null, packSavedKey, key);
    }

    #endregion

    #region Setup IAP seasonal pack

    public static bool enableFeature => _seasonalPackName != PackName.NONE;
    public static PackName seasonalPackName => _seasonalPackName;

    private static PackName _seasonalPackName = PackName.NONE;

    public static void SetupIapSeasonalPack() {
        string packNameFromRemote = RemoteConfigBase.instance.SeasonalPack_PackName;
        string config = RemoteConfigBase.instance.SeasonalPack_Config;

        if (string.IsNullOrEmpty(packNameFromRemote)) {
            return;
        }

        if (!Enum.TryParse(packNameFromRemote, true, out _seasonalPackName)) {
            CustomException.Fire("[SeasonalPack]", $"error on parsing PackName: {packNameFromRemote}");
            return;
        }

        switch (_seasonalPackName) {
            case PackName.SUMMER:
                if (ConvertSeasonalPackConfig<IAP_SummerPackConfig>(config)) {
                    instance.SetPopupPath(PopupName.IAPSummerPartyPack);
                } else {
                    _seasonalPackName = PackName.NONE;
                }
                break;

            case PackName.HALLOWEEN:
                if (ConvertSeasonalPackConfig<IAP_HalloweenPackConfig>(config)) {
                    instance.SetPopupPath(PopupName.IAPHalloweenPack);
                } else {
                    _seasonalPackName = PackName.NONE;
                }
                break;
            
            case PackName.CHRISTMAS:
                if (ConvertSeasonalPackConfig<IAP_ChristmasConfig>(config)) {
                    instance.SetPopupPath(PopupName.IAPChristmasPack);
                } else {
                    _seasonalPackName = PackName.NONE;
                }
                break;
            
            case PackName.VALENTINE:
                if (ConvertSeasonalPackConfig<IAP_ValentineConfig>(config)) {
                    instance.SetPopupPath(PopupName.IAPValentinePack);
                } else {
                    _seasonalPackName = PackName.NONE;
                }
                break;
            
            case PackName.EASTER:
                if (ConvertSeasonalPackConfig<IAP_EasterConfig>(config)) {
                    instance.SetPopupPath(PopupName.IAPEasterPack);
                } else {
                    _seasonalPackName = PackName.NONE;
                }
                break;
        }
    }
    
    private static bool ConvertSeasonalPackConfig<T>(string configString) where T : BaseSeasonalPackConfig, new() {
        T config = new();
        try {
            Logger.EditorLog($"[Seasonal][{typeof(T)}] convert config");
            JsonUtility.FromJsonOverwrite(configString, config);

            // set config and init manager
            SetConfig(config);

            // set pack name
            SetPackName(config.GetPackName());
            SetPackKey(config.GetPackKey());
        } catch (Exception exception) {
            CustomException.Fire($"[{typeof(T)}]", exception.Message);
            return false;
        }

        return true;
    }

    #endregion
}
