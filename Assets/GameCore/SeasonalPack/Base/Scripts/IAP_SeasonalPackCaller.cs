using System;
using System.Collections;
using TileHop.Cores.Pooling;
using TileHop.Cores.UserProgression;
using TileHop.EconomySystem;
using UnityEngine;
using UnityEngine.UI;

// ReSharper disable once InconsistentNaming
public class IAP_SeasonalPackCaller : MonoBehaviour {
    #region RESOURCE PATHS GETTER
    // các gói mới ưu tiên đưa lên switch case đầu => tối ưu switch case
    
    /// <summary>
    /// resource path of home entry point
    /// </summary>
    public static string buttonName {
        get {
            return SeasonalPackManager.seasonalPackName switch {
                SeasonalPackManager.PackName.EASTER => "Button/btnEasterPack",
                SeasonalPackManager.PackName.VALENTINE => "Button/btnValentinePack",
                SeasonalPackManager.PackName.CHRISTMAS => "Button/btnChristmasPack",
                SeasonalPackManager.PackName.HALLOWEEN => "Button/btnHalloweenPack",
                SeasonalPackManager.PackName.SUMMER => "Button/btnSummerPack",
                _ => string.Empty
            };
        }
    } 
    
    /// <summary>
    /// resource path of home entry point in UserProgression version
    /// </summary>
    public static string buttonNameUp {
        get {
            return SeasonalPackManager.seasonalPackName switch {
                SeasonalPackManager.PackName.EASTER => "Button/btnEasterPack_UP",
                SeasonalPackManager.PackName.VALENTINE => "Button/btnValentinePack_UP",
                SeasonalPackManager.PackName.CHRISTMAS => "Button/btnXmas_UP",
                SeasonalPackManager.PackName.HALLOWEEN => "Button/btnHalloweenPack_UP",
                SeasonalPackManager.PackName.SUMMER => "Button/btnSummerPack_UP",
                _ => string.Empty
            };
        }
    }

    /// <summary>
    /// resource path of item in shop IAP
    /// </summary>
    public static string shopItemName {
        get {
            return SeasonalPackManager.seasonalPackName switch {
                SeasonalPackManager.PackName.EASTER => "UI/ItemEasterPack",
                SeasonalPackManager.PackName.VALENTINE => "UI/ItemValentinePack",
                SeasonalPackManager.PackName.CHRISTMAS => "UI/ItemChristmasPack",
                SeasonalPackManager.PackName.HALLOWEEN => "UI/ItemHalloweenPack",
                SeasonalPackManager.PackName.SUMMER => "UI/ItemSummerParty",
                _ => string.Empty
            };
        }
    }
    
    /// <summary>
    /// resource path of item in shop IAP
    /// </summary>
    public static string shopRevampItemName {
        get {
            return SeasonalPackManager.seasonalPackName switch {
                SeasonalPackManager.PackName.EASTER => "UI/ItemEasterPack",
                SeasonalPackManager.PackName.VALENTINE => "UI/ItemValentinePack_Revamp",
                SeasonalPackManager.PackName.CHRISTMAS => "UI/ItemChristmasPack",
                SeasonalPackManager.PackName.HALLOWEEN => "UI/ItemHalloweenPack",
                SeasonalPackManager.PackName.SUMMER => "UI/ItemSummerParty",
                _ => string.Empty
            };
        }
    }

    /// <summary>
    /// resource path of special card in result screen's song list
    /// </summary>
    public static string itemCardName {
        get {
            return SeasonalPackManager.seasonalPackName switch {
                SeasonalPackManager.PackName.EASTER => "Button/IAP_EasterPackCard",
                SeasonalPackManager.PackName.VALENTINE => "Button/IAP_ValentinePackCard",
                SeasonalPackManager.PackName.CHRISTMAS => "Button/IAP_ChristmasPackCard",
                SeasonalPackManager.PackName.HALLOWEEN => "Button/IAP_HalloweenPackCard",
                SeasonalPackManager.PackName.SUMMER => "Button/IAP_SummerPackCard",
                _ => string.Empty
            };
        }
    }

    #endregion

    [SerializeField] protected Button button;
    [SerializeField] protected EconomyIAPTracker.TRACK_LOCATION location;
    [SerializeField] protected Text txtCountdown;
    
    [Space]
    [SerializeField] protected string timeLabelLocalizeKey;
    [SerializeField] protected string timeFormat;
    [SerializeField] protected bool   isLocalizeTimeFormat;
    
    protected Coroutine countdownCoroutine;

    #region UNITY EVENT FUNCTIONS

    private void Awake() {
        Setup();
        SubscriptionController.OnChange += HandleSubscription;
    }

    protected virtual void Start() {
        if (!SeasonalPackManager.isInstanced || !SeasonalPackManager.instanceSafe) {
            return;
        }
        
        SeasonalPackManager.instanceSafe.onEventContinue?.AddListener(HandleEventContinue);
        SeasonalPackManager.instanceSafe.onEventDisable?.AddListener(HandleEventDisable);
    }

    protected virtual void OnEnable() {
        HandleEventContinue();
    }

    protected virtual void OnDisable() {
        if (countdownCoroutine != null) {
            StopCoroutine(countdownCoroutine);
        }
    }

    private void OnDestroy() {
        SubscriptionController.OnChange -= HandleSubscription;
        if (!SeasonalPackManager.isInstanced) return;
        SeasonalPackManager.instanceSafe.onEventContinue.RemoveListener(HandleEventContinue);
        SeasonalPackManager.instanceSafe.onEventDisable.RemoveListener(HandleEventDisable);
    }

    #endregion
    
    protected virtual void Setup() {
        if (button == null) {
            TryGetComponent(out button);
        }
        button.onClick.AddListener(OnClick);
    }

    /// <summary>
    /// Handle when user become VIP while entry point is showing
    /// </summary>
    /// <param name="isBuySuccess"></param>
    private void HandleSubscription(bool isBuySuccess) {
        Destroy(gameObject);
    }
    
    private void HandleEventDisable() {
        Destroy(gameObject);
    }

    protected virtual void HandleEventContinue() {
        if (countdownCoroutine != null) {
            StopCoroutine(countdownCoroutine);
        }
        countdownCoroutine = StartCoroutine(IECountdownToEnd());
    }

    protected IEnumerator IECountdownToEnd() {
        var wait = YieldPool.GetWaitForSeconds(1f);
        TimeSpan remainTime;
        do {
            remainTime = SeasonalPackPopup.realtimeRemainningTime;

            string format = isLocalizeTimeFormat 
                ? LocalizationManager.instance.GetLocalizedValue(timeFormat) 
                : string.IsNullOrEmpty(timeFormat) ? Util.HHMMSS_TIME_FORMAT : timeFormat;

            if (!string.IsNullOrEmpty(timeLabelLocalizeKey)) {
                format = Util.BuildString(
                    null,
                    LocalizationManager.instance.GetLocalizedValue(timeLabelLocalizeKey),
                    format);   
            }

            txtCountdown.text = Util.GetLocalizeTimeString(remainTime, format);
            yield return wait;
        } while (remainTime.CompareTo(TimeSpan.Zero) >= 0);
    }
    
    public virtual void OnClick() {
        SoundManager.PlayGameButton();
        SeasonalPackPopup.ShowPopup(false, location);

        // fire event
        EconomyIAPTracker.TrackIAP_PackEntryClick(
            SeasonalPackManager.packId, 
            location, 
            Configuration.instance.GetDiamonds(), 
            SeasonalPackPopup.timeElapsed);
    }
}
