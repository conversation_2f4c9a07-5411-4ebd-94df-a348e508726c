using System;
using System.Collections;
using System.Text;
using DG.Tweening;
using TileHop.Cores.Pooling;
using TileHop.EconomySystem;
using UnityEngine;
using UnityEngine.UI;
using Random = UnityEngine.Random;

public abstract class SeasonalPackPopup : PopupUI {
    [SerializeField] protected Button btnClose;
    [SerializeField] protected Button btnAdsPurchase;

    [Header("Animate Open/Close")]
    [SerializeField] protected float animatedDuration;
    [SerializeField] protected Transform[] animatedTransforms;
    protected                  Tweener[]   animatedTweeners;
    protected                  int         animatedCount;
    private                    Canvas      _mainCanvas;
    private                    Vector3     _initButtonGroupScale = Vector3.one;

    [Header("Content")] [SerializeField] protected Text       countDownText;
    [SerializeField]                     protected Text       reviveTimeText;
    [SerializeField]                     protected Transform  tfGroupButton;
    [SerializeField]                     protected GameObject IAP_BuyButton;
    [SerializeField]                     protected GameObject LabelBetweenButtons;
    [SerializeField]                     protected GameObject Ads_BuyButton;
    [SerializeField]                     protected Text       Ads_CountText;

    protected static TimeSpan          lastRemainTime;
    protected static TimeSpan          elapsedTime;
    protected static bool              isLoadedData     = false;
    protected static int               showAtHomeCount  = 0;
    protected static float             phaseChangedTime = 0f;
    protected static bool              isEndCountdown   = false;
    protected static bool              isInCountdown    = false;
    protected static string            countdownLabel;
    private static   bool              _isInited = false;
    private static   SeasonalPackPopup _instancedPopup;

    public         EconomyIAPTracker.TRACK_LOCATION location;
    public static SeasonalPackPopup instancedPopup => _instancedPopup;

    public static bool isShowing {
        get {
            if (_instancedPopup == null)
                return false;

            return _instancedPopup._mainCanvas
                ? _instancedPopup._mainCanvas.enabled
                : _instancedPopup.gameObject.activeInHierarchy;
        }
    }
    
    public static TimeSpan realtimeRemainningTime =>
        lastRemainTime - TimeSpan.FromSeconds(TimeManager.TimeIngameIgnorePause - phaseChangedTime);

    public static float timeElapsed =>
        (float) (elapsedTime + TimeSpan.FromSeconds(TimeManager.TimeIngameIgnorePause - phaseChangedTime))
        .TotalSeconds;

    #region UNITY EVENT FUNCTIONS

    protected virtual void Awake() {
        btnClose.onClick.AddListener(Close);
        btnAdsPurchase.onClick.AddListener(AdsPurchase);

        TryGetComponent(out _mainCanvas);
        SetupAppearAnimation();

        if (LocalizationManager.instance != null) {
            LocalizationManager.instance.OnLanguageChange += LocalizedFormattersOnLanguageChanged;
            LocalizedFormattersOnLanguageChanged();
        }

        if (tfGroupButton != null) {
            _initButtonGroupScale = tfGroupButton.localScale;
        }
    }

    protected override void OnEnable() {
        base.OnEnable();

        for (int i = 0; i < animatedCount; i++) {
            animatedTweeners[i].PlayForward();
        }
    }

    protected virtual void OnDestroy() {
        if (LocalizationManager.instance != null) {
            LocalizationManager.instance.OnLanguageChange -= LocalizedFormattersOnLanguageChanged;
        }

        for (int i = 0; i < animatedCount; i++) {
            animatedTweeners[i].Kill();
        }
    }

    #endregion
    
    #region OVERRIDE PopupUI

    public virtual void ForceClose() {
        Close();
    }
    
    public override void Close() {
        if (Ads_BuyButton) {
            Ads_BuyButton.SetActive(false);
        }

        IAP_BuyButton.SetActive(false);

        if (LabelBetweenButtons) {
            LabelBetweenButtons.SetActive(false);
        }

        countDownText.gameObject.SetActive(false);

        for (int i = 0; i < animatedCount; i++) {
            animatedTweeners[i].PlayBackwards();
        }

        onStartClose?.Invoke();
        EventEscapeManager.Pop(this);
        StartCoroutine(IEClose());

        //event tracking
        EconomyIAPTracker.TrackIAP_PackPopupClose(SeasonalPackManager.packId, 0, Configuration.instance.GetDiamonds(),
            timeElapsed);
    }

    protected IEnumerator IEClose() {
        btnClose.interactable = false;
        yield return YieldPool.GetWaitForSeconds(animatedDuration);

        if (destroyOnClose) {
            Destroy(gameObject);
        } else if (_mainCanvas != null) {
            _mainCanvas.enabled = false;
        } else {
            gameObject.SetActive(false);
        }
    }

    public override void Open() {
        if (_mainCanvas) {
            _mainCanvas.enabled = true;
            OnEnable();
        } else {
            base.Open();
        }

        HandleOnOpen();
    }

    protected void HandleOnOpen() {
        btnClose.interactable = true;
        countDownText.gameObject.SetActive(true);
        countDownText.text = string.Empty;
        UpdateAdsCountText(SeasonalPackManager.adsPurchasedCount); 
    }

    protected virtual void CustomOpen(bool isAutoShow) {
        Open();
    }

    #endregion

    public static void Init() {
        if (_isInited)
            return;

        _isInited = true;

        TimeManager.saveDataOnPauseQuit += SaveData;
    }

    protected virtual void SetupAppearAnimation() {
        animatedCount = animatedTransforms.Length;
        animatedTweeners = new Tweener[animatedCount];

        // setup animated objects
        for (int i = 0; i < animatedCount; i++) {
            int index = i;
            animatedTweeners[index] = DOTween.To(
                x => animatedTransforms[index].localScale = Vector3.one * x,
                0.0f, 
                1.0f, 
                animatedDuration).SetAutoKill(false).SetEase(Ease.OutBack).SetId(index).Pause();
        }
    }

    protected static IEnumerator IECountdownCoroutine() {
        if (SeasonalPackManager.durationInMinutes == 0) {
            yield break;
        }

        isInCountdown = true;
        isEndCountdown = false;
        TimeSpan remainTime;
        StringBuilder stringBuilder = new();
        var wait = YieldPool.GetWaitForSeconds(1f);
        countdownLabel = LocalizationManager.instance.GetLocalizedValue(SeasonalPackManager.GetCountdownLabel());
        do {
            remainTime = realtimeRemainningTime;
            if (isShowing && _instancedPopup.countDownText != null) {
                stringBuilder.Clear();
                stringBuilder.Append(countdownLabel);
                stringBuilder.Append(Util.GetLocalizeTimeString(remainTime));
                _instancedPopup.countDownText.text = stringBuilder.ToString();
            }

            yield return wait;
        } while (remainTime.CompareTo(TimeSpan.Zero) > 0);

        isInCountdown = false;
        EndPhase();
    }

    /// <summary>
    /// kết thúc event khi hết thời gian, user chưa mua pack
    /// </summary>
    public static void EndPhase() {
        // tracking với phase 0 và 1
        EconomyIAPTracker.TrackIAP_PackDisable(SeasonalPackManager.packId, timeElapsed);

        isEndCountdown = true;
        SeasonalPackManager.showPhase++;
        PlayerPrefs.DeleteKey(SeasonalPackManager.remainTimeKey);

        // đặt showCount = 2 để auto show phase_2 lần đầu khi về home
        showAtHomeCount = 2;

        if (SeasonalPackManager.isEndEvent) {
            if (_instancedPopup)
                _instancedPopup.Close();
            SeasonalPackManager.instanceSafe.onEventDisable?.Invoke();
        } else {
            // nếu phase sau khi tăng ShowPhase là phase ads (phase 2), giá ads tương ứng > 0 và bật config
            if (CheckBeingInLastPhase()) {
                phaseChangedTime = TimeManager.TimeIngameIgnorePause;
                elapsedTime = TimeSpan.Zero;
                if (isShowing) {
                    // auto=false để bỏ qua điều kiện 'không auto show 2 lần liên tiếp ở home'
                    ShowPopup(false, instancedPopup.location);
                } else {
                    CheckEndOfTime();
                }

                SeasonalPackManager.instanceSafe.onEventContinue?.Invoke();
                return;
            }

            SeasonalPackManager.instanceSafe.onEventDisable?.Invoke();
        }

    }

    private static bool CheckBeingInLastPhase() {
        return SeasonalPackManager.Config.Phase2_IsEnable && SeasonalPackManager.Config.PriceAdsPhase2 > 0 &&
               SeasonalPackManager.showPhase == (int) SeasonalPackManager.PackShowPhase.PHASE2;
    }

    protected virtual void SetupUI(bool isIAP) {
        if (IAP_BuyButton) {
            IAP_BuyButton.SetActive(isIAP);
        }

        bool isAds = SeasonalPackManager.instanceSafe.isShowAdsButton;

        if (Ads_BuyButton) {
            Ads_BuyButton.SetActive(isAds);
        }

        if (LabelBetweenButtons) {
            LabelBetweenButtons.SetActive(isIAP && isAds);
        }

        // scale up if only one button is shown
        if (tfGroupButton != null) {
            tfGroupButton.localScale = _initButtonGroupScale / (Ads_BuyButton.activeSelf? 1.35f : 1f);
        }
    }

    /// <summary>
    /// Check xem đã hết thời gian đếm ngược chưa trước khi show popup
    /// </summary>
    /// <returns></returns>
    public static bool CheckEndOfTime() {
        // setup countdown, isActive = true khi đã setup 'remain time'
        if (SeasonalPackManager.isActive) {
            if (SeasonalPackManager.showPhase > 1)
                return false;

            if (!isLoadedData) {
                isLoadedData = true;
                string remainTimeString = PlayerPrefs.GetString(SeasonalPackManager.remainTimeKey);
                if (!TimeSpan.TryParse(remainTimeString, out lastRemainTime)) {
                    return false;
                }

                TimeSpan.TryParse(PlayerPrefs.GetString(SeasonalPackManager.elapsedTimeKey), out elapsedTime);
            }

            if (SeasonalPackManager.durationInMinutes == 0) {
                return true;
            }

            float phaseDuration = SeasonalPackManager.durationInMinutes;
            if (lastRemainTime.TotalMinutes > SeasonalPackManager.durationInMinutes) {
                lastRemainTime = TimeSpan.FromMinutes(phaseDuration);
                return true;
            }

            if (realtimeRemainningTime.CompareTo(TimeSpan.Zero) > 0) {
                return true;
            }
        }

        // lần đầu hiện popup
        lastRemainTime = TimeSpan.FromMinutes(SeasonalPackManager.durationInMinutes);
        elapsedTime = TimeSpan.Zero;
        phaseChangedTime = TimeManager.TimeIngameIgnorePause;
        PlayerPrefs.SetString(SeasonalPackManager.remainTimeKey, lastRemainTime.ToString());
        PlayerPrefs.SetString(SeasonalPackManager.elapsedTimeKey, elapsedTime.ToString());

        // fire 1 lần duy nhất tương ứng với mỗi phase 
        EconomyIAPTracker.TrackIAP_PackEnable(SeasonalPackManager.packId);
        return true;
    }

    /// <summary href="https://amanotesjsc.atlassian.net/wiki/spaces/THE/pages/3666903041/Copy+of+Proposal+-+BH+summer+cosmetic+IAP">
    /// TH-3070: Gọi popup thông qua method này, khi user đủ điều kiện:
    /// 1- song_result >= 3
    /// 2- không phải VIP user
    /// 3- đã hết thời gian đếm ngược, config hiện tiếp phase Ads được bật
    ///    và có config price-ads > 0 (có thể mua bằng ads)
    ///
    /// TH1: chưa kích hoạt pack tại thời điểm dùng thử VIP:
    ///  + Kết thúc VIP => kích hoạt pack, tại entry point auto show ở home
    /// 
    /// TH2: dùng thử VIP trong thời gian đếm ngược pack:
    ///  + Thời gian đếm ngược khong tính thời gian đang là VIP
    /// </summary>
    public static void ShowPopup(bool isAuto, EconomyIAPTracker.TRACK_LOCATION location) {

        if (!SeasonalPackManager.enableFeature || !SeasonalPackManager.isInstanced) {
            return;
        }

        if (!SeasonalPackManager.Config.CheckQualified()) {
            return;
        }

        if (SubscriptionController.IsSubscriptionVip()) {
            return;
        }

        if (!CheckEndOfTime()) {
            return;
        }

        Init();

        if (isAuto && Util.IsHomeScene()) {
            showAtHomeCount++;
        }

        // khoong auto-show 2 lần liên tiếp khi về home
        if (isAuto && showAtHomeCount % 2 == 0)
            return;

        if (!_instancedPopup) {
            Util.ShowPopUpCanvasHeight(SeasonalPackManager.popupPath)?.TryGetComponent(out _instancedPopup);
            if (_instancedPopup == null) {
                return;
            }
        }

        _instancedPopup.CustomOpen(isAuto);
        _instancedPopup.SetupUI(SeasonalPackManager.CheckShowIAPButton());
        _instancedPopup.location = location;

        if (!isInCountdown || isEndCountdown) {
            SeasonalPackManager.instanceSafe.StartCoroutine(IECountdownCoroutine());
        }

        // fire event tracking
        EconomyIAPTracker.TrackIAP_PackPopupShow(SeasonalPackManager.packId, location,
            Configuration.instance.GetDiamonds(), timeElapsed);
    }

    private static void SaveData() {
        // nếu đã đăng kí VIP từ session trước
        if (SubscriptionController.IsSubscriptionVip() && TimeManager.timeStartSubscriptionVIP == 0)
            return;

        // TH1: đăng kí VIP trong session hiện tại
        // TH2: hủy VIP ở session hiện tại
        float timeInApp = SubscriptionController.IsSubscriptionVip()
            ? Mathf.Max(TimeManager.timeStartSubscriptionVIP - phaseChangedTime, 0f)
            : TimeManager.TimeIngameIgnorePause - phaseChangedTime -
              (TimeManager.timeEndSubscriptionVIP - TimeManager.timeStartSubscriptionVIP);

        // save remainning time
        if (SeasonalPackManager.isEndEvent) {
            PlayerPrefs.DeleteKey(SeasonalPackManager.remainTimeKey);
        } else {
            if (SeasonalPackManager.durationInMinutes > 0) {
                PlayerPrefs.SetString(SeasonalPackManager.remainTimeKey,
                    (lastRemainTime - TimeSpan.FromSeconds(timeInApp)).ToString());
            } else {
                PlayerPrefs.SetString(SeasonalPackManager.remainTimeKey, TimeSpan.Zero.ToString());
            }

            PlayerPrefs.SetString(SeasonalPackManager.elapsedTimeKey,
                (elapsedTime + TimeSpan.FromSeconds(timeInApp)).ToString());
        }
    }

    #region Purchase

    public void AdsPurchase() {
        string adsLocation = Util.BuildString('_', "iap", SeasonalPackManager.packId);
        AnalyticHelper.LogVideoAds(AD_STATE.click, VIDEOREWARD.None, null, adsLocation);

        int currentGems = Configuration.instance.GetDiamonds();
        EconomyIAPTracker.TrackIAP_PackPopupClick(SeasonalPackManager.packId, location, "ads", currentGems, currentGems,
            timeElapsed);

        AdsManager.instance.ShowRewardAds(VIDEOREWARD.None, null, adsLocation, true, isCompleted => {
            if (isCompleted) {
                int lastCount = SeasonalPackManager.adsPurchasedCount;
                SeasonalPackManager.adsPurchasedCount = ++lastCount;

                if (lastCount >= SeasonalPackManager.instanceSafe.adsPrice) {
                    // update button text
                    Ads_BuyButton.SetActive(false);
                    LabelBetweenButtons.SetActive(false);

                    MakePurchasing();

                    // event tracking
                    EconomyIAPTracker.TrackIAP_PackPopupSuccess(SeasonalPackManager.packId, instancedPopup.location,
                        "ads", 0, currentGems, currentGems, timeElapsed);

                    // TOAST: mua bằng button IAP thành công thì đã có toast thông báo, mua bằng ads thì chưa có toast
                    // => hiển thị toast khi mua thành công bằng ads
                    FadeToast.ShowFadeLocalizedToast("PURCHASED_SUCCESSFULLY", "PLEASE_ENJOY");
                } else {
                    // update button text
                    UpdateAdsCountText(lastCount);
                }
            }
        }, isAutoRewardWhenAdsNotReady: false);
    }

    /// <summary>
    /// Sử dụng cho nút mua bằng ads trực tiếp ở ngoài popup
    /// </summary>
    /// <param name="onUpdateUI">param: bool isPurchaseCompleted, string adsCountString</param>
    /// <param name="location">location của button ads</param>
    public static void AdsPurchaseOutsidePopup(Action<bool, string> onUpdateUI,
                                               EconomyIAPTracker.TRACK_LOCATION location) {
        string adsLocation = Util.BuildString('_', "iap", SeasonalPackManager.packId);
        AnalyticHelper.LogVideoAds(AD_STATE.click, VIDEOREWARD.None, null, adsLocation);

        int currentGems = Configuration.instance.GetDiamonds();
        EconomyIAPTracker.TrackIAP_PackPopupClick(SeasonalPackManager.packId, location, "ads", currentGems, currentGems,
            timeElapsed);

        AdsManager.instance.ShowRewardAds(VIDEOREWARD.None, null, adsLocation, true, isCompleted => {
            if (isCompleted) {
                int lastCount = SeasonalPackManager.adsPurchasedCount;
                SeasonalPackManager.adsPurchasedCount = ++lastCount;

                if (lastCount >= SeasonalPackManager.instanceSafe.adsPrice) {
                    onUpdateUI?.Invoke(true, null);

                    MakePurchasing();

                    // event tracking
                    EconomyIAPTracker.TrackIAP_PackPopupSuccess(SeasonalPackManager.packId, location, "ads", 0,
                        currentGems, currentGems, timeElapsed);

                    FadeToast.ShowFadeLocalizedToast("PURCHASED_SUCCESSFULLY", "PLEASE_ENJOY");
                } else {
                    onUpdateUI?.Invoke(false, GetAdsCountString(lastCount));
                }
            }
        }, isAutoRewardWhenAdsNotReady: false);
    }

    /// <summary>
    /// Xử lý mua pack thành công, bằng price hoặc ads
    /// </summary>
    public static void MakePurchasing() {
        if (SubscriptionController.IsSubscriptionVip()) {
            return;
        }

        if (PlayerPrefs.HasKey(SeasonalPackManager.purchasedKey)) {
            return;
        }

        PlayerPrefs.SetInt(SeasonalPackManager.purchasedKey, 1);
        PlayerPrefs.DeleteKey(SeasonalPackManager.remainTimeKey);
        if (_instancedPopup) {
            _instancedPopup.Close();
        }

        #region Trả thưởng sau khi mua Package

        // thêm thời gian revive không giới hạn
        UnlimitedReviveManager.IncreaseUnlimitedReviveTime(SeasonalPackManager.Config.UnlimitedReviveTime);

        // update gems if needed
        if (SeasonalPackManager.Config.GemsReward > 0) {
            Configuration.UpdateDiamond(SeasonalPackManager.Config.GemsReward, CurrencyEarnSource.seasonal_pack.ToString());
        }

        string packId = SeasonalPackManager.packId;

        // trả bóng cho người chơi
        foreach (var ballRewardId in SeasonalPackManager.Config.BallRewards) {
            Configuration.SetOpenBall(ballRewardId, 0, location: packId, true, false);
        }

        // equip cho user ngẫu nhiên một trong các bóng
        Configuration.SetSelectedBall(
            SeasonalPackManager.Config.BallRewards[Random.Range(0, SeasonalPackManager.Config.BallRewards.Length)],
            isForce: true, location: packId);

        // trả thưởng songs nếu có trong config
        if (!string.IsNullOrEmpty(SeasonalPackManager.Config.SongRewardAcmId)) {
            Song song = SongManager.instance.GetSongByAcmId(SeasonalPackManager.Config.SongRewardAcmId);

            if (song != null) {
                Configuration.instance.SetOpenSong(song, 0, true, SongUnlockType.seasonal_pack);

                // remove khỏi danh sách hidden songs và show trong songlist
                SongManager.instance.RemoveSongInHiddenList(song);
            }
        }

        #endregion

        // tắt các element khác: home button, shop item, ...
        SeasonalPackManager.instanceSafe.onEventDisable?.Invoke();
        EconomyIAPTracker.TrackIAP_PackDisable(packId, timeElapsed);
    }

    private void UpdateAdsCountText(int count) {
        Ads_CountText.text = GetAdsCountString(count);
    }

    public static string GetAdsCountString(int count) {
        int maxCount = 0;
        switch (SeasonalPackManager.showPhase) {
            case 0:
                maxCount = SeasonalPackManager.Config.PriceAdsPhase1;
                break;

            case 1:
                maxCount = SeasonalPackManager.Config.PriceAdsPhase2;
                break;
        }

        return Util.BuildString("/", count, maxCount);
    }

    /// <summary>
    /// Refresh countdown text with localization
    /// </summary>
    protected virtual void LocalizedFormattersOnLanguageChanged() {
        if (reviveTimeText != null) {
            reviveTimeText.text = string.Format(
                LocalizationManager.instance.GetLocalizedValue(SeasonalPackManager.REVIVE_TIME_FORMAT_LOCALIZE_KEY),
                SeasonalPackManager.Config.UnlimitedReviveTime);   
        }

        countdownLabel = LocalizationManager.instance.GetLocalizedValue(SeasonalPackManager.GetCountdownLabel());
    }

    #endregion
}