using System;
using System.Collections.Generic;
using UnityEngine;

[Serializable]
public class MoreGames {
    public List<Game> games;

    public MoreGames(List<Game> list) {
        games = list;
    }

    [Serializable]
    public class Game {
        public string nameGame;
        public string id;
        public string urlIcon;
        public string urlThumb;
        public string urlLinkGame;
        public string urlVideo;

        public Sprite GetLocalIcon() {
            Sprite sprite = Resources.Load<Sprite>(ResourcesPath.MoreGames_Icon + id);
            return sprite;
        }
        
        public Sprite GetLocalThumb() {
            Sprite sprite = Resources.Load<Sprite>(ResourcesPath.MoreGames_Thumb + id);
            return sprite;
        }
    }

    public static List<Game> Get() {
        return null;
        //MoreGames games = JsonUtility.FromJson<MoreGames>(moreGames);
        //return games.games;
    }
}