using System.Collections;
using System.Collections.Generic;
using Assets.SimpleSlider.Scripts;
using GameCore;
using UnityEngine;

public class ItemsSlideScript : MonoBehaviour {
    #region Fields

    [Header("Config")] [SerializeField] private bool isItemFullWith = true;
    [SerializeField] private RectTransform rtfImages;
    [SerializeField] private RectTransform rtfPagination;
    [SerializeField] private HorizontalScrollSnap horizontalScrollSnap;
    [SerializeField] private PremiumFormScript premiumFormScript;

    [SerializeField] private ItemSlideScript objItemSlide;
    [SerializeField] private GameObject objPage;

    //private
    float _widthItem;

    private List<ItemSlideScript> _lstItemSlide;

    #endregion

    #region Unity Method

    private Coroutine _loadPromotionGame;

    private void Awake() {
        List<MoreGames.Game> games = MoreGames.Get();

        objItemSlide.SetActive(false);
        objPage.SetActive(false);

        _lstItemSlide = new List<ItemSlideScript>();
        for (int index = 0; index < games.Count; index++) {
            //init item slide
            ItemSlideScript itemSlideScript = Instantiate(objItemSlide, rtfImages);
            itemSlideScript.SetActive(true);
            MoreGames.Game game = games[index];
            itemSlideScript.Init(this, game);
            itemSlideScript.index = index;
            itemSlideScript.onClick = () => {
                //if (Configuration.IsNoAds()) { // user is VIP
                if (itemSlideScript != ItemSlideScript.currentItem) {
                    Debug.Log("Load promotion game: " + itemSlideScript.gameData.urlLinkGame);
                    
                    AnalyticHelper.Button_Click(BUTTON_NAME.VipGameClick.ToString(),itemSlideScript.gameData.nameGame);
                    
                    if (ItemSlideScript.currentItem != null) {
                        ItemSlideScript.currentItem.SetHighlight(false);
                    }

                    itemSlideScript.SetHighlight(true);
                    premiumFormScript.ShowSubscription(false);

                    if (_loadPromotionGame != null) {
                        StopCoroutine(_loadPromotionGame);
                    }

                    _loadPromotionGame = StartCoroutine(premiumFormScript.LoadPromotionGame(itemSlideScript));
                    horizontalScrollSnap.StopAutoSwitch();
                }

                //}
            };

            //init item page
            GameObject page = Instantiate(objPage, rtfPagination);
            page.name = index.ToString();
            page.SetActive(true);

            //add to list
            _lstItemSlide.Add(itemSlideScript);
        }

        Destroy(objItemSlide.gameObject);
        Destroy(objPage.gameObject);
    }

    private IEnumerator Start() {
        yield return null;

        SetupItemSlide();
        horizontalScrollSnap.Initialize();
    }

    #endregion

    #region Method

    private void SetupItemSlide() {
        Canvas canvas = GetComponentInParent<Canvas>();
        RectTransform rectTransform = canvas.GetComponent<RectTransform>();
        _widthItem = rectTransform.sizeDelta.x;

        ((RectTransform) transform).SetSizeDeltaX(_widthItem);
        rtfImages.SetSizeDeltaX(_widthItem);

        if (isItemFullWith) {
            List<Transform> tfChilds = rtfImages.GetChilds();
            foreach (Transform tfChild in tfChilds) {
                RectTransform rtfChild = (RectTransform) tfChild;
                rtfChild.SetSizeDeltaX(_widthItem);
            }
        }
    }

    #endregion

    public IEnumerator PlayFirstPromotionGame() {
        while (_lstItemSlide == null || _lstItemSlide.Count == 0) {
            yield return null;
        }

        _lstItemSlide[0].onClick?.Invoke();
        horizontalScrollSnap.SetPage(0, true);
    }

    public void PlayNextPromotionGame() {
        int nextIndex = ItemSlideScript.currentItem.index + 1;
        if (nextIndex > _lstItemSlide.Count - 1) {
            nextIndex = 0;
        }
    
        foreach (ItemSlideScript slideScript in _lstItemSlide) {
            if (slideScript.index == nextIndex) {
                slideScript.onClick?.Invoke();
                horizontalScrollSnap.SetPage(nextIndex, true);
                break;
            }
        }
    }
}