using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;

public class UIAdminPopup_SpecialTileV2 : PopupUI {
    [SerializeField] private Toggle toggleOptionPrefab;

    [Space]
    [SerializeField] private Text txtValue;

    [SerializeField] private Button btnPlay;
    [SerializeField] private Button btnClose;

    private Song _song;

    private List<Toggle> _listOptionSection = new List<Toggle>();

    private List<NoteElementType> _allSpecialSection;

    #region Unity Medhods

    private void Awake() {
        _allSpecialSection = new List<NoteElementType>() {
            NoteElementType.LongTileBreak,
            NoteElementType.FadeOut,
            NoteElementType.Teleport,
            NoteElementType.FakeConveyor,
            NoteElementType.MovingCircle,
            NoteElementType.SpecialMirror,
            NoteElementType.SpecialHyperBoost,
        };
    }

    private void Start() {
        btnPlay.onClick.AddListener(OnBtnPlayClicked);
        btnClose.onClick.AddListener(OnBtnCloseClicked);
        UpdateUI();
    }

    #endregion

    public void Show(Song song) {
        this._song = song;
    }

    private void UpdateUI() {
        var list = Configuration.GetListAutoGenElements();
        txtValue.text = string.Join(";", list);
        for (int i = 0; i < _allSpecialSection.Count; i++) {
            var option = Instantiate(toggleOptionPrefab, toggleOptionPrefab.transform.parent);
            option.transform.SetSiblingIndex(i);
            option.gameObject.SetActive(true);
            option.GetComponentInChildren<Text>().text =
                $"{(int) _allSpecialSection[i]} {_allSpecialSection[i].ToString()}";
            option.isOn = list.Contains(_allSpecialSection[i]);
            _listOptionSection.Add(option);
            option.onValueChanged.AddListener(OnChangeValue);
        }
    }

    private void OnChangeValue(bool isOn) {
        List<NoteElementType> current = new List<NoteElementType>();
        for (int i = 0; i < _listOptionSection.Count; i++) {
            if (_listOptionSection[i].isOn)
                current.Add(_allSpecialSection[i]);
        }

        txtValue.text = string.Join(";", current);
    }

    private void OnBtnPlayClicked() {
        HashSet<NoteElementType> current = new HashSet<NoteElementType>();
        for (int i = 0; i < _listOptionSection.Count; i++) {
            if (_listOptionSection[i].isOn)
                current.Add(_allSpecialSection[i]);
        }

        Configuration.SetListAutoGenElements(current);
        RemoteConfig.instance.NewElements_Enable = true;
        RemoteConfig.instance.NewElements_AutoGen_List = string.Join(";", current);
        if (NotesManager.instance) {
            NotesManager.instance.UpdateListAutoGen(current);
        }
        //go to play
        Util.GoToGamePlay(_song, LOCATION_NAME.devInfo.ToString(), isSongClick: true);
    }

    private void OnBtnCloseClicked() {
        this.Close();
    }
}