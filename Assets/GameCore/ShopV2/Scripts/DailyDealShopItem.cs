using System.Collections;
using TileHop.Cores.Pooling;
using UnityEngine;
using UnityEngine.UI;

public class DailyDealShopItem : MonoBehaviour {
    public static readonly   string                  assetName = "UI/ItemDailyDeal";
    [SerializeField] private UIItemRewardDailyDeal[] itemRewards;
    [SerializeField] private Image[]                 lineRewards;

    [SerializeField] private Button     btnFree;
    [SerializeField] private Text       txtFree;
    [SerializeField] private Button     btnAds;
    [SerializeField] private Text       txtAds;
    [SerializeField] private GameObject objCooldown;
    [SerializeField] private Text       txtCooldown;
    [SerializeField] private Text       txtTimeCooldown;

    private Shop _shop;
    private void Start() {
        if (itemRewards != null)
        {
            for (int i = 0, count = itemRewards.Length; i < count; i++)
            {
                itemRewards[i].Init(DailyDealShop.instanceSafe._rewardValues[i]);
            }
        }

        if (btnFree != null) btnFree.onClick.AddListener(OnBtnFreeClicked);
        if (btnAds != null) btnAds.onClick.AddListener(OnBtnAdsClicked);
    }

    private void OnEnable() {
        DailyDealShop.OnChangeState += UpdateUIState;
        UpdateUIState();
    }

    private void OnDisable() {
        DailyDealShop.OnChangeState -= UpdateUIState;
    }

    public void Init(Shop shop) {
        _shop = shop;
    }

    private void UpdateUIState() {
        int state = DailyDealShop.instanceSafe.GetState();
        int idReward = DailyDealShop.instanceSafe.idReward;
        switch (state) {
            case -1: //cooldown
                btnFree.gameObject.SetActive(false);
                btnAds.gameObject.SetActive(false);
                objCooldown.SetActive(true);
                StartCoroutine(IECountTimeDown());
                break;

            case 0: //free reward
                btnFree.gameObject.SetActive(true);
                btnAds.gameObject.SetActive(false);
                objCooldown.SetActive(false);
                break;

            default:
                btnFree.gameObject.SetActive(false);
                btnAds.gameObject.SetActive(true);
                objCooldown.SetActive(false);
                if (state == 1) {
                    txtAds.text = LocalizationManager.instance.GetLocalizedValue("FREE");
                } else {
                    txtAds.text =
                        $"{LocalizationManager.instance.GetLocalizedValue("FREE")}\n{DailyDealShop.instanceSafe.GetStateAds()}";
                }

                break;
        }

        for (int i = 0; i < itemRewards.Length; i++) {
            if (i > idReward) {
                itemRewards[i].SetState(ItemState.LOCK);
            } else if (i == idReward) {
                itemRewards[i].SetState(ItemState.OPEN);
            } else {
                itemRewards[i].SetState(ItemState.RECEIVED);
            }
        }

        for (int i = 0; i < lineRewards.Length; i++) {
            lineRewards[i].color = i < idReward ? new Color32(102, 1, 231, 255) : Color.gray;
        }
    }

    private void OnBtnAdsClicked() {
        SoundManager.PlayGameButton();
        AdsManager.instance.ShowRewardAds(VIDEOREWARD.currency, null,
            location: LOCATION_NAME.free_gem_iap_shop.ToString(), true, result => {
                if (result) {
                    bool hasReward = DailyDealShop.instanceSafe.IncreaseAds();
                    if (hasReward) {
                        GetReward();
                    } else {
                        txtAds.text =
                            $"{LocalizationManager.instance.GetLocalizedValue("FREE")}\n{DailyDealShop.instanceSafe.GetStateAds()}";
                    }
                }
            });
    }

    private void OnBtnFreeClicked() {
        SoundManager.PlayGameButton();
        GetReward();
    }

    private void GetReward() {
        int amount = DailyDealShop.instanceSafe.GetRewardValue();
        if (amount <= 0)
            return;

        if (_shop.diamondIcon != null) {
            UIOverlay.instance.AddDiamondsV2(btnAds.transform.RootLocalPos(), _shop.diamondIcon.RootLocalPos(), amount,
                DailyDealShop.instanceSafe.GetReward);
        } else {
            DailyDealShop.instanceSafe.GetReward();
        }
    }

    private IEnumerator IECountTimeDown() {
        float remainSecond = DailyDealShop.instanceSafe.GetRemainTime();
        var wait = YieldPool.GetWaitForSeconds(1f);
        do {
            var remainTime = DailyDealShop.instanceSafe.remainTime;
            txtTimeCooldown.text = string.Format("{0:00} : {1:00} : {2:00}", remainTime.Hours, remainTime.Minutes,
                remainTime.Seconds);
            yield return wait;

            remainSecond -= 1;
        } while (remainSecond > 0);

        UpdateUIState();
    }
}