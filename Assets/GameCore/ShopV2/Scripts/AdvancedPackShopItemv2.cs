using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using TileHop.Cores.Boosters;
using UnityEngine;
using UnityEngine.UI;

public class AdvancedPackShopItemv2 : AdvancedPackShopItem {
    [Header("Ver 2")]
    [SerializeField] private Text txtDiamondBonus;

    [SerializeField] private Transform itemReviveBonus;
    [SerializeField] private Text      txtReviveBonus;
    [SerializeField] private Image     imgBooster;

    protected override void OnEnable() {
        base.OnEnable();
        try {
            boosterRewards =
                JsonConvert.DeserializeObject<List<BoosterReward>>(RemoteConfigBase.instance.ShopAdvancedPack_Booster);
        } catch (Exception e) {
            Logger.LogError($"Cant convert data : {RemoteConfigBase.instance.ShopAdvancedPack_Booster}");
        }

        txtDiamondBonus.text = $"+{RemoteConfigBase.instance.ShopAdvancedPack_Gems.ToString()}";
        txtReviveBonus.text = $"+{RemoteConfigBase.instance.ShopAdvancedPack_UnlimitedRevive} Mins";

        boosterRewards ??= new List<BoosterReward>();
        if (boosterRewards.IsNullOrEmpty()) {
            imgBooster.gameObject.SetActive(false);
        } else {
            foreach (var booster in boosterRewards) {
                var config = BoosterManager.GetBoosterConfig(booster.type);
                if (config == null)
                    continue;

                var itemBooster = Instantiate(imgBooster, imgBooster.transform.parent);
                itemBooster.sprite = config.icon;
                itemBooster.gameObject.SetActive(true);
                itemBooster.GetComponentInChildren<Text>().text = $"+{booster.amount}";
            }

            itemReviveBonus.SetAsLastSibling();
        }
    }
}