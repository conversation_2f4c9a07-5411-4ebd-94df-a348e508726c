using System;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;

namespace AdIntegrationManager
{
    public class Utilities
    {
        public static string CreateMaxEvent(string eventName)
        {
            return "{\"name\":\"" + eventName + "\"}";
        }

        public static string CreateMaxEvent(string eventName, string adUnitIdentifier, string format)
        {
            return "{\"name\":\""+ eventName + "\",\"adUnitId\":\"" + adUnitIdentifier + "\",\"adFormat\":\"" + format + "\"}";
        }

        public static string CreateMaxRewardEvent(string eventName, string adUnitIdentifier, string rewardLabel)
        {
            return "{\"name\":\"" + eventName + "\",\"adUnitId\":\"" + adUnitIdentifier + "\",\"rewardLabel\":\"" + rewardLabel + "\"}";
        }

        public static void Log(string message)
        {
            UnityEngine.Debug.Log("[AdIntegrationManager] " + message);
        }

        public static void Error(string message)
        {
            UnityEngine.Debug.LogError("[AdIntegrationManager] " + message);
        }

        public static bool IsCurrentSdkVersionLower(string latestSdkVersion)
        {
            var curVersion = CurrentSdkVersion();

            if (curVersion == null || curVersion.Length == 0) return false;

            var currentVersion = new Version(curVersion);
            var latestVersion = new Version(latestSdkVersion);
            int comparison = currentVersion.CompareTo(latestVersion);

            return comparison < 0;
        }

        public static string CurrentSdkVersion()
        {
            string versionFilePath = PathOfDirectory("Playgap") + "/Version.md";
            string content = File.ReadAllText(versionFilePath);
            var regex = new Regex(@"Version: \*{2}(?<version>.+)\*{2}", RegexOptions.Singleline);

            var match = regex.Match(content);
            if (!match.Success || match.Groups.Count == 1)
            {
                return null;
            }

            return match.Groups.FirstOrDefault((x) => x.Name == "version")?.Value;
        }

        public static string PathOfDirectory(string directory)
        {
            string[] dirs = Directory.GetDirectories("Assets", "*", SearchOption.AllDirectories);

            return Array.Find(dirs, elem => elem.EndsWith(directory)) ?? $"Assets/{ directory }";
        }
    }
}