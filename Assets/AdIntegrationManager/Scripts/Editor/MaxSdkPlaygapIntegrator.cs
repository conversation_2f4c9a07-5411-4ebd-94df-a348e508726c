using System;
using System.Threading;
using UnityEditor;
using UnityEngine;

namespace AdIntegrationManager
{
    internal class MaxSdkPlaygapIntegrator : IPlaygapIntegrator
    {
        private readonly Step[] steps;
        private readonly MaxSdkAutoIntegrator autoIntegrator = new();

        public MaxSdkPlaygapIntegrator()
        {
            steps = new[]
            {
                new Step("Check Max SDK version", () => autoIntegrator.CheckMaxVersion()),
                new Step("Find MaxSdk.Scripts.asmdef", () => autoIntegrator.FindAssemblyDefinition()),
                new Step("Find MaxSdkCallbacks.cs", () => autoIntegrator.FindMaxSdkCallbacksFile()),
                new Step("Find MaxSdkAndroid.cs", () => autoIntegrator.FindMaxSdkAndroidFile()),
                new Step("Find MaxSdkiOS.cs", () => autoIntegrator.FindMaxSdkiOSFile()),
                new Step("Load files", () => autoIntegrator.LoadFiles()),
                new Step("Patch MaxSdk.Scripts.asmdef", () => autoIntegrator.PatchAssemblyDefinition()),
                new Step("Patch MaxSdkCallbacks.cs", () => autoIntegrator.PatchMaxSdkCallbacksFile()),
                new Step("Patch MaxSdkAndroid.cs", () => autoIntegrator.PatchMaxSdkAndroidFile()),
                new Step("Patch MaxSdkiOS.cs", () => autoIntegrator.PatchMaxSdkiOSFile()),
                new Step("Save files", () => autoIntegrator.SaveFiles()),
                new Step("Cleanup", () => autoIntegrator.Cleanup()),
            };
        }

        public void Integrate()
        {
            EditorApplication.LockReloadAssemblies();

            autoIntegrator.RestoreOutdatedMacroIntegration();

            try
            {
                float stepIndex = 0;
                foreach (Step step in steps)
                {
                    try
                    {
                        Thread.Sleep((int)(stepIndex * 50.0f));

                        step.action();

                        EditorUtility.DisplayProgressBar("Playgap Integration...", step.title, stepIndex / steps.Length);

                        stepIndex += 1;
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError("[Playgap] Automatic Playgap integration failed due to error");
                        Debug.LogException(ex);
                        return;
                    }
                }

                EditorUtility.ClearProgressBar();
            }
            finally
            {
                EditorApplication.UnlockReloadAssemblies();
            }
        }

        public void Reset()
        {
            EditorApplication.LockReloadAssemblies();

            try
            {
                autoIntegrator.Restore();
            }
            finally
            {
                EditorApplication.UnlockReloadAssemblies();
            }
        }
    }
}