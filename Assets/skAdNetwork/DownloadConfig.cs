
#define LZMA_COMPRESSION
#define ENABLE_RECOMPRESS
#define ENABLE_LZMA_RECOMPRESS


#if !UNITY_WEBGL || UNITY_EDITOR
#define ENABLE_FILE_IO
#endif

using System.Collections.Generic;
using System.IO;
using System.Text;
using UnityEditor;
using UnityEngine;
using UnityEngine.Networking;
using System.Threading.Tasks;
using Debug = UnityEngine.Debug;
using Newtonsoft.Json.Linq;
#if UNITY_EDITOR
public class DownloadConfig : EditorWindow
{
    // const string SKADNETWORK_OUPUT = "Assets/Editor/plist-sdk.txt";
    //
    // [MenuItem("Build/3. Download All Config", false, 3)]
    // public static async void DownLoadAllConfig()
    // {
    //     //Minh.ho: get ads network data
    //     await GetAdsNetworkData();
    //     AssetDatabase.Refresh();
    //
    //     //Debug.LogError("Done! total link: " + cachedUrl.Count);
    // }
    //
    // private async static Task<string> GetAdsNetworkData()
    // {
    //     // get token 1st
    //     var form = new WWWForm();
    //     var jwt = Read_SKAdNetwork_Data.GetJWTForGoogleCloud();
    //     form.AddField("grant_type", "urn:ietf:params:oauth:grant-type:jwt-bearer");
    //     form.AddField("assertion", jwt);
    //     string url = "https://oauth2.googleapis.com/token";
    //     var dicHeaders = new Dictionary<string, string>();
    //     dicHeaders.Add("Content-Type", "application/x-www-form-urlencoded");
    //     string json = await HttpGet(url, dicHeaders, form);
    //     var res = JsonUtility.FromJson<Response_Main>(json);
    //     Debug.Log(res.access_token);
    //     dicHeaders.Clear();
    //     dicHeaders.Add("Authorization", "Bearer " + res.access_token);
    //
    //     string raw_sheetDatas = await HttpGet(Read_SKAdNetwork_Data.sheetURLForEditor, dicHeaders);
    //     if (string.IsNullOrEmpty(raw_sheetDatas))
    //     {
    //         EditorUtility.DisplayDialog("Message Error","Sheet data is null or empty","Close");
    //         return "";
    //     }
    //
    //     ///Parse data
    //     var jsonObject = JObject.Parse(raw_sheetDatas);
    //     var values = jsonObject["values"].ToObject<List<List<object>>>();
    //
    //     List<string> listSKAdNetwork = new List<string>();
    //     if (values != null && values.Count > 0)
    //     {
    //         foreach (IList<object> row in values)
    //         {
    //             string cellStr = "";
    //             StringBuilder oneRow = new StringBuilder();
    //             foreach (object cell in row)
    //             {
    //                 cellStr = cell.ToString().Trim();
    //                 if (!listSKAdNetwork.Contains(cellStr))
    //                 {
    //                     listSKAdNetwork.Add(cellStr);
    //                 }
    //             }
    //             //listSKAdNetwork += oneRow.ToString() + "\n";
    //         }
    //     }
    //     else
    //     {
    //         Debug.Log("No data found.");
    //     }
    //
    //
    //     File.WriteAllLines(SKADNETWORK_OUPUT, listSKAdNetwork.ToArray());
    //
    //     Debug.Log("Add " + listSKAdNetwork.Count + " items to " + SKADNETWORK_OUPUT);
    //     return "";
    // }
    //
    // public static async Task<string> HttpGet(string url, Dictionary<string, string> headers = null, WWWForm form = null)
    // {
    //     UnityWebRequest req;
    //     if (form == null) req = UnityWebRequest.Get(url);
    //     else req = UnityWebRequest.Post(url,form);
    //
    //     if(headers != null)
    //     {
    //         foreach (var item in headers)
    //         {
    //             req.SetRequestHeader(item.Key, item.Value);
    //         }
    //     }
    //
    //     await req.SendWebRequest();
    //
    //     if (!string.IsNullOrEmpty(req.error))
    //     {
    //         Debug.LogError(req.error + "---->" + url);
    //         return string.Empty;
    //     }
    //
    //     return req.downloadHandler.text;
    // }
}
#endif