using System;
using UnityEngine;
using UnityEngine.UI;

public class FileSelectorItemV2Script : OptimizedCellView {
    #region Fields

    public                   Text   title;
    public                   string path;

    [SerializeField] private Button btnClick;

    private Action<FileSelectorItemV2Script> onItemClick;

    #endregion

    #region Unity Method

    private void Awake() {
        btnClick.onClick.AddListener(btnClickOnClick);
    }

    #endregion

    private void btnClickOnClick() {
        onItemClick?.Invoke(this);
    }

    public void SetFile(FileData fileInfo, Action<FileSelectorItemV2Script> onClick) {
        title.text = Util.FileNameToTitle(fileInfo.Name, 25);
        path = fileInfo.FullName;
        onItemClick = onClick;
    }

    public override void SetData(IData _data) {
    }
}