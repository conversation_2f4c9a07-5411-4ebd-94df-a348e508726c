// Made with Amplify Shader Editor v1.9.1.2
// Available at the Unity Asset Store - http://u3d.as/y3X 
Shader "InwaveEffect/Trail-DR"
{
	Properties
	{
		_TintColor ("Tint Color", Color) = (0.5,0.5,0.5,0.5)
		//_MainTex ("Particle Texture", 2D) = "white" {}
		_InvFade ("Soft Particles Factor", Range(0.01,3.0)) = 1.0
		_TextureSample0("Texture Sample 0", 2D) = "white" {}
		_Noise("Noise", 2D) = "white" {}
		_Mask("Mask", 2D) = "white" {}
		_SpeedMainTexUVNoiseZW("Speed MainTex U/V + Noise Z/W", Vector) = (0,0,0,0)
		_Color("Color", Color) = (0.0235849,0.6119576,1,1)
		_MainOpacity("MainOpacity", Range( 0 , 1.5)) = 1.2
		_NoiseScale("NoiseScale", Vector) = (1,1,0,0)
		[HDR]_NoiseColor("NoiseColor", Color) = (1,1,1,1)
		_NoiseOpacity("NoiseOpacity", Range( 0 , 1.5)) = 0.3
		[Toggle]_Masking("Masking", Float) = 0
		[HideInInspector] _texcoord( "", 2D ) = "white" {}

	}


	Category 
	{
		SubShader
		{
		LOD 0

			Tags { "Queue"="Transparent" "IgnoreProjector"="True" "RenderType"="Transparent" "PreviewType"="Plane" }
			Blend SrcAlpha OneMinusSrcAlpha
			ColorMask RGB
			Cull Off
			Lighting Off 
			ZWrite Off
			ZTest LEqual
			
			Pass {
			
				CGPROGRAM
				
				#ifndef UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX
				#define UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(input)
				#endif
				
				#pragma vertex vert
				#pragma fragment frag
				#pragma target 3.0
				#pragma multi_compile_instancing
				#pragma multi_compile_particles
				#pragma multi_compile_fog
				#include "UnityShaderVariables.cginc"
				#define ASE_NEEDS_FRAG_COLOR


				#include "UnityCG.cginc"

				struct appdata_t 
				{
					float4 vertex : POSITION;
					fixed4 color : COLOR;
					float4 texcoord : TEXCOORD0;
					UNITY_VERTEX_INPUT_INSTANCE_ID
					
				};

				struct v2f 
				{
					float4 vertex : SV_POSITION;
					fixed4 color : COLOR;
					float4 texcoord : TEXCOORD0;
					UNITY_FOG_COORDS(1)
					#ifdef SOFTPARTICLES_ON
					float4 projPos : TEXCOORD2;
					#endif
					UNITY_VERTEX_INPUT_INSTANCE_ID
					UNITY_VERTEX_OUTPUT_STEREO
					
				};
				
				
				#if UNITY_VERSION >= 560
				UNITY_DECLARE_DEPTH_TEXTURE( _CameraDepthTexture );
				#else
				uniform sampler2D_float _CameraDepthTexture;
				#endif

				//Don't delete this comment
				// uniform sampler2D_float _CameraDepthTexture;

				uniform sampler2D _MainTex;
				uniform fixed4 _TintColor;
				uniform float4 _MainTex_ST;
				uniform float _InvFade;
				uniform float _Masking;
				uniform sampler2D _TextureSample0;
				uniform float4 _SpeedMainTexUVNoiseZW;
				uniform float _MainOpacity;
				uniform float4 _Color;
				uniform sampler2D _Noise;
				uniform float2 _NoiseScale;
				uniform float _NoiseOpacity;
				uniform sampler2D _Mask;
				uniform float4 _Mask_ST;
				uniform float4 _NoiseColor;


				v2f vert ( appdata_t v  )
				{
					v2f o;
					UNITY_SETUP_INSTANCE_ID(v);
					UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);
					UNITY_TRANSFER_INSTANCE_ID(v, o);
					

					v.vertex.xyz +=  float3( 0, 0, 0 ) ;
					o.vertex = UnityObjectToClipPos(v.vertex);
					#ifdef SOFTPARTICLES_ON
						o.projPos = ComputeScreenPos (o.vertex);
						COMPUTE_EYEDEPTH(o.projPos.z);
					#endif
					o.color = v.color;
					o.texcoord = v.texcoord;
					UNITY_TRANSFER_FOG(o,o.vertex);
					return o;
				}

				fixed4 frag ( v2f i  ) : SV_Target
				{
					UNITY_SETUP_INSTANCE_ID( i );
					UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX( i );

					#ifdef SOFTPARTICLES_ON
						float sceneZ = LinearEyeDepth (SAMPLE_DEPTH_TEXTURE_PROJ(_CameraDepthTexture, UNITY_PROJ_COORD(i.projPos)));
						float partZ = i.projPos.z;
						float fade = saturate (_InvFade * (sceneZ-partZ));
						i.color.a *= fade;
					#endif

					float2 appendResult10 = (float2(_SpeedMainTexUVNoiseZW.x , _SpeedMainTexUVNoiseZW.y));
					float2 texCoord9 = i.texcoord.xy * float2( 1,1 ) + float2( 0,0 );
					float2 panner14 = ( 1.0 * _Time.y * appendResult10 + texCoord9);
					float4 tex2DNode32 = tex2D( _TextureSample0, panner14 );
					float2 appendResult13 = (float2(_SpeedMainTexUVNoiseZW.z , _SpeedMainTexUVNoiseZW.w));
					float2 texCoord15 = i.texcoord.xy * _NoiseScale + float2( 0,0 );
					float2 panner16 = ( 1.0 * _Time.y * appendResult13 + texCoord15);
					float4 tex2DNode19 = tex2D( _Noise, panner16 );
					float2 uv_Mask = i.texcoord.xy * _Mask_ST.xy + _Mask_ST.zw;
					float temp_output_45_0 = tex2D( _Mask, uv_Mask ).r;
					float4 temp_output_48_0 = ( ( tex2DNode32 * _MainOpacity * tex2DNode32.a * _Color ) + ( tex2DNode19 * _NoiseOpacity * tex2DNode19.a * tex2DNode32.a * temp_output_45_0 * ( _Color + _NoiseColor ) ) );
					

					fixed4 col = saturate( ( (( _Masking )?( ( temp_output_48_0 * temp_output_45_0 ) ):( temp_output_48_0 )) * i.color ) );
					UNITY_APPLY_FOG(i.fogCoord, col);
					return col;
				}
				ENDCG 
			}
		}	
	}
	CustomEditor "ASEMaterialInspector"
	
	Fallback Off
}
/*ASEBEGIN
Version=19102
Node;AmplifyShaderEditor.CommentaryNode;7;-2774.938,-610.6951;Inherit;False;1037.896;533.6285;Textures movement;8;16;15;14;13;10;9;8;55;;1,1,1,1;0;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;47;-1230.616,-683.2457;Inherit;True;4;4;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;52;-254.6455,-316.3148;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.VertexColorNode;18;-488.4281,-201.9918;Inherit;False;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SaturateNode;42;-59.03079,-316.5362;Inherit;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;51;-724.4323,-325.902;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.RangedFloatNode;50;-1617.155,-12.34554;Inherit;False;Property;_NoiseOpacity;NoiseOpacity;8;0;Create;True;0;0;0;False;0;False;0.3;0.9560883;0;1.5;0;1;FLOAT;0
Node;AmplifyShaderEditor.ToggleSwitchNode;37;-568.5816,-466.9197;Float;True;Property;_Masking;Masking;9;0;Create;True;0;0;0;False;0;False;0;True;2;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.Vector4Node;8;-2724.938,-381.6526;Float;False;Property;_SpeedMainTexUVNoiseZW;Speed MainTex U/V + Noise Z/W;3;0;Create;True;0;0;0;False;0;False;0,0,0,0;0,0,-0.5,-0.5;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.TextureCoordinatesNode;9;-2230.456,-560.6951;Inherit;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.DynamicAppendNode;10;-2152.169,-433.4305;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.DynamicAppendNode;13;-2140.39,-210.067;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.PannerNode;14;-1944.041,-519.7615;Inherit;False;3;0;FLOAT2;0,0;False;2;FLOAT2;0,0;False;1;FLOAT;1;False;1;FLOAT2;0
Node;AmplifyShaderEditor.PannerNode;16;-1950.904,-301.8894;Inherit;False;3;0;FLOAT2;0,0;False;2;FLOAT2;0,0;False;1;FLOAT;1;False;1;FLOAT2;0
Node;AmplifyShaderEditor.SamplerNode;6;-1459.683,80.72726;Inherit;True;Property;_Mask;Mask;2;0;Create;True;0;0;0;False;0;False;-1;356232e8aaa04992a2d749d0840db567;edc4055b7b77c7948a01b5f9076b2932;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.TextureCoordinatesNode;15;-2235.526,-335.2923;Inherit;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SamplerNode;19;-1632.002,-207.931;Inherit;True;Property;_Noise;Noise;1;0;Create;True;0;0;0;False;0;False;-1;381971b998464a3b89560a23153e6212;381971b998464a3b89560a23153e6212;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.Vector2Node;55;-2445.753,-198.314;Inherit;False;Property;_NoiseScale;NoiseScale;6;0;Create;True;0;0;0;False;0;False;1,1;0.8,0.8;0;3;FLOAT2;0;FLOAT;1;FLOAT;2
Node;AmplifyShaderEditor.ColorNode;20;-1529.885,-856.4634;Float;False;Property;_Color;Color;4;0;Create;True;0;0;0;False;0;False;0.0235849,0.6119576,1,1;1,0,0,1;False;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.RangedFloatNode;46;-1542.422,-479.0902;Inherit;False;Property;_MainOpacity;MainOpacity;5;0;Create;True;0;0;0;False;0;False;1.2;0.85;0;1.5;0;1;FLOAT;0
Node;AmplifyShaderEditor.ColorNode;54;-1681.686,-391.9217;Float;False;Property;_NoiseColor;NoiseColor;7;1;[HDR];Create;True;0;0;0;False;0;False;1,1,1,1;0.1115696,0.1115696,0.1115696,1;False;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleAddOpNode;48;-921.8916,-436.7862;Inherit;True;2;2;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;49;-1163.305,-400.4012;Inherit;True;6;6;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleAddOpNode;57;-1400.056,-369.8763;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.ComponentMaskNode;45;-1150.385,77.21522;Inherit;True;True;False;False;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SamplerNode;32;-1620.765,-677.1207;Inherit;True;Property;_TextureSample0;Texture Sample 0;0;0;Create;True;0;0;0;False;0;False;-1;7337a1ffd4378fd42816aae18c955de5;fd71d745ff6bb6c458dc93cc4df39050;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.TemplateMultiPassMasterNode;0;151.7235,-317.6081;Float;False;True;-1;2;ASEMaterialInspector;0;11;InwaveEffect/Trail-DR;0b6a9f8b4f707c74ca64c0be8e590de0;True;SubShader 0 Pass 0;0;0;SubShader 0 Pass 0;2;True;True;2;5;False;;10;False;;0;1;False;;0;False;;False;False;False;False;False;False;False;False;False;False;False;False;True;2;False;;False;True;True;True;True;False;0;False;;False;False;False;False;False;False;False;False;False;True;2;False;;True;3;False;;False;True;4;Queue=Transparent=Queue=0;IgnoreProjector=True;RenderType=Transparent=RenderType;PreviewType=Plane;False;False;0;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;True;2;False;0;;0;0;Standard;0;0;1;True;False;;False;0
WireConnection;47;0;32;0
WireConnection;47;1;46;0
WireConnection;47;2;32;4
WireConnection;47;3;20;0
WireConnection;52;0;37;0
WireConnection;52;1;18;0
WireConnection;42;0;52;0
WireConnection;51;0;48;0
WireConnection;51;1;45;0
WireConnection;37;0;48;0
WireConnection;37;1;51;0
WireConnection;10;0;8;1
WireConnection;10;1;8;2
WireConnection;13;0;8;3
WireConnection;13;1;8;4
WireConnection;14;0;9;0
WireConnection;14;2;10;0
WireConnection;16;0;15;0
WireConnection;16;2;13;0
WireConnection;15;0;55;0
WireConnection;19;1;16;0
WireConnection;48;0;47;0
WireConnection;48;1;49;0
WireConnection;49;0;19;0
WireConnection;49;1;50;0
WireConnection;49;2;19;4
WireConnection;49;3;32;4
WireConnection;49;4;45;0
WireConnection;49;5;57;0
WireConnection;57;0;20;0
WireConnection;57;1;54;0
WireConnection;45;0;6;1
WireConnection;32;1;14;0
WireConnection;0;0;42;0
ASEEND*/
//CHKSM=904DEC6C7E5BFF7B8930BFC52BE62DD75C2A0CD1