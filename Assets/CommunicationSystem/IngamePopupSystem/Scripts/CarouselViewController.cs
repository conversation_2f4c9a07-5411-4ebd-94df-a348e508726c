using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

public class CarouselViewController : MonoBehaviour
{
    [SerializeField] private GameObject ingamePopupPrefab;
    [SerializeField] private RectTransform view_window;
    [SerializeField] private CarouselIndicator indicator;
    private List<RectTransform> ingamePopups = new List<RectTransform>();

    private bool canSwipe;
    private float viewWindow_width;
    private float lerpTimer;
    private float lerpPosition;
    private float mousePositionStartX;
    private float mousePositionEndX;
    private float dragAmount;
    private float screenPosition;
    private float lastScreenPosition;

    // Space between images
    public float image_gap = 30;

    public int swipeThrustHold = 30;

    private int m_currentIndex;

    // The index of the current image on display.
    public int CurrentIndex { get { return m_currentIndex; } }
    public int PopupsCount { get { return ingamePopups.Count; } }

    public static bool igPopupInternalSetOnboarding = true;

    public static Action OnPopupShowAll       = null;
    public static Action OnPopupCloseAll      = null;

    #region mono
    // Use this for initialization
    void Start()
    {
        this.gameObject.SetActive(false);
    }

    private void OnEnable()
    {
        IngamePopupItem.OnPopupClose += OnPopupClose;
        IngamePopupSystem.IsShowing = true;
    }

    private void OnDisable()
    {
        IngamePopupItem.OnPopupClose -= OnPopupClose;
        IngamePopupSystem.IsShowing = false;
        OnPopupCloseAll?.Invoke();
    }

    void Update()
    {
        UpdateCarouselView();
    }
    #endregion


    #region private methods
    void UpdateCarouselView()
    {
        lerpTimer = lerpTimer + Time.deltaTime;

        if (lerpTimer < 0.333f)
        {
            screenPosition = Mathf.Lerp(lastScreenPosition, lerpPosition * -1, lerpTimer * 3);
            lastScreenPosition = screenPosition;
        }

        if (Input.GetMouseButtonDown(0))
        {
            canSwipe = true;
            mousePositionStartX = Input.mousePosition.x;
        }


        if (Input.GetMouseButton(0))
        {
            if (canSwipe)
            {
                mousePositionEndX = Input.mousePosition.x;
                dragAmount = mousePositionEndX - mousePositionStartX;
                //screenPosition = lastScreenPosition + dragAmount;
            }
        }

        if (Mathf.Abs(dragAmount) > swipeThrustHold && canSwipe)
        {
            canSwipe = false;
            lastScreenPosition = screenPosition;
            if (m_currentIndex < ingamePopups.Count)
                OnSwipeComplete();
            else if (m_currentIndex == ingamePopups.Count && dragAmount < 0)
                lerpTimer = 0;
            else if (m_currentIndex == ingamePopups.Count && dragAmount > 0)
                OnSwipeComplete();
        }

        for (int i = 0; i < ingamePopups.Count; i++)
        {
            ingamePopups[i].anchoredPosition = new Vector2(screenPosition + ((viewWindow_width + image_gap) * i), 0);
            if (i == m_currentIndex)
            {
                ingamePopups[i].localScale = Vector3.Lerp(ingamePopups[i].localScale, new Vector3(1.2f, 1.2f, 1.2f), Time.deltaTime * 5);
            }
            else
            {
                ingamePopups[i].localScale = Vector3.Lerp(ingamePopups[i].localScale, new Vector3(0.7f, 0.7f, 0.7f), Time.deltaTime * 5);
            }
        }
    }

    void OnSwipeComplete()
    {
        lastScreenPosition = screenPosition;
        if (dragAmount > 0)
        {
            if (dragAmount >= swipeThrustHold)
            {
                if (m_currentIndex == 0)
                {
                    lerpTimer = 0; lerpPosition = 0;
                }
                else
                {
                    m_currentIndex--;
                    lerpTimer = 0;
                    if (m_currentIndex < 0)
                        m_currentIndex = 0;
                    lerpPosition = (viewWindow_width + image_gap) * m_currentIndex;
                }
            }
            else
            {
                lerpTimer = 0;
            }
        }
        else if (dragAmount < 0)
        {
            if (Mathf.Abs(dragAmount) >= swipeThrustHold)
            {
                if (m_currentIndex == ingamePopups.Count - 1)
                {
                    lerpTimer = 0;
                    lerpPosition = (viewWindow_width + image_gap) * m_currentIndex;
                }
                else
                {
                    lerpTimer = 0;
                    m_currentIndex++;
                    lerpPosition = (viewWindow_width + image_gap) * m_currentIndex;
                }
            }
            else
            {
                lerpTimer = 0;
            }
        }
        dragAmount = 0;
        indicator.SetCurrentDot(m_currentIndex);
        LogPopupShow(shownBy: "drag");
    }

    private GameObject InstantiateItem(IngamePopup ingamePopup)
    {
        GameObject newItem = Instantiate(ingamePopupPrefab, view_window);
        IngamePopupItem controller = newItem.GetComponent<IngamePopupItem>();
        controller.Initialize(ingamePopup);
        return newItem;
    }

    private List<IngamePopup> SortPopupsByPriority(List<IngamePopup> ingamePopupList)
    {
        return ingamePopupList.OrderByDescending(x => x.priority).ToList();
    }

    private void ClosePopup(bool isClosingAll = false)
    {
        RectTransform currentPopup = ingamePopups[m_currentIndex];
        ingamePopups.Remove(currentPopup);
        Destroy(currentPopup.gameObject);

        if (ingamePopups.Count <= 0)
        {
            this.gameObject.SetActive(false);
            if (igPopupInternalSetOnboarding == false)
            {
                //OnboardingManager.instance.SetCanShowOnboarding(true);
                igPopupInternalSetOnboarding = true;
            }
        }
        else
        {
            if (ingamePopups.Count <= 1) indicator.Hide();
            if (m_currentIndex >= ingamePopups.Count)
            {
                m_currentIndex = 0;
            }
            GoToIndexSmooth(m_currentIndex);
            if (!isClosingAll) LogPopupShow(shownBy: "close");
        }
        indicator.RemoveDot(m_currentIndex, m_currentIndex);
    }

    private void CloseAllPopups()
    {
        for (int i = 0, n = ingamePopups.Count; i < n; i++)
        {
            ClosePopup(isClosingAll: true);
        }
    }

    private void OnPopupClose(bool isExecuteAction)
    {
        if (isExecuteAction)
        {
            gameObject.SetActive(false);
            CloseAllPopups();
        }
        else
        {
            ClosePopup();
        }
    }

    private List<string> appearedPopupIds = new List<string>();
    public void LogPopupShow(string shownBy = "")
    {
        IngamePopupItem popup = ingamePopups[m_currentIndex].GetComponent<IngamePopupItem>();
        bool isLogged = appearedPopupIds.Contains(popup.popupId);
        if (isLogged) return;
        CommunicationEventTracking.LogIngamePopupImpression(popup.popupId, CommunicationEventTracking.LOCATION_HOME);
        appearedPopupIds.Add(popup.popupId);
    }

    private IEnumerator CoLogFirstPopupShow()
    {
        if (ingamePopups.Count <= 0) yield break;
        yield return new WaitUntil(() => IsTargetVisible(ingamePopups[0]) /*&& !SV_Root.Instance.HavePopupOrScreen()*/);
        LogPopupShow(shownBy: "init");
    }

    private bool IsTargetVisible(Transform target)
    {
        /*
        var planes = GeometryUtility.CalculateFrustumPlanes(SV_Root.Instance.camera);
        var point = target.position;
        foreach (var plane in planes)
        {
            if (plane.GetDistanceToPoint(point) < 0)
                return false;
        }
        */
        return true;
    }

    private void SetSwipeThrustHold()
    {
        swipeThrustHold = Screen.width / 12;
    }
    #endregion

    #region public methods
    public void GoToIndex(int value)
    {
        m_currentIndex = value;
        lerpTimer = 0;
        lerpPosition = (viewWindow_width + image_gap) * m_currentIndex;
        screenPosition = lerpPosition * -1;
        lastScreenPosition = screenPosition;
        for (int i = 0; i < ingamePopups.Count; i++)
        {
            ingamePopups[i].anchoredPosition = new Vector2(screenPosition + ((viewWindow_width + image_gap) * i), 0);
        }
    }

    public void GoToIndexSmooth(int value)
    {
        m_currentIndex = value;
        lerpTimer = 0;
        lerpPosition = (viewWindow_width + image_gap) * m_currentIndex;
    }

    public void Show(List<IngamePopup> ingamePopupList)
    {
        SetSwipeThrustHold();
        viewWindow_width = view_window.rect.width;
        ingamePopupList = SortPopupsByPriority(ingamePopupList);
        for (int i = 0; i < ingamePopupList.Count; i++)
        {
            IngamePopup ingamePopup = ingamePopupList[i];
            GameObject ingamePopupItem = InstantiateItem(ingamePopup);
            RectTransform rect = ingamePopupItem.GetComponent<RectTransform>();
            ingamePopups.Add(rect);
            rect.anchoredPosition = new Vector2(((viewWindow_width + image_gap) * i), 0);
        }
        indicator.Initialize(ingamePopupList.Count);
        this.gameObject.SetActive(true);
        OnPopupShowAll?.Invoke();
        //OnboardingManager.instance.SetCanShowOnboarding(false);
        igPopupInternalSetOnboarding = false;
        IngamePopupSystem.HasShownThisSession = true;
        StartCoroutine(CoLogFirstPopupShow());
    }
    #endregion
}
