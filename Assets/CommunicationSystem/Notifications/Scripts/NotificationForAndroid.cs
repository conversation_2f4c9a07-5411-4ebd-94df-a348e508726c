using System;
using System.Collections;
using UnityEngine;
#if UNITY_ANDROID
using Unity.Notifications.Android;
#endif

namespace Amanotes.Push {
    public class NotificationForAndroid : NotificationBase {
        private static bool _isInitialized = false;
        private static readonly object _initLock = new object();

        public void InitialLocalNotification() {
#if UNITY_ANDROID
            // Thread-safe initialization check
            lock (_initLock) {
                if (_isInitialized) {
                    if (NotificationLog.isEnabled) {
                        Debug.Log("[NotificationForAndroid] Already initialized, skipping");
                    }
                    return;
                }

                try {
                    // Add null check and exception handling
                    if (!IsNotificationSystemAvailable()) {
                        Debug.LogWarning("[NotificationForAndroid] Notification system not available");
                        return;
                    }

                    AndroidNotificationChannel channel = new AndroidNotificationChannel() {
                        Id = AmaNotificationData.NOTIFICATION_CHANNEL,
                        Name = AmaNotificationData.NOTIFICATION_CHANNEL,
                        Importance = Importance.High,
                        Description = AmaNotificationData.NOTIFICATION_CHANNEL,
                    };

                    // Use try-catch for the actual JNI call that's crashing
                    AndroidNotificationCenter.RegisterNotificationChannel(channel);
                    _isInitialized = true;

                    if (NotificationLog.isEnabled) {
                        Debug.Log("[NotificationForAndroid] Successfully initialized notification channel");
                    }

                } catch (Exception ex) {
                    Debug.LogError($"[NotificationForAndroid] Failed to initialize notifications: {ex.Message}");
                    Debug.LogError($"[NotificationForAndroid] Stack trace: {ex.StackTrace}");
                    
                    // Don't rethrow - gracefully handle the failure
                    _isInitialized = false;
                }
            }
#endif
        }

#if UNITY_ANDROID
        private bool IsNotificationSystemAvailable() {
            try {
                // Simple check to see if the Android notification system is accessible
                // This might help detect if there are JNI issues before the crash
                var testChannel = new AndroidNotificationChannel() {
                    Id = "test_channel_check",
                    Name = "Test",
                    Importance = Importance.Low,
                    Description = "Test channel for availability check",
                };
                
                // If this doesn't throw, the system is likely available
                return testChannel != null;
            } catch {
                return false;
            }
        }
#endif

        public void RegisterNotification(AmaNotificationData data) {
#if UNITY_ANDROID
            // Ensure initialization was successful before registering notifications
            if (!_isInitialized) {
                Debug.LogWarning("[NotificationForAndroid] Cannot register notification - system not initialized");
                return;
            }

            try {
                AndroidNotification notification = new AndroidNotification();
                bool isRepeating = data.repeatInterval != new TimeSpan(0);
                if (isRepeating) {
                    notification.RepeatInterval = data.repeatInterval;
                    notification.FireTime = DateTime.Now.Add(data.repeatInterval);
                } else {
                    notification.FireTime = data.fireTime.DateTime;
                }

                notification.Title = data.title;
                notification.Text = data.content;
                notification.IntentData = JsonUtility.ToJson(data);
                notification.SmallIcon = data.SmallIcon;
                notification.LargeIcon = data.LargeIcon;
                
                AndroidNotificationCenter.SendNotificationWithExplicitID(notification,
                    AmaNotificationData.NOTIFICATION_CHANNEL, data.id);
                    
                if (NotificationLog.isEnabled) {
                    Debug.Log($"RegisterNotification {data.title} {notification.FireTime}");
                }
            } catch (Exception ex) {
                Debug.LogError($"[NotificationForAndroid] Failed to register notification: {ex.Message}");
            }
#endif
        }

        public void CancelNotification(int notificationId) {
#if UNITY_ANDROID
            if (!_isInitialized) {
                Debug.LogWarning("[NotificationForAndroid] Cannot cancel notification - system not initialized");
                return;
            }

            try {
                AndroidNotificationCenter.CancelNotification(notificationId);
                if (NotificationLog.isEnabled) {
                    Debug.Log("[Notification] CancelNotification " + notificationId);
                }
            } catch (Exception ex) {
                Debug.LogError($"[NotificationForAndroid] Failed to cancel notification {notificationId}: {ex.Message}");
            }
#endif
        }

        public void SetActiveLog(bool isLog) {
#if UNITY_ANDROID
            NotificationLog.isEnabled = isLog;
#endif
        }

        public void ClearAllShowingNotification() {
#if UNITY_ANDROID
            if (!_isInitialized) {
                Debug.LogWarning("[NotificationForAndroid] Cannot clear notifications - system not initialized");
                return;
            }

            try {
                AndroidNotificationCenter.CancelAllNotifications();
                if (NotificationLog.isEnabled) {
                    Debug.Log("[Notification] ClearAllShowingNotification");
                }
            } catch (Exception ex) {
                Debug.LogError($"[NotificationForAndroid] Failed to clear notifications: {ex.Message}");
            }
#endif
        }

        public string CheckFirstTimeAppLaunch() {
            string result = string.Empty;
#if UNITY_ANDROID
            if (!_isInitialized) {
                Debug.LogWarning("[NotificationForAndroid] Cannot check app launch - system not initialized");
                return result;
            }

            try {
                AndroidNotificationIntentData notificationIntentData =
                    AndroidNotificationCenter.GetLastNotificationIntent();
                if (notificationIntentData != null) {
                    int id = notificationIntentData.Id;
                    string channel = notificationIntentData.Channel;
                    AndroidNotification notification = notificationIntentData.Notification;
                    result = notification.IntentData;
                }

                if (NotificationLog.isEnabled) {
                    Debug.Log("[Notification] CheckFirstTimeAppLaunch " + result);
                }
            } catch (Exception ex) {
                Debug.LogError($"[NotificationForAndroid] Failed to check first time app launch: {ex.Message}");
            }
#endif
            return result;
        }

        // Static method to check if notifications are properly initialized
        public static bool IsNotificationSystemReady() {
            return _isInitialized;
        }

        // Method to reset initialization state (for testing or recovery)
        public static void ResetInitializationState() {
            lock (_initLock) {
                _isInitialized = false;
            }
        }
    }
}
