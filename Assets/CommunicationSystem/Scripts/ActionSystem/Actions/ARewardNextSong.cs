using System;

namespace Amanotes.ActionSystem
{
    [System.Serializable]
    public class ARewardNextSongData : ActionData {}

    public class ARewardNextSong : BaseAction<ARewardNextSongData>
    {
        public override void Execute(ARewardNextSongData data, Action onSuccess, Action onFail)
        {
            Logger.EditorLog("Communication System-Action System", $"Chưa làm type: ARewardNextSong");
            onFail?.Invoke();
        }

        public override string Description()
        {
            return "ap.nextsong";
        }

        public override string Path(ActionSystemData<ARewardNextSongData> actionSystemData) {
            return "ap.nextsong";
        }
    }
}