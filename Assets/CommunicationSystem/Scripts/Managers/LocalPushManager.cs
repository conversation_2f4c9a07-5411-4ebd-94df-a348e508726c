using UnityEngine;
using System;
using Amanotes.Push;
using System.Collections.Generic;
#if UNITY_EDITOR
using UnityEditor;
#endif


public class LocalPushManager : PianoIdol.SingletonMono<LocalPushManager>
{
	private const string APP_ICON_URL = "notify_icon_small_s";
	private const string DEFAULT_IMAGE_URL = "pushnotification/default.png";
	private const string LIFE_IMAGE_URL = "pushnotification/life.png";
	private const string TREASURE_IMAGE_URL = "pushnotification/free_treasure.png";
	private const string PUSH_LAYOUT_SMALL = "push_layout_small";
	private const string PUSH_SOUND = "push_sound";

	bool isInit = false;

	private List<AmaNotificationData> toPushList = new List<AmaNotificationData>();

	void Start ()
	{
		GameObject.DontDestroyOnLoad(this.gameObject);
	}

	public void Init()
	{
		try {
			if (isInit)
				return;

			AmanotesPushAction.OpenFromPushCallBack += openFromPushCallBack;
			NotificationManager.Instance.InitialLocalNotification();
			NotificationManager.instance.SetActiveLog(Configuration.isAdmin);
			NotificationManager.instance.CheckFirstTimeComeFrom();
			isInit = true;
		} catch (Exception ex) {
			Debug.LogError(
				$"[NotificationInit] Notification initialization attempt {attempt + 1} threw exception: {ex.Message}");
		}
	}

	private void openFromPushCallBack(string json)
	{
		if (!string.IsNullOrEmpty(json))
		{
			Amanotes.ActionSystem.ActionSystemLogs.Log("receive action data from localpush: " + json);
			NotificationDeepLink.ProcessNotificationJsonData(json, actionFrom: Amanotes.ActionSystem.ActionFrom.LOCAL_NOTIFICATION);
		}
	}

	public void NotificationClickOnBackground(string notification)//call from native
	{
		UnityEngine.Debug.Log("LocalNotifications NotificationClickOnBackground data: " + notification);
		openFromPushCallBack(notification);
	}

	public void AddToPushList(int id, string title, string content, DateTime timePush, string extra_info = "", string action_data = "")
    {
		AmaNotificationData notification = new AmaNotificationData(id, timePush, title, content, extra_info);
		notification.app_icon = APP_ICON_URL;
		notification.url_image_logo = DEFAULT_IMAGE_URL;
		notification.layout_small = PUSH_LAYOUT_SMALL;
		notification.sound = PUSH_SOUND;
		notification.is_vibrate = true;
		notification.action_data = action_data;
		toPushList.Add(notification);
	}

	public void RemoveFromPushList(int id)
    {
		AmaNotificationData notification = toPushList.Find(x => x.id == id);
		if (notification != null)
        {
			toPushList.Remove(notification);
        }
    }

	public void RemoveAllFromPushList()
    {
		toPushList.Clear();
    }

	public void Schedule(AmaNotificationData notification)
	{
		if (!EnableLocalPush()) return;
		NotificationManager.Instance.RegisterNotification(notification);
	}

	public void Unschedule(int id)
	{
		if (!EnableLocalPush()) return;
		NotificationManager.Instance.CancelNotification(id);
	}

	public void UnscheduleAllNotifications()
	{
		if (!EnableLocalPush()) return;
		NotificationManager.Instance.ClearAllShowingNotification();
	}

	private bool EnableLocalPush()
	{
		/*
		if (ConfigManager.Instance == null) return false;
		if (ConfigManager.Instance.GameConfigs == null) return false;
		return ConfigManager.Instance.GameConfigs.ENABLE_LOCALPUSH;
		*/
		return true;
	}

	public void ScheduleToPushList()
    {
	    for (int i = 0, n = toPushList.Count; i < n; i++)
        {
			AmaNotificationData notification = toPushList[i];
			Schedule(notification);
        }
    }

	private void UnscheduleToPushList()
	{
		for (int i = 0, n = toPushList.Count; i < n; i++)
		{
			AmaNotificationData notification = toPushList[i];
			Unschedule(notification.id);
		}
	}

	#region Testing
#if UNITY_EDITOR
	[MenuItem("Tools/Push/Navigation/Home-SongList")]
	public static void DoOpenHomeSongList()
	{
		Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"churn:409\",\"title\":\"This is title\",\"content\":\"This is content\", \"action_data\":\"{\\\"type\\\": \\\"NAVIGATE\\\",\\\"param\\\": {\\\"path\\\": \\\"HOME/SONG_LIST\\\"}}\"}");
	}
	[MenuItem("Tools/Push/Navigation/Home-Vida de Rico")]
	public static void DoOpenHomeSongList1()
	{
		Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"churn:409\",\"title\":\"This is title\",\"content\":\"This is content\", \"action_data\":\"{\\\"type\\\": \\\"NAVIGATE\\\",\\\"param\\\": {\\\"path\\\": \\\"HOME/SONG_LIST/6bb2f3ca-d683-4f66-82a3-c2dc847debbd\\\"}}\"}");
	}
	[MenuItem("Tools/Push/Navigation/Home-Heros")]
	public static void DoOpenHomeSongList2()
	{
		Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"churn:409\",\"title\":\"This is title\",\"content\":\"This is content\", \"action_data\":\"{\\\"type\\\": \\\"NAVIGATE\\\",\\\"param\\\": {\\\"path\\\": \\\"HOME/SONG_LIST/1383c4bb-fa77-4998-87b5-047a103d59b9\\\"}}\"}");
	}
	[MenuItem("Tools/Push/Navigation/Home-All Of Us")]
	public static void DoOpenHomeSongList3()
	{
		Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"churn:409\",\"title\":\"This is title\",\"content\":\"This is content\", \"action_data\":\"{\\\"type\\\": \\\"NAVIGATE\\\",\\\"param\\\": {\\\"path\\\": \\\"HOME/SONG_LIST/307b1192-1663-4ae7-a336-e81c19437c49\\\"}}\"}");
	}
	[MenuItem("Tools/Push/Navigation/Discovery-Genre")]
	public static void DoOpenDiscoveryGenre()
	{
		Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"churn:409\",\"title\":\"This is title\",\"content\":\"This is content\", \"action_data\":\"{\\\"type\\\": \\\"NAVIGATE\\\",\\\"param\\\": {\\\"path\\\": \\\"DISCOVERY/GENRE\\\"}}\"}");
	}
	[MenuItem("Tools/Push/Navigation/Discovery-Genre-EDM")]
	public static void DoOpenDiscoveryGenre1()
	{
		Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"churn:409\",\"title\":\"This is title\",\"content\":\"This is content\", \"action_data\":\"{\\\"type\\\": \\\"NAVIGATE\\\",\\\"param\\\": {\\\"path\\\": \\\"DISCOVERY/GENRE/EDM\\\"}}\"}");
	}
	[MenuItem("Tools/Push/Navigation/Discovery-Genre-Dance")]
	public static void DoOpenDiscoveryGenre2()
	{
		Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"churn:409\",\"title\":\"This is title\",\"content\":\"This is content\", \"action_data\":\"{\\\"type\\\": \\\"NAVIGATE\\\",\\\"param\\\": {\\\"path\\\": \\\"DISCOVERY/GENRE/Dance\\\"}}\"}");
	}

	[MenuItem("Tools/Push/Navigation/Discovery-Artist")]
	public static void DoOpenDiscoveryArtist()
	{
		Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"churn:409\",\"title\":\"This is title\",\"content\":\"This is content\", \"action_data\":\"{\\\"type\\\": \\\"NAVIGATE\\\",\\\"param\\\": {\\\"path\\\": \\\"DISCOVERY/ARTIST\\\"}}\"}");
	}
	[MenuItem("Tools/Push/Navigation/Discovery-Artist-Billie")]
	public static void DoOpenDiscoveryArtist1()
	{
		Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"churn:409\",\"title\":\"This is title\",\"content\":\"This is content\", \"action_data\":\"{\\\"type\\\": \\\"NAVIGATE\\\",\\\"param\\\": {\\\"path\\\": \\\"DISCOVERY/ARTIST/Billie\\\"}}\"}");
	}

	[MenuItem("Tools/Push/Navigation/Discovery-Album")]
	public static void DoOpenDiscoveryAlbum()
	{
		Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"churn:409\",\"title\":\"This is title\",\"content\":\"This is content\", \"action_data\":\"{\\\"type\\\": \\\"NAVIGATE\\\",\\\"param\\\": {\\\"path\\\": \\\"DISCOVERY/ALBUM\\\"}}\"}");
	}

	[MenuItem("Tools/Push/Navigation/Discovery-Album - Summer")]
	public static void DoOpenDiscoveryAlbum1()
	{
		Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"churn:409\",\"title\":\"This is title\",\"content\":\"This is content\", \"action_data\":\"{\\\"type\\\": \\\"NAVIGATE\\\",\\\"param\\\": {\\\"path\\\": \\\"DISCOVERY/ALBUM/SUMMER\\\"}}\"}");
	}

	[MenuItem("Tools/Push/Navigation/Achievement")]
	public static void DoOpenDiscoveryAchievement()
	{
		Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"churn:409\",\"title\":\"This is title\",\"content\":\"This is content\", \"action_data\":\"{\\\"type\\\": \\\"NAVIGATE\\\",\\\"param\\\": {\\\"path\\\": \\\"ACHIEVEMENT\\\"}}\"}");
	}

	[MenuItem("Tools/Push/Navigation/FreeGift")]
	public static void DoOpenDiscoveryFreeGift()
	{
		Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"churn:409\",\"title\":\"This is title\",\"content\":\"This is content\", \"action_data\":\"{\\\"type\\\": \\\"NAVIGATE\\\",\\\"param\\\": {\\\"path\\\": \\\"FREEGIFT\\\"}}\"}");
	}

	[MenuItem("Tools/Push/Navigation/7DayMission")]
	public static void DoOpenDiscovery7DayMission()
	{
		Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"churn:409\",\"title\":\"This is title\",\"content\":\"This is content\", \"action_data\":\"{\\\"type\\\": \\\"NAVIGATE\\\",\\\"param\\\": {\\\"path\\\": \\\"SEVENDAYMISSION\\\"}}\"}");
	}
	[MenuItem("Tools/Push/Navigation/BallShop")]
	public static void DoOpenDiscoveryBallShop()
	{
		Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"churn:409\",\"title\":\"This is title\",\"content\":\"This is content\", \"action_data\":\"{\\\"type\\\": \\\"NAVIGATE\\\",\\\"param\\\": {\\\"path\\\": \\\"BALLSHOP\\\"}}\"}");
	}

	[MenuItem("Tools/Push/Navigation/BallShop - Ball 15")]
	public static void DoOpenDiscoveryBallShop1()
	{
		Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"churn:409\",\"title\":\"This is title\",\"content\":\"This is content\", \"action_data\":\"{\\\"type\\\": \\\"NAVIGATE\\\",\\\"param\\\": {\\\"path\\\": \\\"BALLSHOP/15\\\"}}\"}");
	}
	[MenuItem("Tools/Push/Navigation/BallShop - Character 40")]
	public static void DoOpenDiscoveryBallShop2()
	{
		Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"churn:409\",\"title\":\"This is title\",\"content\":\"This is content\", \"action_data\":\"{\\\"type\\\": \\\"NAVIGATE\\\",\\\"param\\\": {\\\"path\\\": \\\"BALLSHOP/40\\\"}}\"}");
	}
	[MenuItem("Tools/Push/Navigation/Event")]
	public static void DoOpenLiveEvent()
	{
		Debug.Log("DoOpenLiveEvent");
		Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"churn:409\",\"title\":\"This is title\",\"content\":\"This is content\", \"action_data\":\"{\\\"type\\\": \\\"NAVIGATE\\\",\\\"param\\\": {\\\"path\\\": \\\"EVENT\\\"}}\"}");
	}
	[MenuItem("Tools/Push/PlaySong - BangBangBang_BigBang")]
	public static void DoPlaySong()
	{
		Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"churn:409\",\"title\":\"This is title\",\"content\":\"This is content\", \"action_data\":\"{\\\"type\\\": \\\"PlaySong\\\",\\\"param\\\": {\\\"acmID\\\": \\\"fdc5a660-35d8-45d0-bcf1-df8132618656\\\"}}\"}");
	}

	[MenuItem("Tools/Push/PlaySong - Sunflower_cover")]
	public static void DoPlaySong2()
	{
		Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"churn:409\",\"title\":\"This is title\",\"content\":\"This is content\", \"action_data\":\"{\\\"type\\\": \\\"PlaySong\\\",\\\"param\\\": {\\\"acmID\\\": \\\"e5a7e8f1-f1f1-48c2-af05-3ad53e07b4e6\\\"}}\"}");
	}
	[MenuItem("Tools/Push/PlaySongAndUnlock - Monody_TheFatRat_cut")]
	public static void DoPlaySongAndUnlock()
	{
		Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"churn:409\",\"title\":\"This is title\",\"content\":\"This is content\", \"action_data\":\"{\\\"type\\\": \\\"PlaySongAndUnlock\\\",\\\"param\\\": {\\\"acmID\\\": \\\"d1ea3745-d3a6-4f63-93df-1ec893c1c6c7\\\"}}\"}");
	}
	[MenuItem("Tools/Push/PlaySongAndUnlock - AfterMidnight_Slushii")]
	public static void DoPlaySongAndUnlock2()
	{
		Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"churn:409\",\"title\":\"This is title\",\"content\":\"This is content\", \"action_data\":\"{\\\"type\\\": \\\"PlaySongAndUnlock\\\",\\\"param\\\": {\\\"acmID\\\": \\\"a2683ae3-5f5a-4f90-a5f7-b07385ba545c\\\"}}\"}");
	}
	[MenuItem("Tools/Push/OpenStore")]
	public static void DoOpenStore()
	{
		Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"churn:409\",\"title\":\"This is title\",\"content\":\"This is content\", \"action_data\":\"{\\\"type\\\": \\\"OpenStore\\\",\\\"param\\\": {}}\"}");
	}
	[MenuItem("Tools/Push/OpenURL")]
	public static void DoOpenURL()
	{
		Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"churn:409\",\"title\":\"This is title\",\"content\":\"This is content\", \"action_data\":\"{\\\"type\\\": \\\"OpenUrl\\\",\\\"param\\\": {\\\"url\\\": \\\"https://inwave.vn/\\\"}}\"}");
	}
	[MenuItem("Tools/Push/Open VIP Sub")]
	public static void DoOpenVIPSub()
	{
		Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"churn:409\",\"title\":\"This is title\",\"content\":\"This is content\", \"action_data\":\"{\\\"type\\\": \\\"OpenVipSub\\\",\\\"param\\\": {\\\"placement\\\": \\\"menu\\\"}}\"}");
	}
	// [MenuItem("Tools/Push/PlaySong")]
	// public static void DoOpenPush2()
	// {
	// 	Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"song_id:442\",\"title\":\"This is title\",\"content\":\"This is content\"}");
	// }
	//
	// [MenuItem("Tools/Push/Screen_BattleMode")]
	// public static void DoOpenPush1()
	// {
	// 	Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"screen:battle_mode\",\"title\":\"This is title\",\"content\":\"This is content\"}");
	// }
	// [MenuItem("Tools/Push/Screen_SongList")]
	// public static void DoOpenPush3()
	// {
	// 	Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"screen:songlist\",\"title\":\"This is title\",\"content\":\"This is content\"}");
	// }
	// [MenuItem("Tools/Push/Screen_Genres")]
	// public static void DoOpenPush4()
	// {
	// 	Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"screen:genres\",\"title\":\"This is title\",\"content\":\"This is content\"}");
	// }
	// [MenuItem("Tools/Push/Screen_Search")]
	// public static void DoOpenPush5()
	// {
	// 	Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"screen:search\",\"title\":\"This is title\",\"content\":\"This is content\"}");
	// }
	// [MenuItem("Tools/Push/Screen_Home")]
	// public static void DoOpenPush6()
	// {
	// 	Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"screen:home\",\"title\":\"This is title\",\"content\":\"This is content\"}");
	// }
	// [MenuItem("Tools/Push/Screen_Discover")]
	// public static void DoOpenPush7()
	// {
	// 	Instance.openFromPushCallBack(
	// 		"{\"type\":1,\"extra_info\":\"screen:discover\",\"title\":\"This is title\",\"content\":\"This is content\",\"action_data\":\"{\\\"type\\\": \\\"NAVIGATE\\\",\\\"param\\\": {\\\"path\\\": \\\"DISCOVERY/GENRE\\\"}}\"}");
	// }

	[MenuItem("Tools/Push/Popup_Offer")]
	public static void DoOpenPush8()
	{
		Instance.openFromPushCallBack("{\"type\":1,\"extra_info\":\"popup:offer\",\"title\":\"This is title\",\"content\":\"This is content\"}");
	}
	// [MenuItem("Tools/Push/Popup_RewardSong")]
	// public static void DoOpenPush9()
	// {
	// 	Instance.openFromPushCallBack("{\"notification_id\":" + 334 + ",\"type\":4,\"extra_info\":\"popup:rewardsong\"}");
	// }
#endif

	public void GMPush (int time)
	{
		if (!EnableLocalPush()) return;
		Unschedule(-1);
		DateTime timePush = DateTime.Now.AddMinutes (time);
		string pushTitle = "MagicTiles3";
		string content = "demo notify";
		UnityEngine.Debug.Log("tat game roi doi "+time+" phut nha may che");
		AddToPushList(-1, pushTitle, content, timePush);
	}

	public void GMPushSong(int songId)
	{
		Unschedule(-1);
		DateTime timePush = DateTime.Now.AddMinutes(1);
		string pushTitle = "Tiles Hop";
		string content = "Test noti with song: " + songId;
		UnityEngine.Debug.Log("-----> Added local push: " + timePush);
		AddToPushList(-1, pushTitle, content, timePush, songId.ToString());
	}

	public void GMPushExtraInfo(string extraInfo)
	{
		Unschedule(-1);
		DateTime timePush = DateTime.Now.AddMinutes(1);
		string pushTitle = "Tiles Hop";
		string content = "Test noti: " + extraInfo;
		string data = "{\"type\":1,\"extra_info\":\"" + extraInfo + "\",\"title\":\"This is title\",\"content\":\"This is content\"}";
		UnityEngine.Debug.Log("-----> Added local push: " + timePush + ", data: " + data);
		AddToPushList(-1, pushTitle, content, timePush, extraInfo);
	}

	public void GMPushAction(string action)
	{
		Unschedule(-1);
		DateTime timePush = DateTime.Now.AddMinutes(1);
		string pushTitle = "Tiles Hop";
		string content = "Test noti: " + action;
		string data = "{\"type\":1,\"action_data\":\"" + action + "\",\"title\":\"This is title\",\"content\":\"This is content\"}";
		UnityEngine.Debug.Log("-----> Added local push: " + timePush + ", data: " + data);
		AddToPushList(-1, pushTitle, content, timePush, action_data: action);
	}

	public void GMPushScreen(string screenName)
	{
		Unschedule(-1);
		DateTime timePush = DateTime.Now.AddMinutes(1);
		string pushTitle = "Tiles Hop";
		string content = "Test noti open screen: " + screenName;
		UnityEngine.Debug.Log("-----> Added local push: " + timePush);
		AddToPushList(-1, pushTitle, content, timePush, "screen:" + screenName);
	}
    #endregion
}