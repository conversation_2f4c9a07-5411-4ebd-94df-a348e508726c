// Decompiled with JetBrains decompiler
// Type: GameSavvy.PropertySearch.PropertySearchHelpWindow
// Assembly: PropertySearch, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: D9E32750-8AA0-42CC-BD69-6EC0DB6C7435
// Assembly location: D:\Projects\InWave\BeatJumper\Assets\Trungvt\Editor\GameSavvy\PropertySearch\PropertySearch.dll

using System;
using UnityEditor;
using UnityEngine;

namespace GameSavvy.PropertySearch {
    public class PropertySearchHelpWindow : EditorWindow {
        private const string _Message =
            "\r\n- All searches are str.contains\r\n\r\n- Simply start typing to search\r\n\r\n- Typing a dot('.') in the search string  sets a context search\r\n        - tra.pos => searches for a component that contains 'tra' and a property that contains 'pos' in their names\r\n        - cub.tra.pos => does the above but also searches 'cub' in the gameObject name\r\n\r\n- Typing 't:<TYPE>' would search in the type of the property\r\n        - t:int would search properties of type 'Int'\r\n        - t:float would search properties of type 'float'\r\n\r\n- Typing 'v:(value)' would search in the value or appointed asset of the property\r\n        - v:Bart would search properties that eitehr caontain 'bart' in their value\r\n                 or point to an asset with 'bart' in their names\r\n        - v:null would be an easy way of filtering references that have no object assinged to them yet\r\n\r\n- You can mix the search parameters\r\n        - cub.tra.t:int would search GameObject 'cub' Component 'tra' and properties in these that are int\r\n            * [Cube.Transform.{t:int}]\r\n        - cub.tra.v:bart would search GameObject 'cub' Component 'tra' and properties in these that contain 'bart' in their value\r\n            * [Cube.Transform.'Bart Simpson']\r\n            \r\n- Press the [!] button to Highlight ping the GameObject in the Hierarchy";

        [MenuItem("Tools/Property Search/Help")]
        public static void OpenHelpWindow() {
            PropertySearchHelpWindow instance = ScriptableObject.CreateInstance<PropertySearchHelpWindow>();
            ((EditorWindow) instance).titleContent.text = ("Property Search Help");
            ((EditorWindow) instance).ShowUtility();

        }

        private void OnGUI() {
            GUILayout.Label(
                "\r\n- All searches are str.contains\r\n\r\n- Simply start typing to search\r\n\r\n- Typing a dot('.') in the search string  sets a context search\r\n        - tra.pos => searches for a component that contains 'tra' and a property that contains 'pos' in their names\r\n        - cub.tra.pos => does the above but also searches 'cub' in the gameObject name\r\n\r\n- Typing 't:<TYPE>' would search in the type of the property\r\n        - t:int would search properties of type 'Int'\r\n        - t:float would search properties of type 'float'\r\n\r\n- Typing 'v:(value)' would search in the value or appointed asset of the property\r\n        - v:Bart would search properties that eitehr caontain 'bart' in their value\r\n                 or point to an asset with 'bart' in their names\r\n        - v:null would be an easy way of filtering references that have no object assinged to them yet\r\n\r\n- You can mix the search parameters\r\n        - cub.tra.t:int would search GameObject 'cub' Component 'tra' and properties in these that are int\r\n            * [Cube.Transform.{t:int}]\r\n        - cub.tra.v:bart would search GameObject 'cub' Component 'tra' and properties in these that contain 'bart' in their value\r\n            * [Cube.Transform.'Bart Simpson']\r\n            \r\n- Press the [!] button to Highlight ping the GameObject in the Hierarchy",
                Array.Empty<GUILayoutOption>());
        }
    }
}